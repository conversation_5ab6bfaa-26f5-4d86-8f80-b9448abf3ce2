# -*- encoding: utf-8 -*-
"""
@File   :local_parser.py
@Time   :2025/6/29
<AUTHOR>
"""
import os
from typing import Optional
from datetime import datetime
from loguru import logger

from app.core.db.manager import DBManager
from app.core.db.models.doc_reader_task import DocReaderTask
from app.core.llm.session.document_qa.doc_session import DocSessionManager


class LocalDocumentParser:
    """本地文档解析器（用于非PDF文档的异步处理）"""
    
    def __init__(self):
        pass
    
    def process_text_task(self, task_id: str) -> tuple[bool, str]:
        """
        处理TEXT类型的解析任务

        Args:
            task_id: 任务ID

        Returns:
            tuple[bool, str]: (处理是否成功, 错误消息)
        """
        try:
            # 原子性地获取并更新任务状态，防止重复处理
            with DBManager.get_mysql().session() as db:
                # 使用原子操作：只有当状态为pending时才更新为processing
                update_result = db.query(DocReaderTask).filter(
                    DocReaderTask.task_id == task_id,
                    DocReaderTask.status == "pending"
                ).update({
                    "status": "processing",
                    "updated_at": datetime.now()
                })

                if update_result == 0:
                    # 没有更新任何记录，说明任务不存在或状态不是pending
                    task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task_id).first()
                    if not task:
                        error_msg = f"任务不存在: task_id={task_id}"
                        logger.error(error_msg)
                        return False, error_msg
                    else:
                        logger.info(f"任务状态不是pending，跳过处理: task_id={task_id}, status={task.status}")
                        return True, ""

                db.commit()

                # 重新获取更新后的任务信息
                task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task_id).first()
            
            logger.info(f"开始处理本地文档解析任务: task_id={task_id}, filename={task.filename}")
            
            # 检查doc_id是否存在
            if not task.doc_id:
                raise Exception(f"任务的doc_id为空，请检查任务创建流程: task_id={task_id}")

            # 获取文档信息
            session_manager = DocSessionManager()
            file_info = session_manager.file_manager.get_file_info(task.doc_id, all_fields=True)

            if not file_info:
                raise Exception(f"文档信息不存在: doc_id={task.doc_id}")

            file_path = file_info.get("file_path")
            if not file_path or not os.path.isfile(file_path):
                raise Exception(f"文件不存在: {file_path}")

            # 使用现有的文档解析器解析文件
            content = self._parse_document(file_path)

            if not content:
                raise Exception("文档解析结果为空，可能是文档内容无法识别或文档已损坏")

            # 更新MongoDB中的文档内容和状态
            session_manager.file_manager.collection.update_one(
                {"doc_id": task.doc_id},
                {
                    "$set": {
                        "content": content,
                        "status": "extracted",
                        "char_count": len(content),
                        "updated_at": datetime.now()
                    }
                }
            )

            # 处理文档内容（分块）
            chunks = session_manager.file_manager.file_processor.process_content(
                content,
                task.filename,
                task.doc_id
            )

            # 更新文档状态为已处理
            session_manager.file_manager.collection.update_one(
                {"doc_id": task.doc_id},
                {
                    "$set": {
                        "status": "processed",
                        "chunk_count": len(chunks),
                        "updated_at": datetime.now()
                    }
                }
            )

            # 添加文档到会话
            session_manager.add_document_to_session(task.session_id, task.doc_id)
            
            chunks_count = len(chunks) if chunks else 0
            
            # 更新任务状态为完成
            with DBManager.get_mysql().session() as db:
                task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task_id).first()
                if task:
                    task.status = "completed"
                    task.chunks_count = chunks_count
                    task.completed_at = datetime.now()
                    task.updated_at = datetime.now()
                    db.commit()
            
            # 更新MongoDB中的文件状态
            session_manager.session_collection.update_one(
                {"session_id": task.session_id, "files.task_id": task_id},
                {"$set": {"files.$.file_status": "completed"}}
            )
            
            logger.info(f"✅ 本地文档解析任务完成: task_id={task_id}, chunks={chunks_count}")
            return True, ""
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ 本地文档解析任务失败: task_id={task_id}, error={error_msg}")

            # 更新任务状态为失败
            with DBManager.get_mysql().session() as db:
                task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task_id).first()
                if task:
                    task.status = "failed"
                    task.error_message = error_msg[:500]  # 限制错误消息长度
                    task.updated_at = datetime.now()
                    db.commit()

            # 更新MongoDB中的文件状态
            try:
                session_manager = DocSessionManager()
                session_manager.session_collection.update_one(
                    {"files.task_id": task_id},
                    {"$set": {"files.$.file_status": "failed"}}
                )
            except:
                pass

            return False, error_msg
    
    def _parse_document(self, file_path: str) -> str:
        """
        使用现有的文档解析器解析文档
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 解析后的文本内容
        """
        try:
            # 使用现有的文档解析器
            from app.core.doc_parser.registry import parse_file
            
            content = parse_file(file_path)
            
            if not content:
                logger.warning(f"文档解析结果为空: {file_path}")
                return ""
            
            logger.info(f"文档解析成功: {file_path}, content_length={len(content)}")
            return content
            
        except Exception as e:
            logger.error(f"文档解析失败: {file_path}, error={e}")
            raise Exception(f"文档解析失败: {e}")
    
    def get_pending_text_tasks(self) -> list:
        """获取待处理的TEXT类型任务"""
        with DBManager.get_mysql().session() as db:
            tasks = db.query(DocReaderTask).filter(
                DocReaderTask.parse_type == "TEXT",
                DocReaderTask.status == "pending"
            ).all()
            return tasks
