# -*- encoding: utf-8 -*-
"""
@File   :adapter.py
@Time   :2025/6/29
<AUTHOR>
"""
import io
import uuid
from typing import Optional, Dict, List
from datetime import datetime
from loguru import logger
import requests

from app.core.db.manager import DBManager
from app.core.db.models.doc_reader_task import DocReaderTask


class DocReaderAdapter:
    """文档解读专用适配器（兼容session_manager）"""

    def __init__(self, parser_host: str, api_key: str):
        self.parser_host = parser_host.rstrip("/")
        self.headers = {"Authorization": f"Bearer {api_key}"}

    def submit_batch_tasks(
        self,
        username: str,
        session_id: str,
        file_infos: List[Dict]
    ) -> Dict:
        """
        批量提交文档解读任务（真正的异步处理）

        Args:
            username: 用户名
            session_id: 会话ID
            file_infos: 文件信息列表 [{"temp_doc_id": "", "filename": "", "file_bytes": b"", "file_type": "", "file_size": 0}]

        Returns:
            Dict: {"batch_id": "", "task_ids": [], "success_count": 0, "failed_count": 0}
        """
        batch_id = str(uuid.uuid4())
        task_ids = []
        success_count = 0
        failed_count = 0

        logger.info(f"开始批量提交文档解读任务: batch_id={batch_id}, file_count={len(file_infos)}")

        # 创建批次记录到MongoDB
        self._create_batch_record(username, session_id, batch_id, file_infos)

        for file_info in file_infos:
            try:
                task_id = self._create_async_task(
                    username=username,
                    session_id=session_id,
                    batch_id=batch_id,
                    **file_info
                )

                if task_id:
                    task_ids.append(task_id)
                    success_count += 1
                else:
                    failed_count += 1

            except Exception as e:
                logger.error(f"创建异步任务失败: {file_info.get('filename')}, error={e}")
                failed_count += 1

        logger.info(f"批量任务创建完成: batch_id={batch_id}, success={success_count}, failed={failed_count}")

        return {
            "batch_id": batch_id,
            "task_ids": task_ids,
            "success_count": success_count,
            "failed_count": failed_count
        }

    def _create_batch_record(self, username: str, session_id: str, batch_id: str, file_infos: List[Dict]):
        """创建批次记录到MongoDB"""
        try:
            from app.core.llm.session.document_qa.doc_session import DocSessionManager

            session_manager = DocSessionManager()

            # 计算主要文件扩展名
            ext_names = [f["file_type"] for f in file_infos]
            main_ext = max(set(ext_names), key=ext_names.count) if ext_names else ""

            # 创建批次记录
            batch_record = {
                "batch_id": batch_id,
                "file_count": len(file_infos),
                "upload_time": datetime.now().isoformat(),
                "defined_name": None,  # 用户可以后续重命名
                "is_pinned": False,
                "main_ext": main_ext,
                "status": "pending"
            }

            # 创建文件记录（暂时没有真实的doc_id和wps_url）
            batch_files = []
            for file_info in file_infos:
                parse_type = "OCR" if file_info["file_type"] == "pdf" else "TEXT"

                file_record = {
                    "doc_id": None,  # 异步任务中会更新
                    "temp_doc_id": file_info["temp_doc_id"],
                    "wps_url": "",  # 异步任务中会更新
                    "filename": file_info["filename"],
                    "file_type": file_info["file_type"],
                    "file_size": file_info["file_size"],
                    "task_id": None,  # 异步任务中会更新
                    "batch_id": batch_id,
                    "file_status": "pending",
                    "parse_type": parse_type,
                    "upload_time": datetime.now().isoformat()
                }
                batch_files.append(file_record)

            # 更新session记录
            session_manager.session_collection.update_one(
                {"session_id": session_id},
                {
                    "$push": {
                        "files": {"$each": batch_files},
                        "batches": batch_record
                    }
                }
            )

            logger.info(f"批次记录已创建: batch_id={batch_id}, file_count={len(file_infos)}")

        except Exception as e:
            logger.error(f"创建批次记录失败: batch_id={batch_id}, error={e}")

    def _create_async_task(
        self,
        username: str,
        session_id: str,
        batch_id: str,
        temp_doc_id: str,
        filename: str,
        file_bytes: bytes,
        file_type: str,
        file_size: int
    ) -> Optional[str]:
        """
        创建异步任务（不阻塞，只创建任务记录）
        """
        task_id = str(uuid.uuid4())
        parse_type = "OCR" if file_type.lower() == "pdf" else "TEXT"

        try:
            # 将文件保存到临时目录，避免在数据库中存储大量数据
            import os
            from app.configs.document_config import doc_settings

            # 创建临时文件目录
            temp_dir = os.path.join(doc_settings.default_upload_dir, "doc_reader_temp")
            os.makedirs(temp_dir, exist_ok=True)

            # 生成临时文件路径
            file_ext = os.path.splitext(filename)[1]
            temp_filename = f"{task_id}{file_ext}"
            temp_file_path = os.path.join(temp_dir, temp_filename)

            # 保存文件到临时目录
            with open(temp_file_path, 'wb') as f:
                f.write(file_bytes)

            logger.info(f"文件已保存到临时目录: {temp_file_path}")

            metadata = {
                "temp_file_path": temp_file_path,
                "file_size": file_size,
                "temp_doc_id": temp_doc_id
            }
            # 获取文件信息并生成WPS预览URL

            file_path = temp_file_path

            wps_url = ""
            if file_path and os.path.isfile(file_path):
                try:
                    from wps_callback_kit import wps
                    wps_url = wps.run(str(uuid.uuid4()), file_path, username)
                except Exception as wps_error:
                    logger.warning(f"获取预览地址失败: {filename}, error={wps_error}")
            # 创建任务记录
            with DBManager.get_mysql().session() as db:
                task = DocReaderTask(
                    task_id=task_id,
                    username=username,
                    session_id=session_id,
                    doc_id="",  # 异步任务中会创建真实的doc_id
                    batch_id=batch_id,
                    filename=filename,
                    file_type=file_type,
                    parse_type=parse_type,
                    status="pending",
                    meta_info=metadata,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )
                db.add(task)
                db.commit()

            # 更新MongoDB中的task_id
            from app.core.llm.session.document_qa.doc_session import DocSessionManager
            session_manager = DocSessionManager()
            session_manager.session_collection.update_one(
                {"session_id": session_id, "files.temp_doc_id": temp_doc_id},
                {"$set": {"files.$.task_id": task_id,
                          "files.$.wps_url": wps_url}}
            )

            logger.info(f"异步任务已创建: task_id={task_id}, parse_type={parse_type}")
            return task_id

        except Exception as e:
            logger.error(f"创建异步任务失败: {filename}, error={e}")
            return None

    def _submit_ocr_task(
        self, task_id: str, username: str, session_id: str, batch_id: str,
        doc_id: str, filename: str, file_bytes: bytes, file_type: str
    ) -> Optional[str]:
        """提交OCR任务到杏仁解析中心"""
        url = f"{self.parser_host}/api/v1/document/upload"
        files = {
            "files": (filename, io.BytesIO(file_bytes), "application/octet-stream")
        }
        data = {
            "service_type": "document_reader"
        }

        response = requests.post(url, files=files, data=data, headers=self.headers, timeout=60)
        response.raise_for_status()
        parser_result = response.json()

        if parser_result.get("success"):
            almond_batch_id = parser_result.get("batch_id")
            parser_doc_id = parser_result["uploaded_files"][0]["document_id"]

            # 创建OCR任务记录
            with DBManager.get_mysql().session() as db:
                task = DocReaderTask(
                    task_id=task_id,
                    username=username,
                    session_id=session_id,
                    doc_id=doc_id,
                    batch_id=batch_id,
                    filename=filename,
                    file_type=file_type,
                    parse_type="OCR",
                    parser_doc_id=parser_doc_id,
                    almond_batch_id=almond_batch_id,
                    status="pending",
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )
                db.add(task)
                db.commit()

            logger.info(f"OCR任务已创建: task_id={task_id}, parser_doc_id={parser_doc_id}")
            return task_id
        else:
            raise Exception(f"杏仁解析中心返回失败: {parser_result}")

    def _submit_text_task(
        self, task_id: str, username: str, session_id: str, batch_id: str,
        doc_id: str, filename: str, file_type: str
    ) -> Optional[str]:
        """创建本地TEXT解析任务"""
        with DBManager.get_mysql().session() as db:
            task = DocReaderTask(
                task_id=task_id,
                username=username,
                session_id=session_id,
                doc_id=doc_id,
                batch_id=batch_id,
                filename=filename,
                file_type=file_type,
                parse_type="TEXT",
                status="pending",
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
            db.add(task)
            db.commit()

        logger.info(f"TEXT任务已创建: task_id={task_id}")
        return task_id

    def get_doc_reader_result(self, parser_doc_id: str) -> Optional[Dict]:
        """
        获取文档解读结果

        Args:
            parser_doc_id: 杏仁解析器返回的文档ID

        Returns:
            Dict: 解析结果
        """
        url = f"{self.parser_host}/api/v1/document/{parser_doc_id}/result/"
        params = {"content_type": "json"}

        try:
            response = requests.get(url, params=params, headers=self.headers, timeout=30)

            if response.status_code == 200:
                result = response.json().get('result', {})
                markdown_text = result.get("markdown_text", "")
                return {
                    "success": True,
                    "markdown_text": markdown_text,
                    "metadata": result.get("metadata", {})
                }
            elif response.status_code == 400:
                logger.info(f'文档解读任务处理中: parser_doc_id={parser_doc_id}')
                return {"success": False, "error": "pending"}
            else:
                try:
                    error_msg = response.json().get("message", None)
                    if not error_msg:
                        error_msg = response.json().get("detail", "")
                except Exception:
                    error_msg = response.text
                logger.error(f"获取文档解读结果失败: {response.status_code} - {error_msg}")
                return {"success": False, "error": error_msg}

        except Exception as e:
            logger.error(f"获取文档解读结果异常: {e}")
            return {"success": False, "error": "获取文档解读结果异常，请稍后再试"}

    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            Dict: 任务状态信息
        """
        with DBManager.get_mysql().session() as db:
            task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task_id).first()
            if not task:
                return None

            return {
                "task_id": task.task_id,
                "status": task.status,
                "filename": task.filename,
                "file_type": task.file_type,
                "chunks_count": task.chunks_count,
                "error_message": task.error_message,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "has_content": bool(task.markdown_content)
            }

    def update_task_status(self, task_id: str, status: str, **kwargs):
        """更新任务状态"""
        with DBManager.get_mysql().session() as db:
            task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task_id).first()
            if task:
                task.status = status
                task.updated_at = datetime.now()

                # 更新其他字段
                for key, value in kwargs.items():
                    if hasattr(task, key):
                        setattr(task, key, value)

                if status == "completed":
                    task.completed_at = datetime.now()

                db.commit()
                logger.info(f"文档解读任务状态已更新: task_id={task_id}, status={status}")
            else:
                logger.warning(f"未找到文档解读任务: task_id={task_id}")

    def _create_failed_task(
        self, task_id: str, username: str, session_id: str, batch_id: str,
        doc_id: str, filename: str, file_type: str, parse_type: str, error_message: str
    ):
        """创建失败的任务记录"""
        with DBManager.get_mysql().session() as db:
            task = DocReaderTask(
                task_id=task_id,
                username=username,
                session_id=session_id,
                doc_id=doc_id,
                batch_id=batch_id,
                filename=filename,
                file_type=file_type,
                parse_type=parse_type,
                status="failed",
                error_message=error_message,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
            db.add(task)
            db.commit()

    def get_batch_status(self, batch_id: str) -> Optional[Dict]:
        """
        获取批次状态

        Args:
            batch_id: 批次ID

        Returns:
            Dict: 批次状态信息
        """
        with DBManager.get_mysql().session() as db:
            tasks = db.query(DocReaderTask).filter(DocReaderTask.batch_id == batch_id).all()
            if not tasks:
                return None

            task_statuses = []
            for task in tasks:
                task_statuses.append({
                    "task_id": task.task_id,
                    "filename": task.filename,
                    "status": task.status,
                    "parse_type": task.parse_type,
                    "error_message": task.error_message,
                    "chunks_count": task.chunks_count,
                })

            # 计算整体状态
            statuses = [task.status for task in tasks]
            if all(s == "completed" for s in statuses):
                overall_status = "completed"
            elif any(s == "failed" for s in statuses):
                overall_status = "mixed" if any(s == "completed" for s in statuses) else "failed"
            elif any(s == "processing" for s in statuses):
                overall_status = "processing"
            else:
                overall_status = "pending"

            return {
                "batch_id": batch_id,
                "overall_status": overall_status,
                "tasks": task_statuses
            }

    def get_doc_reader_result(self, parser_doc_id: str) -> Optional[Dict]:
        """
        获取文档解读结果

        Args:
            parser_doc_id: 杏仁解析器返回的文档ID

        Returns:
            Dict: 解析结果
        """
        url = f"{self.parser_host}/api/v1/document/{parser_doc_id}/result/"
        params = {"content_type": "json"}

        try:
            response = requests.get(url, params=params, headers=self.headers, timeout=30)

            if response.status_code == 200:
                result = response.json().get('result', {})
                markdown_text = result.get("markdown_text", "")
                return {
                    "success": True,
                    "markdown_text": markdown_text,
                    "metadata": result.get("metadata", {})
                }
            elif response.status_code == 400:
                logger.info(f'文档解读任务处理中: parser_doc_id={parser_doc_id}')
                return {"success": False, "error": "pending"}
            else:
                logger.error(f"获取文档解读结果失败: {response.status_code} - {response.text}")
                return {"success": False, "error": response.text}

        except Exception as e:
            logger.error(f"获取文档解读结果异常: {e}")
            return {"success": False, "error": str(e)}

    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            Dict: 任务状态信息
        """
        with DBManager.get_mysql().session() as db:
            task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task_id).first()
            if not task:
                return None

            return {
                "task_id": task.task_id,
                "status": task.status,
                "filename": task.filename,
                "file_type": task.file_type,
                "parse_type": task.parse_type,
                "chunks_count": task.chunks_count,
                "error_message": task.error_message,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            }

    def update_task_status(self, task_id: str, status: str, **kwargs):
        """更新任务状态"""
        with DBManager.get_mysql().session() as db:
            task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task_id).first()
            if task:
                task.status = status
                task.updated_at = datetime.now()

                # 更新其他字段
                for key, value in kwargs.items():
                    if hasattr(task, key):
                        setattr(task, key, value)

                if status == "completed":
                    task.completed_at = datetime.now()

                db.commit()
                logger.info(f"文档解读任务状态已更新: task_id={task_id}, status={status}")
            else:
                logger.warning(f"未找到文档解读任务: task_id={task_id}")
