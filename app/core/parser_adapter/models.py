# -*- encoding: utf-8 -*-
"""
@File   :models.py
@Time   :2025/6/16 15:12
<AUTHOR>
"""
from sqlalchemy import Column, Integer, String, Text, DateTime
from app.core.db import Base  # 假设你已有 Base 类
from datetime import datetime


class ParseTask(Base):
    __tablename__ = "parse_tasks"

    id = Column(Integer, primary_key=True, autoincrement=True)
    file_path = Column(Text, nullable=False)
    batch_id = Column(String(100), nullable=False)
    doc_id = Column(String(100), nullable=True)
    status = Column(String(20), default="pending")  # pending | done | error
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
