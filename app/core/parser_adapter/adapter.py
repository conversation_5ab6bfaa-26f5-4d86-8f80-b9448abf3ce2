# -*- encoding: utf-8 -*-
"""
@File   :adapter.py
@Time   :2025/6/16 17:30
<AUTHOR>
"""
import io
import os
import requests
from loguru import logger
from typing import Optional, Dict
from app.core.db.manager import DBManager
from app.core.db.models.parse import ParseTask
from datetime import datetime


class ParserAdapter:
    def __init__(self, parser_host: str, api_key: str):
        self.parser_host = parser_host.rstrip("/")
        self.headers = {"Authorization": f"Bearer {api_key}"}

    def submit_parse_task(
            self,
            username,
            filename: str,
            file_bytes: bytes,
            maxkb_url: str,
            maxkb_id: str,
            almond_parser_id: str,
    ) -> Optional[ParseTask]:
        url = f"{self.parser_host}/api/v1/document/upload"
        # 构造 file-like 对象，附带文件名和 MIME 类型
        files = {
            "files": (filename, io.BytesIO(file_bytes), "application/octet-stream")
        }
        data = {
            "service_type": "document"
        }
        try:
            response = requests.post(url, files=files, data=data, headers=self.headers, timeout=60)
            response.raise_for_status()
            parser_results = response.json()
            logger.info(f"解析中心返回响应：{parser_results}")
            if parser_results["success"]:
                batch_id = parser_results.get("batch_id")
                uploaded_files = parser_results["uploaded_files"]
                if uploaded_files:
                    document_id = uploaded_files[0]["document_id"]
                    with DBManager.get_mysql().session() as db:
                        task = ParseTask(
                            username=username,
                            file_name=filename,
                            file_path="",
                            batch_id=batch_id,
                            doc_id=document_id,
                            almond_parser_id=almond_parser_id,
                            status="pending",
                            maxkb_url=maxkb_url,
                            maxkb_id=maxkb_id,
                            created_at=datetime.now(),
                            updated_at=datetime.now(),
                        )
                        db.add(task)
                        logger.info(f"已创建解析任务: batch_id={batch_id}, doc_id={document_id}")
                        return task
                else:
                    logger.error(f"解析失败: {response.text}")
                    with DBManager.get_mysql().session() as db:
                        task = ParseTask(
                            username=username,
                            file_name=filename,
                            file_path="",
                            batch_id="",
                            doc_id="",
                            status="error",
                            maxkb_url=maxkb_url,
                            maxkb_id=maxkb_id,
                            almond_parser_id=almond_parser_id,
                            error_message=str(parser_results["failed_files"]),
                            created_at=datetime.now(),
                            updated_at=datetime.now(),
                        )
                        db.add(task)
            else:

                logger.error(f"解析失败: {response.text}")
                with DBManager.get_mysql().session() as db:
                    task = ParseTask(
                        username=username,
                        file_name=filename,
                        file_path="",
                        batch_id="",
                        doc_id="",
                        status="error",
                        maxkb_url=maxkb_url,
                        maxkb_id=maxkb_id,
                        almond_parser_id=almond_parser_id,
                        error_message=parser_results["reason"],
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                    )
                    db.add(task)

        except Exception as e:
            logger.error(f"提交解析任务失败: {e}")
            with DBManager.get_mysql().session() as db:
                task = ParseTask(
                    username=username,
                    file_name=filename,
                    file_path="",
                    batch_id="",
                    doc_id="",
                    status="error",
                    almond_parser_id=almond_parser_id,
                    maxkb_url=maxkb_url,
                    maxkb_id=maxkb_id,
                    error_message=str(e),
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )
                db.add(task)
            return None

    def get_parse_result(
            self, document_id: str, content_type: str = "file"
    ) -> Optional[Dict]:
        url = f"{self.parser_host}/api/v1/document/{document_id}/result/"
        params = {"content_type": content_type}

        try:
            response = requests.get(
                url, params=params, headers=self.headers, timeout=30
            )
            if response.status_code == 200:
                markdown_text = response.json().get('result', {}).get("markdown_text", "")
                return {"success": True, "markdown_text": markdown_text}
            elif response.status_code == 400:
                logger.info(f'================ 解析中 (pending) ==================')
                return {"success": False, "error": "pending"}
            else:
                logger.error(f"获取解析结果失败: {response.status_code} - {response.text}")
                return {"success": False, "error": response.text}

        except Exception as e:
            logger.error(f"获取解析结果失败: {e}")
            return {"success": False, "error": str(e)}

    def update_task_status(self, doc_id: str, new_status: str):
        with DBManager.get_mysql().session() as db:
            task = db.query(ParseTask).filter(ParseTask.doc_id == doc_id).first()
            if task:
                task.status = new_status
                task.updated_at = datetime.now()
                logger.info(f"解析任务状态已更新: doc_id={doc_id}, status={new_status}")
            else:
                logger.warning(f"未找到对应解析任务: doc_id={doc_id}")


if __name__ == "__main__":
    from app.configs.config import settings

    print(settings.get_mysql_config)
    DBManager.initialize(mysql_config=settings.get_mysql_config)
    p = ParserAdapter(settings.parser_base_url, settings.parser_api_key)
    # print(p.get_parse_result("541ca330c4804fc796e7aacd9e8c4d52"))
    with open(r"C:\Users\<USER>\Documents\立信VPN使用手册.pdf", 'rb')as f:
        file_bytes = f.read()

    res = p.submit_parse_task("test", "立信VPN使用手册.pdf", file_bytes,
                              "http://172.17.10.72:8891",
                              "99cb3b90-532f-11f0-a3eb-02b02c9946ab",
                              "123")
    # print(res)
    # print(p.get_parse_task("5ad8cabc126241fe9b656317ff19edd1").status)
