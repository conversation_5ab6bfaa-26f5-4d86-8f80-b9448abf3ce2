"""
@File    :base.py
@Time    :2024/3/26
<AUTHOR>
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Optional


class BaseVectorStore(ABC):
    """向量存储基类"""

    def __init__(self, dimension: int, top_k: int = 5):
        """初始化

        Args:
            dimension: 向量维度
            top_k: 返回最相似的前k个结果
        """
        self.dimension = dimension
        self.top_k = top_k

    @abstractmethod
    async def add(self, doc_id: str, vectors: List[List[float]], metadata: List[Dict]) -> bool:
        """添加向量

        Args:
            doc_id: 文档ID
            vectors: 向量列表
            metadata: 元数据列表

        Returns:
            bool: 是否添加成功
        """
        pass

    @abstractmethod
    async def search(self, query_vector: List[float], doc_ids: Optional[List[str]] = None) -> List[Dict]:
        """搜索向量

        Args:
            query_vector: 查询向量
            doc_ids: 限定搜索的文档ID列表

        Returns:
            List[Dict]: 搜索结果列表
        """
        pass

    @abstractmethod
    async def delete(self, doc_id: str) -> bool:
        """删除向量

        Args:
            doc_id: 文档ID

        Returns:
            bool: 是否删除成功
        """
        pass
