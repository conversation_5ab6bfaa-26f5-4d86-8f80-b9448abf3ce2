"""
@File    :config.py
@Time    :2024/3/26
<AUTHOR>
"""

from typing import Dict, Optional

class VectorConfig:
    """向量化配置"""
    
    def __init__(
        self,
        embedding_config: Optional[Dict] = None,
        vector_store: str = "faiss",
        faiss_index_folder: str = "faiss_indexes",
        top_k: int = 5
    ):
        # 嵌入配置
        self.embedding_config = embedding_config or {
            "engine": "local",
            "model": "bge-m3:latest",
            "api_url": "",
            "api_key": "",
            "batch_size": 8,
            "dimension": 1024
        }
        
        # 向量存储配置
        self.vector_store = vector_store.lower()
        self.faiss_index_folder = faiss_index_folder
        self.top_k = top_k 