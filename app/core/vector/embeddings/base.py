"""
@File    :base.py
@Time    :2024/3/26
<AUTHOR>
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Optional

class BaseEmbedding(ABC):
    """嵌入基类"""
    
    def __init__(self, config: Dict):
        """初始化
        
        Args:
            config: 配置信息
        """
        self.config = config
        self.dimension = config.get("dimension", 1024)
        self.batch_size = config.get("batch_size", 8)
        
    @abstractmethod
    async def encode(self, texts: List[str]) -> List[List[float]]:
        """文本编码
        
        Args:
            texts: 文本列表
            
        Returns:
            List[List[float]]: 向量列表
        """
        pass 