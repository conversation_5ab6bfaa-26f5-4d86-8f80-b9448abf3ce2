"""
@File    :manager.py
@Time    :2024/3/26
<AUTHOR>
"""

import os
from typing import List, Dict, Optional, Type
from loguru import logger

from .config import VectorConfig
from .embeddings.base import BaseEmbedding
from .storage.base import BaseVectorStore


class VectorManager:
    """向量管理器"""
    
    def __init__(
        self,
        config: Optional[VectorConfig] = None,
        embedding_cls: Optional[Type[BaseEmbedding]] = None,
        vector_store_cls: Optional[Type[BaseVectorStore]] = None
    ):
        """初始化
        
        Args:
            config: 向量化配置
            embedding_cls: 嵌入实现类
            vector_store_cls: 向量存储实现类
        """
        self.config = config or VectorConfig()
        
        # 初始化嵌入模型
        if embedding_cls:
            self.embedding = embedding_cls(self.config.embedding_config)
        else:
            # 根据配置动态导入嵌入实现
            from .embeddings.local import LocalEmbedding
            self.embedding = LocalEmbedding(self.config.embedding_config)
            
        # 初始化向量存储
        if vector_store_cls:
            self.vector_store = vector_store_cls(
                dimension=self.config.embedding_config["dimension"],
                top_k=self.config.top_k
            )
        else:
            # 根据配置动态导入向量存储实现
            if self.config.vector_store == "faiss":
                from .storage.faiss import FaissVectorStore
                self.vector_store = FaissVectorStore(
                    dimension=self.config.embedding_config["dimension"],
                    top_k=self.config.top_k,
                    index_folder=self.config.faiss_index_folder
                )
            elif self.config.vector_store == "milvus":
                from .storage.milvus import MilvusVectorStore
                self.vector_store = MilvusVectorStore(
                    dimension=self.config.embedding_config["dimension"],
                    top_k=self.config.top_k
                )
            else:
                raise ValueError(f"不支持的向量存储类型: {self.config.vector_store}")
                
    async def create_index(self, doc_id: str, texts: List[str], metadata: List[Dict]) -> bool:
        """创建向量索引
        
        Args:
            doc_id: 文档ID
            texts: 文本列表
            metadata: 元数据列表
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 文本向量化
            vectors = await self.embedding.encode(texts)
            
            # 创建索引
            return await self.vector_store.add(doc_id, vectors, metadata)
        except Exception as e:
            logger.error(f"创建向量索引失败: {e}")
            return False
            
    async def search(self, query: str, doc_ids: Optional[List[str]] = None) -> List[Dict]:
        """搜索向量
        
        Args:
            query: 查询文本
            doc_ids: 限定搜索的文档ID列表
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        try:
            # 查询文本向量化
            query_vector = await self.embedding.encode([query])[0]
            
            # 搜索向量
            return await self.vector_store.search(query_vector, doc_ids)
        except Exception as e:
            logger.error(f"搜索向量失败: {e}")
            return []
            
    async def delete_index(self, doc_id: str) -> bool:
        """删除向量索引
        
        Args:
            doc_id: 文档ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            return await self.vector_store.delete(doc_id)
        except Exception as e:
            logger.error(f"删除向量索引失败: {e}")
            return False 