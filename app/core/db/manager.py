# -*- encoding: utf-8 -*-
"""
@File    :manager.py
@Time    :2024/3/26 16:45
<AUTHOR>
"""

from typing import Optional

from sqlalchemy import text

from app.core.db.mongo import MongoClient
from app.core.db.mysql import MySQLClient
from app.core.db.redis import RedisClient


class DBManager:
    """数据库管理器"""

    _instance = None
    _mysql: Optional[MySQLClient] = None
    _redis: Optional[RedisClient] = None
    _mongo: Optional[MongoClient] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def initialize(
        cls,
        mysql_config: Optional[dict] = None,
        redis_config: Optional[dict] = None,
        mongo_config: Optional[dict] = None,
    ):
        """初始化数据库连接

        Args:
            mysql_config: MySQL配置
            redis_config: Redis配置
            mongo_config: MongoDB配置
        """
        if mysql_config:
            mysql = MySQLClient()
            mysql.initialize(**mysql_config)
            if not mysql.ping():
                raise RuntimeError("MySQL连接失败")
            cls._mysql = mysql

        if mongo_config:
            mongo = MongoClient()
            mongo.initialize(**mongo_config)
            if not mongo.ping():
                raise RuntimeError("MongoDB连接失败")
            cls._mongo = mongo

        if redis_config:
            redis = RedisClient()
            redis.initialize(**redis_config)
            if not redis.ping():
                raise RuntimeError("Redis连接失败")
            cls._redis = redis



    @classmethod
    def close(cls):
        """关闭所有数据库连接"""
        if cls._mysql:
            cls._mysql.close()
            cls._mysql = None

        if cls._redis:
            cls._redis.close()
            cls._redis = None

        if cls._mongo:
            cls._mongo.close()
            cls._mongo = None

    @classmethod
    def get_mysql(cls) -> MySQLClient:
        """获取MySQL客户端实例"""
        if not cls._mysql:
            raise RuntimeError("MySQL客户端未初始化")
        return cls._mysql

    @classmethod
    def get_redis(cls) -> RedisClient:
        """获取Redis客户端实例"""
        if not cls._redis:
            raise RuntimeError("Redis客户端未初始化")
        return cls._redis

    @classmethod
    def get_mongo(cls) -> MongoClient:
        """获取MongoDB客户端实例"""
        if not cls._mongo:
            raise RuntimeError("MongoDB客户端未初始化")
        return cls._mongo
