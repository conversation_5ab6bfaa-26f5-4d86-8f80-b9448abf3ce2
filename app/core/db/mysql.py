# -*- encoding: utf-8 -*-
"""
@File    :mysql.py
@Time    :2024/3/26 16:45
<AUTHOR>
"""

import threading
from sqlalchemy import text
from typing import Optional, Dict, Any
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError
from contextlib import contextmanager
from loguru import logger
from urllib.parse import quote_plus

from app.core.db.base import DBClientBase, ConnectionPoolBase


class MySQLPool(ConnectionPoolBase):
    """MySQL连接池实现"""

    def __init__(
        self,
        host: str,
        port: int,
        user: str,
        password: str,
        database: str,
        pool_size: int = 10,
        max_overflow: int = 20,
        pool_timeout: int = 30,
        pool_recycle: int = 3600,
        **kwargs,
    ):
        super().__init__(pool_size=pool_size)
        self.db_url = f"mysql+pymysql://{user}:{quote_plus(password)}@{host}:{port}/{database}"
        self.engine_kwargs = {
            "pool_size": pool_size,
            "max_overflow": max_overflow,
            "pool_timeout": pool_timeout,
            "pool_recycle": pool_recycle,
            **kwargs,
        }
        self._engine = None
        self._session_factory = None

    def create_pool(self):
        """创建SQLAlchemy引擎和会话工厂"""
        if not self._engine:
            self._engine = create_engine(
                self.db_url, poolclass=QueuePool, **self.engine_kwargs
            )
            self._session_factory = scoped_session(
                sessionmaker(bind=self._engine, expire_on_commit=False)
            )

    def close_pool(self):
        """关闭连接池"""
        if self._engine:
            self._engine.dispose()
            self._engine = None
            self._session_factory = None

    def acquire(self):
        """获取数据库会话"""
        if not self._session_factory:
            raise RuntimeError("连接池未初始化")
        return self._session_factory()

    def release(self, session: Any):
        """释放数据库会话"""
        if session:
            session.close()


class MySQLClient(DBClientBase):
    """MySQL客户端实现"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, "_initialized"):
            super().__init__()
            self._pool = None
            self._initialized = False

    def initialize(
        self,
        host: str,
        port: int,
        user: str,
        password: str,
        database: str,
        pool_size: int = 10,
        **kwargs,
    ):
        """初始化MySQL连接池

        Args:
            host: 主机地址
            port: 端口号
            user: 用户名
            password: 密码
            database: 数据库名
            pool_size: 连接池大小
            **kwargs: 其他连接参数
        """
        if self._initialized:
            return

        self._pool = MySQLPool(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            pool_size=pool_size,
            **kwargs,
        )
        self._pool.create_pool()
        self._initialized = True
        logger.info("MySQL连接池初始化成功")

    def close(self):
        """关闭MySQL连接"""
        if self._pool:
            self._pool.close_pool()
            self._initialized = False
            logger.info("MySQL连接池已关闭")

    @property
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._initialized and self._pool is not None

    @contextmanager
    def session(self):
        """获取数据库会话的上下文管理器"""
        self.ensure_initialized()
        session = None
        try:
            session = self._pool.acquire()
            yield session
            session.commit()
        except SQLAlchemyError as e:
            if session:
                session.rollback()
            logger.error(f"数据库操作错误: {str(e)}")
            raise
        finally:
            if session:
                self._pool.release(session)

    def get_session(self):
        """获取数据库会话（不使用上下文管理器）"""
        self.ensure_initialized()
        return self._pool.acquire()

    def ping(self) -> bool:
        try:
            with self.get_session() as s:
                s.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"MySQL ping失败: {e}")
            return False


if __name__ == "__main__":

    mysql = MySQLClient()
    mysql.initialize(
        '172.16.6.137',
        5188,
        'dev',
        "dev@2022",
        'bdo_xxr2'
    )
    print(mysql.ping())
