# -*- encoding: utf-8 -*-
"""
@File    :base.py
@Time    :2024/3/26 16:45
<AUTHOR>
"""

from abc import ABC, abstractmethod
from typing import Optional, Any, Dict
from contextlib import contextmanager
import threading
from sqlalchemy.orm import declarative_base

# 全局模型基类
Base = declarative_base()


class DBClientBase(ABC):
    """数据库客户端基类"""

    def __init__(self):
        self._local = threading.local()
        self._initialized = False

    @abstractmethod
    def initialize(self, **kwargs):
        """初始化连接"""
        pass

    @abstractmethod
    def close(self):
        """关闭连接"""
        pass

    @property
    @abstractmethod
    def is_connected(self) -> bool:
        """检查连接状态"""
        pass

    def ensure_initialized(self):
        """确保已初始化"""
        if not self._initialized:
            raise RuntimeError("数据库客户端未初始化")


class ConnectionPoolBase(ABC):
    """连接池基类"""

    def __init__(self, pool_size: int = 10, **kwargs):
        self.pool_size = pool_size
        self.pool_args = kwargs
        self._pool = None

    @abstractmethod
    def create_pool(self):
        """创建连接池"""
        pass

    @abstractmethod
    def close_pool(self):
        """关闭连接池"""
        pass

    @contextmanager
    def get_connection(self):
        """获取连接的上下文管理器"""
        conn = None
        try:
            conn = self.acquire()
            yield conn
        finally:
            if conn:
                self.release(conn)

    @abstractmethod
    def acquire(self):
        """获取连接"""
        pass

    @abstractmethod
    def release(self, conn: Any):
        """释放连接"""
        pass
