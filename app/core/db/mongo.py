# -*- encoding: utf-8 -*-
"""
@File    :mongo.py
@Time    :2024/3/26 16:45
<AUTHOR>
"""

from typing import Optional, Any, Dict, List, Type
from pymongo import MongoClient as PyMongoClient
from pymongo.errors import PyMongoError
from pymongo.database import Database
from contextlib import contextmanager
import threading
from loguru import logger

from app.core.db.base import ConnectionPoolBase, DBClientBase


class MongoPool(ConnectionPoolBase):
    """MongoDB同步连接池实现"""

    def __init__(
            self,
            host: str,
            port: int,
            username: Optional[str] = None,
            password: Optional[str] = None,
            database: str = "test",
            auth_source: str = "admin",
            pool_size: int = 10,
            **kwargs,
    ):
        super().__init__(pool_size=pool_size)
        if username and password:
            self.uri = f"mongodb://{username}:{password}@{host}:{port}/{database}?authSource={auth_source}"
        else:
            self.uri = f"mongodb://{host}:{port}/{database}"

        self.database = database
        self.connection_kwargs = {
            "maxPoolSize": pool_size,
            **kwargs,
        }
        self._client: Optional[PyMongoClient] = None
        self._db: Optional[Database] = None

    def create_pool(self):
        if not self._client:
            self._client = PyMongoClient(self.uri, **self.connection_kwargs)
            self._db = self._client[self.database]

    def close_pool(self):
        if self._client:
            self._client.close()
            self._client = None
            self._db = None

    def acquire(self):
        if self._db is None:
            raise RuntimeError("连接池未初始化")
        return self._db

    def release(self, conn: Any):
        pass  # pymongo 自动管理连接


class MongoClient(DBClientBase):
    """MongoDB同步客户端"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, "_initialized"):
            super().__init__()
            self._pool: Optional[MongoPool] = None
            self._initialized = False

    def initialize(
            self,
            host: str,
            port: int,
            username: Optional[str] = None,
            password: Optional[str] = None,
            database: str = "test",
            auth_source: str = "admin",
            pool_size: int = 10,
            **kwargs,
    ):
        if self._initialized:
            return

        self._pool = MongoPool(
            host=host,
            port=port,
            username=username,
            password=password,
            database=database,
            auth_source=auth_source,
            pool_size=pool_size,
            **kwargs,
        )
        self._pool.create_pool()
        self._initialized = True
        logger.info("MongoDB连接池初始化成功")

    def close(self):
        if self._pool:
            self._pool.close_pool()
            self._initialized = False
            logger.info("MongoDB连接池已关闭")

    @property
    def is_connected(self) -> bool:
        return self._initialized and self._pool is not None

    @property
    def db(self):
        self.ensure_initialized()
        return self._pool.acquire()

    def get_collection(self, name: str):
        return self.db[name]

    @contextmanager
    def transaction(self):
        # pymongo 同步事务需要 replica set 环境支持
        self.ensure_initialized()
        session = self._pool._client.start_session()
        session.start_transaction()
        try:
            yield session
        except PyMongoError as e:
            session.abort_transaction()
            logger.error(f"MongoDB事务错误: {str(e)}")
            raise
        else:
            session.commit_transaction()
        finally:
            session.end_session()

    def ping(self) -> bool:
        try:
            self._pool._client.admin.command("ping")
            return True
        except Exception as e:
            logger.error(f"MongoDB ping失败: {e}")
            return False
