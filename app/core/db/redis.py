# -*- encoding: utf-8 -*-
"""
@File    :redis.py
@Time    :2024/3/26 16:45
<AUTHOR>
"""

from typing import Optional, Any, Union
import redis
from redis.connection import ConnectionPool
from contextlib import contextmanager
from loguru import logger
import threading

from .base import DBClientBase, ConnectionPoolBase


class RedisPool(ConnectionPoolBase):
    """Redis连接池实现"""

    def __init__(
        self,
        host: str,
        port: int,
        password: Optional[str] = None,
        db: int = 0,
        pool_size: int = 10,
        **kwargs,
    ):
        super().__init__(pool_size=pool_size)
        self.connection_kwargs = {
            "host": host,
            "port": port,
            "password": password,
            "db": db,
            "max_connections": pool_size,
            **kwargs,
        }

    def create_pool(self):
        """创建Redis连接池"""
        if not self._pool:
            self._pool = ConnectionPool(**self.connection_kwargs)

    def close_pool(self):
        """关闭连接池"""
        if self._pool:
            self._pool.disconnect()
            self._pool = None

    def acquire(self):
        """获取Redis连接"""
        if not self._pool:
            raise RuntimeError("连接池未初始化")
        return redis.Redis(connection_pool=self._pool)

    def release(self, conn: Any):
        """释放Redis连接"""
        pass


class RedisClient(DBClientBase):
    """Redis客户端实现"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, "_initialized"):
            super().__init__()
            self._pool = None
            self._initialized = False

    def initialize(
        self,
        host: str,
        port: int,
        password: Optional[str] = None,
        db: int = 0,
        pool_size: int = 10,
        **kwargs,
    ):
        """初始化Redis连接池

        Args:
            host: Redis主机地址
            port: Redis端口号
            password: Redis密码
            db: 数据库索引
            pool_size: 连接池大小
            **kwargs: 其他连接参数
        """
        if self._initialized:
            return

        self._pool = RedisPool(
            host=host,
            port=port,
            password=password,
            db=db,
            pool_size=pool_size,
            **kwargs,
        )
        self._pool.create_pool()
        self._initialized = True
        logger.info("Redis连接池初始化成功")

    def close(self):
        """关闭Redis连接"""
        if self._pool:
            self._pool.close_pool()
            self._initialized = False
            logger.info("Redis连接池已关闭")

    @property
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._initialized and self._pool is not None

    @contextmanager
    def connection(self):
        """获取Redis连接的上下文管理器"""
        self.ensure_initialized()
        conn = None
        try:
            conn = self._pool.acquire()
            yield conn
        finally:
            if conn:
                self._pool.release(conn)

    def get_connection(self):
        """获取Redis连接（不使用上下文管理器）"""
        self.ensure_initialized()
        return self._pool.acquire()

    def ping(self) -> bool:
        """检测Redis连接是否正常

        Returns:
            bool: 连接正常返回True，否则返回False
        """
        try:
            with self.connection() as conn:
                # 设置命令执行超时时间为2秒
                conn.set("ping_check", "1", ex=1)  # 尝试写入一个临时键
                return True
        except Exception as e:
            logger.error(f"Redis ping失败: {e}")
            return False
