# -*- encoding: utf-8 -*-
"""
@File   :doc_reader_task.py
@Time   :2025/6/29
<AUTHOR>
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON

from ..base import Base


class DocReaderTask(Base):
    """文档解读任务表（兼容session_manager）"""
    __tablename__ = "doc_reader_tasks"
    __table_args__ = {"comment": "文档解读任务表"}

    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(String(100), nullable=False, unique=True, comment="任务ID")
    username = Column(String(100), nullable=False, comment="用户名")
    session_id = Column(String(100), nullable=False, comment="会话ID")
    doc_id = Column(String(100), nullable=False, comment="文档ID（MongoDB）")
    batch_id = Column(String(100), nullable=False, comment="批次ID")
    filename = Column(String(255), nullable=False, comment="文件名")
    file_type = Column(String(50), nullable=False, comment="文件类型")
    parse_type = Column(String(20), nullable=False, comment="解析类型")  # OCR | TEXT

    # 解析相关字段
    parser_doc_id = Column(String(100), nullable=True, comment="杏仁解析器返回的文档ID（仅OCR类型）")
    almond_batch_id = Column(String(100), nullable=True, comment="杏仁解析器批次ID（仅OCR类型）")
    status = Column(String(20), default="pending", comment="任务状态")  # pending | processing | completed | failed

    # 结果相关字段
    chunks_count = Column(Integer, default=0, comment="分块数量")
    error_message = Column(Text, nullable=True, comment="错误消息")
    retry_count = Column(Integer, default=0, comment="重试次数")

    # 元数据
    meta_info = Column(JSON, nullable=True, comment="任务元数据")

    # 时间字段
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
