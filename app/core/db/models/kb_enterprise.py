# -*- encoding: utf-8 -*-
"""
@File   :kb_preview.py
@Time   :2025/6/16 15:33
<AUTHOR>
"""


from sqlalchemy import Column, Integer, String
from ..base import Base


class KBEnterprise(Base):
    __tablename__ = "kb_enterprise"
    __table_args__ = {"comment": "知识库企业表"}

    autoid = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    kb_name = Column(String(255), nullable=False, unique=True, comment="知识库名称，唯一")
    file_name = Column(String(255), comment="上传文件名")
    file_path = Column(String(512), comment="文件路径")
    version = Column(String(64), comment="版本号")
    file_type = Column(String(32), comment="文件类型")
    ext1 = Column(String(255), comment="扩展字段1")
    ext2 = Column(String(255), comment="扩展字段2")
    ext3 = Column(String(255), comment="扩展字段3")
    ext4 = Column(String(255), comment="扩展字段4")
    ext5 = Column(String(255), comment="扩展字段5")
    ext6 = Column(String(255), comment="扩展字段6")
    ext7 = Column(String(255), comment="扩展字段7")
    ext8 = Column(String(255), comment="扩展字段8")
