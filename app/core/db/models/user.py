# -*- encoding: utf-8 -*-
"""
@File    :user.py
@Time    :2024/3/26 16:45
<AUTHOR>
"""

from typing import Optional, List
from datetime import datetime
from pydantic import EmailStr, Field
from beanie import Indexed, Link, before_event, Replace, Insert

from .base import BaseDocument, TimeStampMixin


class User(BaseDocument, TimeStampMixin):
    """用户模型"""
    
    username: str = Indexed(unique=True)  # 用户名，创建唯一索引
    email: EmailStr = Indexed(unique=True)  # 邮箱，创建唯一索引
    full_name: Optional[str] = None  # 全名
    disabled: bool = False  # 是否禁用
    last_login: Optional[datetime] = None  # 最后登录时间
    roles: List[str] = Field(default_factory=list)  # 角色列表
    
    @before_event([Replace, Insert])
    def validate_roles(self):
        """验证角色列表"""
        valid_roles = {"admin", "user", "guest"}
        self.roles = list(set(self.roles) & valid_roles)  # 只保留有效角色
        
    class Settings:
        name = "users"  # 集合名称
        indexes = [
            [("username", 1), ("email", 1)],  # 组合索引
            "created_at",  # 单字段索引
        ]
        
    class Config:
        schema_extra = {
            "example": {
                "username": "test_user",
                "email": "<EMAIL>",
                "full_name": "Test User",
                "roles": ["user"]
            }
        }
        
    @classmethod
    async def get_by_username(cls, username: str) -> Optional["User"]:
        """通过用户名获取用户
        
        Args:
            username: 用户名
            
        Returns:
            用户对象或None
        """
        return await cls.find_one({"username": username})
        
    @classmethod
    async def get_by_email(cls, email: str) -> Optional["User"]:
        """通过邮箱获取用户
        
        Args:
            email: 邮箱
            
        Returns:
            用户对象或None
        """
        return await cls.find_one({"email": email})
        
    async def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = datetime.utcnow()
        await self.save()
        
    async def add_role(self, role: str):
        """添加角色
        
        Args:
            role: 角色名称
        """
        if role not in self.roles:
            self.roles.append(role)
            await self.save()
            
    async def remove_role(self, role: str):
        """移除角色
        
        Args:
            role: 角色名称
        """
        if role in self.roles:
            self.roles.remove(role)
            await self.save() 