# -*- encoding: utf-8 -*-
"""
@File   :parse.py
@Time   :2025/6/16 15:22
<AUTHOR>
"""
from datetime import datetime

from sqlalchemy import Column, Integer, String, Text, DateTime

from ..base import Base


class ParseTask(Base):
    __tablename__ = "parse_tasks"
    __table_args__ = {"comment": "杏仁解析任务表"}

    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(100), nullable=False, comment="用户名")
    file_name = Column(String(100), nullable=False, comment="文件名")
    file_path = Column(Text, nullable=False, comment="文件路径")
    doc_id = Column(String(100), nullable=True, comment="文档ID")
    batch_id = Column(String(100), nullable=False, comment="批次ID")
    almond_parser_id = Column(String(100), nullable=False, comment="杏仁解析器ID")
    status = Column(String(20), default="pending", comment="解析状态")  # pending | done | error
    maxkb_url = Column(Text, nullable=True, comment="maxkb url")
    maxkb_id = Column(String(100), nullable=True, comment="maxkb id")
    error_message = Column(Text, nullable=True, comment="错误消息")
    created_at = Column(DateTime, default=datetime.now(), comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now(), comment="更新时间")
