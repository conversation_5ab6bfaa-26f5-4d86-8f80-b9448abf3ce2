"""
@File    :doccontext.py
@Time    :2024/3/26 16:45
<AUTHOR>
"""

from datetime import datetime
from typing import List, Dict, Optional
from beanie import Indexed
from pydantic import Field

from base import BaseDocument, TimeStampMixin


class DocContext(BaseDocument, TimeStampMixin):
    """文档上下文模型"""
    file_id: str
    file_name: str = Indexed()  # 文件名
    file_path: str = Indexed()  # 文件路径
    file_type: str  # 文件类型
    file_size: int  # 文件大小
    content: str  # 文件内容
    chunks: List[Dict] = Field(default_factory=list)  # 文档分块
    metadata: Dict = Field(default_factory=dict)  # 元数据

    is_vectorized: bool = False  # 是否已向量化
    is_quantized: bool = False  # 是否已量化
    vector_id: Optional[str] = None  # 向量ID
    status: str = "pending"  # 文档状态: pending, processing, ready, failed

    class Settings:
        name = "doccontext"
        indexes = [
            "file_name",
            "file_path",
            [("is_vectorized", 1), ("status", 1)]
        ]


class MultiDocSession(BaseDocument, TimeStampMixin):
    """多文档会话模型"""

    session_id: str = Indexed(unique=True)  # 会话ID
    user_id: str = Indexed()  # 用户ID
    doc_ids: List[str]  # 关联的文档ID列表
    doc_infos: List[Dict]  # 文档信息列表
    messages: List[Dict] = Field(default_factory=list)  # 会话消息历史
    status: str = "active"  # 会话状态: active, archived, deleted
    title: str = ""  # 会话标题
    history_summary: str = ""  # 历史摘要
    is_summarized: bool = False  # 是否已进行历史摘要
    message_count: int = 0  # 消息计数
    last_message_time: Optional[datetime] = None  # 最后消息时间

    class Settings:
        name = "multi_doc_sessions"
        indexes = [
            "session_id",
            "user_id",
            [("status", 1), ("last_message_time", -1)]
        ]
