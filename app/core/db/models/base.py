# -*- encoding: utf-8 -*-
"""
@File    :base.py
@Time    :2024/3/26 16:45
<AUTHOR>
"""

from datetime import datetime
from typing import Optional
from beanie import Document, Indexed
from pydantic import BaseModel


class TimeStampMixin(BaseModel):
    """时间戳混入类"""

    created_at: datetime = Indexed()  # 创建时间，创建索引
    updated_at: Optional[datetime] = None  # 更新时间

    async def save(self, *args, **kwargs):
        """保存前更新时间戳"""
        if not self.created_at:
            self.created_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        return await super().save(*args, **kwargs)


class BaseDocument(Document):
    """基础文档类"""

    class Settings:
        use_state_management = True  # 启用状态管理
        validate_on_save = True  # 保存时验证
        use_cache = True  # 使用缓存

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}  # 日期时间JSON编码器
