from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger

from .config import MemoryConfig

from .storage import RedisStorage, MongoStorage
from .summary import SummaryStrategy, LLMSummaryStrategy, LocalSummaryStrategy
from ..llm.session.model import Session, Message
from ..db.manager import DBManager


class SummaryTrigger:
    def should_summarize(self, session: Session, messages: List[Message]) -> bool:
        raise NotImplementedError


class DefaultSummaryTrigger(SummaryTrigger):
    def __init__(self, threshold: int = 3000, min_new_messages: int = 5):
        self.threshold = threshold
        self.min_new_messages = min_new_messages

    def should_summarize(self, session: Session, messages: List[Message]) -> bool:
        if not session:
            return False
        if session.is_summarized and len(messages) - session.summarized_message_count < self.min_new_messages:
            return False
        total_tokens = sum(len(msg.content) for msg in messages)
        return total_tokens > self.threshold


class MemoryManager:
    def __init__(
        self,
        config: MemoryConfig,
        session_cls,
        llm_client=None,
        summary_strategy: Optional[SummaryStrategy] = None,
        summary_trigger: Optional[SummaryTrigger] = None,
        auto_backup: bool = True,
    ):
        self.config = config
        self.redis_storage = RedisStorage(config)
        self.mongo_storage = MongoStorage(config)

        self.summary_strategy = summary_strategy or (
            LLMSummaryStrategy(llm_client) if llm_client else LocalSummaryStrategy()
        )
        self.summary_trigger = summary_trigger or DefaultSummaryTrigger()
        self.auto_backup = auto_backup
        self.session_cls = session_cls

    def update_session_config(self, session_id: str, **kwargs) -> bool:
        current_metadata = self.mongo_storage._get_current_metadata(session_id)
        updates = {k: v for k, v in kwargs.items() if v is not None}
        if not updates:
            return True
        new_metadata = {**current_metadata, **updates}
        return self.mongo_storage.update_session_metadata(session_id, new_metadata, reason=kwargs.get("reason"))

    def get_session_config_history(self, session_id: str) -> List[Dict]:
        return self.mongo_storage.get_metadata_history(session_id)

    def get_messages_with_config(self, session_id: str, limit: Optional[int] = None, skip: Optional[int] = None) -> List[Dict]:
        return self.mongo_storage.get_messages_with_metadata(
            session_id, skip=skip or 0, limit=limit or 0, include_metadata=True
        )

    def get_messages_count(
            self,
            session_id: str,
            role: Optional[str] = None,
            keyword: Optional[str] = None,
    ) -> int:
        """
        获取会话的消息总数（高性能版本）
        优先从Redis获取，如果没有过滤条件且Redis有数据则直接返回计数
        否则查询MongoDB
        """
        # 如果没有过滤条件，优先尝试从Redis获取总数
        if not role and not keyword:
            redis_count = self.redis_storage.get_messages_count(session_id)
            if redis_count > 0:
                return redis_count

        # 有过滤条件或Redis没有数据，查询MongoDB
        return self.mongo_storage.get_messages_count(session_id, role=role, keyword=keyword)

    def get_messages(
            self,
            session_id: str,
            role: Optional[str] = None,
            keyword: Optional[str] = None,
            limit: Optional[int] = None,
            skip: Optional[int] = None,
    ) -> List[Message]:
        reset_at = self.get_reset_time(session_id)

        messages = self.redis_storage.get_messages(
            session_id,
            start=skip or 0,
            end=-1 if limit is None else (skip or 0) + limit - 1,
            role=role,
            keyword=keyword
        )

        if reset_at:
            messages = [
                msg for msg in messages
                if "timestamp" in msg and datetime.fromisoformat(msg["timestamp"]) > reset_at
            ]

        if not messages:
            messages = self.mongo_storage.get_messages(
                session_id,
                skip=skip or 0,
                limit=limit or 0,
                role=role,
                keyword=keyword
            )
            if reset_at:
                messages = [
                    msg for msg in messages
                    if "timestamp" in msg and datetime.fromisoformat(msg["timestamp"]) > reset_at
                ]

        return [Message(**msg) for msg in messages]

    def add_message(self, session_id: str, message: Message) -> bool:
        redis_success = self.redis_storage.add_message(session_id, message)
        if not redis_success:
            return False

        return True

    def _should_backup_to_mongo(self, session_id: str) -> bool:
        messages = self.redis_storage.get_messages(session_id)
        return len(messages) >= self.config.REDIS_BACKUP_THRESHOLD

    def _backup_to_mongo(self, session_id: str) -> None:
        messages = self.redis_storage.get_messages(session_id)
        for msg in messages:
            self.mongo_storage.add_message(session_id, Message(**msg))

    def manual_backup_to_mongo(self, session_id: str) -> bool:
        try:
            self._backup_to_mongo(session_id)
            return True
        except Exception as e:
            logger.error(f"手动备份失败: {e}")
            return False

    def _check_and_summarize_history(self, session_id: str) -> bool:
        messages = self.get_messages(session_id)
        session = self.session_cls.get_session(session_id)
        if not self.summary_trigger.should_summarize(session, messages):
            return False

        try:
            retain_count = 5
            early_messages = messages[:-retain_count] if len(messages) > retain_count else []
            summary = self.summary_strategy.summarize(early_messages)
            if summary:
                session.history_summary = summary
                session.is_summarized = True
                session.summarized_message_count = len(messages)
                self.redis_storage.save_session(session)
                self.mongo_storage.save_session(session)
                logger.info(f"✅ 会话 {session_id} 摘要完成")
                return True
        except Exception as e:
            logger.error(f"摘要历史消息失败: {e}")
        return False

    def summarize_session(self, session_id: str) -> Optional[str]:
        if self._check_and_summarize_history(session_id):
            return self.get_session(session_id).history_summary
        return None

    def get_context(self, session_id: str) -> Dict[str, Any]:
        session = self.get_session(session_id)
        if not session:
            return {}
        messages = self.get_messages_with_config(session_id)
        current_config = self.mongo_storage._get_current_metadata(session_id)
        return {
            "messages": messages,
            "history_summary": session.history_summary,
            "metadata": session.metadata,
            "current_config": current_config
        }

    def delete_session(self, session_id: str) -> bool:
        redis_success = self.redis_storage.delete_session(session_id)
        mongo_success = self.mongo_storage.delete_session(session_id)
        return redis_success and mongo_success

    def get_session(self, session_id: str) -> Optional[Session]:
        session_data = self.mongo_storage.get_session(session_id)
        return Session.model_validate(session_data) if session_data else None

    def get_reset_time(self, session_id: str) -> Optional[datetime]:
        try:
            redis_conn = DBManager.get_redis().get_connection()
            reset_val = redis_conn.get(f"memory_reset_at:{session_id}")
            if reset_val:
                return datetime.fromisoformat(reset_val.decode())

            # fallback 从 Mongo 获取
            session = self.get_session(session_id)
            if session and session.reset_at:
                redis_conn.set(f"memory_reset_at:{session_id}", session.reset_at.isoformat())
                return session.reset_at

        except Exception as e:
            logger.warning(f"获取 reset_at 失败: {e}")

        return None

