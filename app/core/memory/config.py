from typing import Dict, Any


class MemoryConfig:
    # Redis配置
    REDIS_HOST = "localhost"
    REDIS_PORT = 6379
    REDIS_DB = 0
    REDIS_PREFIX = "chat_memory:"

    # MongoDB配置
    MONGO_URI = "mongodb://localhost:27017"
    MONGO_DB = "chat_memory"
    MONGO_COLLECTION = "chat_sessions"

    # 记忆系统配置
    MAX_MESSAGES_BEFORE_BACKUP = 20  # 触发MongoDB备份的消息数阈值
    SUMMARY_TRIGGER_THRESHOLD = 2000  # 触发摘要的token阈值
    MAX_CACHED_SESSIONS = 1000  # Redis最大缓存会话数
    DEFAULT_TTL = 3600 * 24 * 7  # Redis缓存默认过期时间（7天）
    REDIS_BACKUP_THRESHOLD = 20  # 触发Redis备份的会话数阈值

    AUTO_BACKUP = True

    # 消息结构配置
    MESSAGE_SCHEMA = {
        "role": str,
        "content": str,
        "timestamp": float,
        "metadata": Dict[str, Any]
    }
