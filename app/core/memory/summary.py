from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from loguru import logger

from app.core.llm.session.model import Message


class SummaryStrategy(ABC):
    @abstractmethod
    def summarize(self, messages: List[Message]) -> Optional[str]:
        pass


class LLMSummaryStrategy(SummaryStrategy):
    def __init__(self, llm_client):
        self.llm = llm_client

    def summarize(self, messages: List[Message]) -> Optional[str]:
        try:
            # 将消息转换为适合LLM处理的格式
            formatted_messages = [f"{msg.role}: {msg.content}" for msg in messages]
            context = "\n".join(formatted_messages)

            # 构造摘要提示
            prompt = f"""请对以下对话内容进行简要总结，保留关键信息：
                        {context}
                        总结："""
            message = [{"role": "user", "content": prompt}]
            # 调用LLM进行摘要
            summary = self.llm.chat(message)
            return summary.content
        except Exception as e:
            logger.exception(f"LLM摘要失败: {str(e)}")
            return None


class LocalSummaryStrategy(SummaryStrategy):
    def summarize(self, messages: List[Message]) -> str:
        """
        本地简单摘要策略：保留每条消息的前N个字符
        """
        MAX_CHARS_PER_MESSAGE = 50
        summary_parts = []

        for msg in messages:
            content = msg.content
            if len(content) > MAX_CHARS_PER_MESSAGE:
                content = content[:MAX_CHARS_PER_MESSAGE] + "..."
            summary_parts.append(f"{msg.role}: {content}")

        return "\n".join(summary_parts)
