from copy import deepcopy
from typing import List, Dict, Any, Optional
from loguru import logger
import json
from datetime import datetime, timezone

from ..db.redis import RedisClient
from ..db.mongo import MongoClient
from .config import MemoryConfig
from ..llm.session.model import Session, Message


class RedisStorage:
    def __init__(self, config: MemoryConfig):
        self.prefix = config.REDIS_PREFIX
        self.ttl = config.DEFAULT_TTL
        self.redis_client = RedisClient()
        self.redis_client.initialize(
            host=config.REDIS_HOST, port=config.REDIS_PORT, db=config.REDIS_DB
        )
        self.mongo_storage = MongoStorage(config)
        self.auto_backup = config.AUTO_BACKUP

    def _get_session_key(self, session_id: str) -> str:
        return f"{self.prefix}session:{session_id}"

    def _get_messages_key(self, session_id: str) -> str:
        return f"{self.prefix}messages:{session_id}"

    def get_session(self, session_id: str) -> Optional[Dict]:
        with self.redis_client.connection() as redis:
            session_data = redis.get(self._get_session_key(session_id))
            if not session_data:
                return None

            session_dict = json.loads(session_data)
            messages = self.get_messages(session_id)
            session_dict["messages"] = messages

            return session_dict

    def save_session(self, session: Session) -> bool:
        try:
            with self.redis_client.connection() as redis:
                session_dict = session.model_dump()
                messages = session_dict.pop("messages", [])

                redis.setex(
                    self._get_session_key(session.session_id),
                    self.ttl,
                    json.dumps(session_dict),
                )

                if messages:
                    key = self._get_messages_key(session.session_id)
                    pipe = redis.pipeline()
                    pipe.delete(key)
                    for i, msg in enumerate(messages):
                        msg["index"] = i
                        pipe.rpush(key, json.dumps(msg))
                    pipe.expire(key, self.ttl)
                    pipe.execute()
            return True
        except Exception as e:
            logger.error(f"Redis保存失败: {e}")
            return False

    def add_message(self, session_id: str, message: Message) -> bool:
        try:
            with self.redis_client.connection() as redis:
                messages_key = self._get_messages_key(session_id)
                index_key = f"{self.prefix}message_index:{session_id}"

                # ✅ 获取自增 index
                index = redis.incr(index_key) - 1
                msg_dict = message.model_dump()
                msg_dict["index"] = index

                # ✅ 转换 datetime 为 ISO 字符串（否则 json.dumps 会报错）
                msg_dict = deepcopy(msg_dict)
                for k, v in msg_dict.items():
                    if isinstance(v, datetime):
                        msg_dict[k] = v.isoformat()

                # ✅ 存入 Redis 列表中
                redis.rpush(messages_key, json.dumps(msg_dict))
                redis.expire(messages_key, self.ttl)

                # ✅ 更新 session 更新时间
                session_key = self._get_session_key(session_id)
                session_data = redis.get(session_key)
                if session_data:
                    session_dict = json.loads(session_data)
                    session_dict["updated_at"] = message.timestamp.isoformat()
                    redis.setex(session_key, self.ttl, json.dumps(session_dict, ensure_ascii=False))
            if self.auto_backup:
                # ✅ 触发备份检查（由 MongoStorage 实现）
                self.mongo_storage.check_and_backup_messages(session_id)
            return True
        except Exception as e:
            logger.error(f"Redis添加消息失败: {e}")
            return False

    def get_messages_count(self, session_id: str) -> int:
        """
        获取会话的消息总数（高性能版本）
        直接从Redis的自增计数器获取，避免查询所有消息
        """
        with self.redis_client.connection() as redis:
            index_key = f"{self.prefix}message_index:{session_id}"
            count = redis.get(index_key)
            return int(count) if count else 0

    def get_messages(
        self,
        session_id: str,
        start: int = 0,
        end: int = -1,
        role: Optional[str] = None,
        keyword: Optional[str] = None,
    ) -> List[Dict]:
        with self.redis_client.connection() as redis:
            messages_key = self._get_messages_key(session_id)
            messages_data = redis.lrange(messages_key, start, end)
            messages = [json.loads(msg) for msg in messages_data]

            if role:
                messages = [m for m in messages if m.get("role") == role]
            if keyword:
                messages = [m for m in messages if keyword in m.get("content", "")]

            return messages

    def delete_session(self, session_id: str) -> bool:
        with self.redis_client.connection() as redis:
            session_key = self._get_session_key(session_id)
            messages_key = self._get_messages_key(session_id)
            return all([redis.delete(session_key), redis.delete(messages_key)])


class MongoStorage:
    BACKUP_COUNT_THRESHOLD = 20

    def __init__(self, config: MemoryConfig):
        self.prefix = config.REDIS_PREFIX
        self.mongo_client = MongoClient()
        self.mongo_client.initialize(
            host=config.MONGO_URI.split("://")[1].split(":")[0],
            port=int(config.MONGO_URI.split(":")[-1].split("/")[0]),
            database=config.MONGO_DB,
        )
        self.sessions_collection = config.MONGO_COLLECTION
        self.messages_collection = f"{config.MONGO_COLLECTION}_messages"
        self.metadata_collection = f"{config.MONGO_COLLECTION}_metadata"

        self.redis = RedisClient().get_connection()

    def check_and_backup_messages(self, session_id: str) -> None:
        message_index_key = f"{self.prefix}message_index:{session_id}"
        backup_index_key = f"{self.prefix}backup_index:{session_id}"

        current_index = int(self.redis.get(message_index_key) or -1)
        last_backup_index = int(self.redis.get(backup_index_key) or -1)

        if current_index >= last_backup_index + self.BACKUP_COUNT_THRESHOLD:
            logger.info(f"触发自动备份: {session_id}")
            self._backup_to_mongo(session_id, last_backup_index + 1, current_index)
            self.redis.set(backup_index_key, current_index)

    def _backup_to_mongo(self, session_id: str, start_index: int, end_index: int) -> None:
        messages_key = f"{self.prefix}messages:{session_id}"
        raw_msgs = self.redis.lrange(messages_key, start_index, end_index)
        if not raw_msgs:
            return

        metadata = self._get_current_metadata(session_id)
        messages = []

        for i, raw in enumerate(raw_msgs, start=start_index):
            msg = json.loads(raw)
            msg["session_id"] = session_id
            msg["index"] = i  # 保证 index 精确同步
            msg["metadata"] = {
                **msg.get("metadata", {}),
                "session_metadata": metadata
            }
            messages.append(msg)

        if messages:
            self.mongo_client.get_collection(self.messages_collection).insert_many(messages)
            self.mongo_client.get_collection(self.sessions_collection).update_one(
                {"session_id": session_id},
                {"$set": {"updated_at": datetime.now(timezone.utc)}}
            )

    def get_session(self, session_id: str) -> Optional[Dict]:
        session = self.mongo_client.get_collection(self.sessions_collection).find_one(
            {"session_id": session_id}
        )
        if not session:
            return None

        messages = self.get_messages(session_id)
        session["messages"] = messages
        return session

    def save_session(self, session: Session) -> bool:
        try:
            session_dict = session.to_dict()
            messages = session_dict.pop("messages", [])

            self.mongo_client.get_collection(self.sessions_collection).update_one(
                {"session_id": session.session_id}, {"$set": session_dict}, upsert=True
            )

            if messages:
                messages_coll = self.mongo_client.get_collection(
                    self.messages_collection
                )
                messages_coll.delete_many({"session_id": session.session_id})
                messages_docs = [
                    {"session_id": session.session_id, "index": i, **msg}
                    for i, msg in enumerate(messages)
                ]
                messages_coll.insert_many(messages_docs)

            return True
        except Exception as e:
            print(f"MongoDB保存失败: {str(e)}")
            return False

    def add_message(self, session_id: str, message: Message) -> bool:
        try:
            current_metadata = self._get_current_metadata(session_id)

            messages_coll = self.mongo_client.get_collection(self.messages_collection)
            last_message = messages_coll.find_one(
                {"session_id": session_id}, sort=[("index", -1)]
            )
            index = (last_message["index"] + 1) if last_message else 0

            message_doc = {
                "session_id": session_id,
                "index": index,
                "metadata": {**message.metadata, "session_metadata": current_metadata},
                **message.model_dump(),
            }

            messages_coll.insert_one(message_doc)

            self.mongo_client.get_collection(self.sessions_collection).update_one(
                {"session_id": session_id}, {"$set": {"updated_at": message.timestamp}}
            )

            return True
        except Exception as e:
            logger.error(f"MongoDB添加消息失败: {str(e)}")
            return False

    def get_messages_count(
        self,
        session_id: str,
        role: Optional[str] = None,
        keyword: Optional[str] = None,
    ) -> int:
        """
        获取会话的消息总数（高性能版本）
        直接使用MongoDB的count_documents方法
        """
        query = {"session_id": session_id}
        if role:
            query["role"] = role
        if keyword:
            query["content"] = {"$regex": keyword, "$options": "i"}

        messages_coll = self.mongo_client.get_collection(self.messages_collection)
        return messages_coll.count_documents(query)

    def get_messages(
        self,
        session_id: str,
        skip: int = 0,
        limit: int = 0,
        role: Optional[str] = None,
        keyword: Optional[str] = None,
    ) -> List[Dict]:
        query = {"session_id": session_id}
        if role:
            query["role"] = role
        if keyword:
            query["content"] = {"$regex": keyword, "$options": "i"}

        projection = {"_id": 0, "session_id": 0, "index": 0}

        messages_coll = self.mongo_client.get_collection(self.messages_collection)
        cursor = messages_coll.find(query, projection).sort("index", 1)

        if skip:
            cursor = cursor.skip(skip)
        if limit:
            cursor = cursor.limit(limit)

        return list(cursor)

    def delete_session(self, session_id: str) -> bool:
        try:
            sessions_result = self.mongo_client.get_collection(
                self.sessions_collection
            ).delete_one({"session_id": session_id})
            self.mongo_client.get_collection(self.messages_collection).delete_many(
                {"session_id": session_id}
            )
            return sessions_result.deleted_count > 0
        except Exception as e:
            logger.error(f"MongoDB删除失败: {str(e)}")
            return False

    def get_sessions_by_user(self, user_id: str) -> List[Dict]:
        return list(
            self.mongo_client.get_collection(self.sessions_collection).find(
                {"metadata.user_id": user_id}
            )
        )

    def update_session_metadata(
        self, session_id: str, metadata: dict, reason: str = None
    ) -> bool:
        try:
            metadata_coll = self.mongo_client.get_collection(self.metadata_collection)

            metadata_doc = {
                "session_id": session_id,
                "metadata": metadata,
                "timestamp": datetime.now(timezone.utc),
                "reason": reason,
                "previous_metadata": self._get_current_metadata(session_id),
            }

            metadata_coll.insert_one(metadata_doc)

            self.mongo_client.get_collection(self.sessions_collection).update_one(
                {"session_id": session_id},
                {
                    "$set": {
                        "current_metadata": metadata,
                        "updated_at": datetime.now(timezone.utc),
                    }
                },
            )

            return True
        except Exception as e:
            logger.error(f"更新会话元数据失败: {str(e)}")
            return False

    def _get_current_metadata(self, session_id: str) -> dict:
        session = self.mongo_client.get_collection(self.sessions_collection).find_one(
            {"session_id": session_id}, {"current_metadata": 1}
        )
        return session.get("current_metadata", {}) if session else {}

    def get_metadata_history(self, session_id: str) -> List[Dict]:
        metadata_coll = self.mongo_client.get_collection(self.metadata_collection)
        return list(
            metadata_coll.find({"session_id": session_id}, {"_id": 0}).sort(
                "timestamp", 1
            )
        )

    def get_messages_with_metadata(
        self,
        session_id: str,
        skip: int = 0,
        limit: int = 0,
        include_metadata: bool = True,
    ) -> List[Dict]:
        messages_coll = self.mongo_client.get_collection(self.messages_collection)
        projection = {"_id": 0, "session_id": 0, "index": 0}
        if not include_metadata:
            projection["metadata"] = 0

        cursor = messages_coll.find({"session_id": session_id}, projection).sort(
            "index", 1
        )

        if skip:
            cursor = cursor.skip(skip)
        if limit:
            cursor = cursor.limit(limit)

        return list(cursor)
