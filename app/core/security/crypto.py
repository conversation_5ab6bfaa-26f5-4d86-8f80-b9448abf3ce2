# -*- encoding: utf-8 -*-
"""
@File   :crypto.py
@Time   :2025/6/9 20:59
<AUTHOR>
"""
from cryptography.fernet import Fernet, InvalidToken
from loguru import logger

from app.configs.config import settings


class CryptoManager:
    def __init__(self, key: bytes = settings.encryption_key.encode()):
        if not isinstance(key, bytes):
            raise ValueError("加密密钥必须是 bytes 类型")
        self.fernet = Fernet(key)

    @staticmethod
    def generate_key() -> bytes:
        """生成新的加密密钥（需保存在安全位置）"""
        return Fernet.generate_key()

    def encrypt(self, data: str) -> str:
        """加密字符串数据，返回 base64 编码后的密文"""
        if not isinstance(data, str):
            raise ValueError("encrypt 只支持 str 类型数据")
        return self.fernet.encrypt(data.encode()).decode()

    def decrypt(self, token: str) -> str:
        """解密 base64 编码的密文，返回原始字符串"""
        try:
            return self.fernet.decrypt(token.encode()).decode()
        except InvalidToken:
            logger.error("❌ 解密失败：非法的 token")
            raise ValueError("解密失败，密钥错误或 token 被篡改")
