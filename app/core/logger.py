# -*- encoding: utf-8 -*-
"""
@File   :logger.py
@Time   :2025/6/10 09:38
<AUTHOR>
"""
import os.path
import sys

from loguru import logger


from app.configs.config import settings


def init_logger():
    """初始化日志管理器"""
    logger.remove()
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
        "<level>{message}</level>",
    )
    os.makedirs(settings.log_dir, exist_ok=True)
    log_path = os.path.join(settings.log_dir, "app.log")
    logger.add(
        log_path,
        level=settings.log_level,
        rotation=settings.log_rotation,
        retention=settings.log_retention,
        encoding="utf-8",
        enqueue=False,
        backtrace=True,
        diagnose=True,
    )
    logger.info("🚀 Loguru Logger Initialized")
