# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/7 13:34
@Auth ： qu ming
@File ：excel_parser.py
@IDE ：PyCharm
"""

import logging
import os
from io import BytesIO
import chardet
import pandas as pd
from openpyxl import Workbook, load_workbook

from app.core.doc_parser.base import BaseDocParser

all_codecs = [
    'utf-8', 'gb2312', 'gbk', 'utf_16', 'ascii', 'big5', 'big5hkscs',
    'cp037', 'cp273', 'cp424', 'cp437',
    'cp500', 'cp720', 'cp737', 'cp775', 'cp850', 'cp852', 'cp855', 'cp856', 'cp857',
    'cp858', 'cp860', 'cp861', 'cp862', 'cp863', 'cp864', 'cp865', 'cp866', 'cp869',
    'cp874', 'cp875', 'cp932', 'cp949', 'cp950', 'cp1006', 'cp1026', 'cp1125',
    'cp1140', 'cp1250', 'cp1251', 'cp1252', 'cp1253', 'cp1254', 'cp1255', 'cp1256',
    'cp1257', 'cp1258', 'euc_jp', 'euc_jis_2004', 'euc_jisx0213', 'euc_kr',
    'gb18030', 'hz', 'iso2022_jp', 'iso2022_jp_1', 'iso2022_jp_2',
    'iso2022_jp_2004', 'iso2022_jp_3', 'iso2022_jp_ext', 'iso2022_kr', 'latin_1',
    'iso8859_2', 'iso8859_3', 'iso8859_4', 'iso8859_5', 'iso8859_6', 'iso8859_7',
    'iso8859_8', 'iso8859_9', 'iso8859_10', 'iso8859_11', 'iso8859_13',
    'iso8859_14', 'iso8859_15', 'iso8859_16', 'johab', 'koi8_r', 'koi8_t', 'koi8_u',
    'kz1048', 'mac_cyrillic', 'mac_greek', 'mac_iceland', 'mac_latin2', 'mac_roman',
    'mac_turkish', 'ptcp154', 'shift_jis', 'shift_jis_2004', 'shift_jisx0213',
    'utf_32', 'utf_32_be', 'utf_32_le', 'utf_16_be', 'utf_16_le', 'utf_7', 'windows-1250', 'windows-1251',
    'windows-1252', 'windows-1253', 'windows-1254', 'windows-1255', 'windows-1256',
    'windows-1257', 'windows-1258', 'latin-2'
]


def find_code(blob):
    detected = chardet.detect(blob[:1024])
    if detected['confidence'] > 0.5:
        if detected['encoding'] == "ascii":
            return "utf-8"

    for c in all_codecs:
        try:
            blob[:1024].decode(c)
            return c
        except Exception:
            pass
        try:
            blob.decode(c)
            return c
        except Exception:
            pass

    return "utf-8"


class ExcelParser(BaseDocParser):

    @staticmethod
    def _load_excel_to_workbook(file_like_object):
        if isinstance(file_like_object, bytes):
            file_like_object = BytesIO(file_like_object)

        # Read first 4 bytes to determine file type
        file_like_object.seek(0)
        file_head = file_like_object.read(4)
        file_like_object.seek(0)

        if not (file_head.startswith(b'PK\x03\x04') or file_head.startswith(b'\xD0\xCF\x11\xE0')):
            logging.info("****wxy: Not an Excel file, converting CSV to Excel Workbook")

            try:
                file_like_object.seek(0)
                df = pd.read_csv(file_like_object)
                return ExcelParser._dataframe_to_workbook(df)

            except Exception as e_csv:
                raise Exception(f"****wxy: Failed to parse CSV and convert to Excel Workbook: {e_csv}")

        try:
            return load_workbook(file_like_object, data_only=True)
        except Exception as e:
            logging.info(f"****wxy: openpyxl load error: {e}, try pandas instead")
            try:
                file_like_object.seek(0)
                df = pd.read_excel(file_like_object)
                return ExcelParser._dataframe_to_workbook(df)
            except Exception as e_pandas:
                raise Exception(f"****wxy: pandas.read_excel error: {e_pandas}, original openpyxl error: {e}")

    @staticmethod
    def _dataframe_to_workbook(df):
        wb = Workbook()
        ws = wb.active
        ws.title = "Data"

        # 写入列名
        for col_num, column_name in enumerate(df.columns, 1):
            cell = ws.cell(row=1, column=col_num)
            cell.value = str(column_name)

        # 写入数据
        for row_num, (_, row) in enumerate(df.iterrows(), 2):
            for col_num, value in enumerate(row, 1):
                cell = ws.cell(row=row_num, column=col_num)
                # 确保值是基本类型
                if hasattr(value, 'item'):  # 处理numpy类型
                    value = value.item()
                cell.value = value

        return wb

    def html(self, fnm, chunk_rows=256):
        file_like_object = BytesIO(fnm) if not isinstance(fnm, str) else fnm
        wb = ExcelParser._load_excel_to_workbook(file_like_object)
        tb_chunks = []
        for sheetname in wb.sheetnames:
            ws = wb[sheetname]
            rows = list(ws.rows)
            if not rows:
                continue

            tb_rows_0 = "<tr>"
            for t in list(rows[0]):
                tb_rows_0 += f"<th>{t.value}</th>"
            tb_rows_0 += "</tr>"

            for chunk_i in range((len(rows) - 1) // chunk_rows + 1):
                tb = ""
                tb += f"<table><caption>{sheetname}</caption>"
                tb += tb_rows_0
                for r in list(
                        rows[1 + chunk_i * chunk_rows: 1 + (chunk_i + 1) * chunk_rows]
                ):
                    tb += "<tr>"
                    for i, c in enumerate(r):
                        if c.value is None:
                            tb += "<td></td>"
                        else:
                            tb += f"<td>{c.value}</td>"
                    tb += "</tr>"
                tb += "</table>\n"
                tb_chunks.append(tb)

        return tb_chunks

    def __call__(self, fnm):
        file_like_object = BytesIO(fnm) if not isinstance(fnm, str) else open(fnm, "rb")
        wb = self._load_excel_to_workbook(file_like_object)

        res = []
        for sheetname in wb.sheetnames:
            ws = wb[sheetname]
            rows_iter = ws.iter_rows()
            try:
                headers_row = next(rows_iter)
            except StopIteration:
                continue

            headers = [cell.value for cell in headers_row]

            for row in rows_iter:
                # 跳过全为空的行
                if all(cell.value is None for cell in row):
                    continue

                fields = []
                for i, cell in enumerate(row):
                    value = cell.value
                    if value is None:
                        continue

                    # 处理特殊类型
                    if hasattr(value, 'item'):
                        value = value.item()
                    elif isinstance(value, (pd.Series, pd.DataFrame)):
                        continue

                    # 获取 header 或默认值
                    header = headers[i] if i < len(headers) and headers[i] else ""
                    field = f"{header}：{value}" if header else str(value)

                    # 去掉制表符、空格
                    field = field.replace('\t', ' ').strip()

                    fields.append(field)

                # 如果有字段内容
                if fields:
                    line = "; ".join(fields)
                    # 去除多余的分隔符（多个分号）
                    line = "; ".join(filter(None, line.split("; "))).strip()

                    # 检查 sheetname 并加上标识
                    if "sheet" not in sheetname.lower():
                        line += f" ——{sheetname}"

                    res.append(line)

        return res

    @staticmethod
    def row_number(fnm, binary):
        if fnm.split(".")[-1].lower().find("xls") >= 0:
            wb = ExcelParser._load_excel_to_workbook(BytesIO(binary))
            total = 0
            for sheetname in wb.sheetnames:
                ws = wb[sheetname]
                total += len(list(ws.rows))
            return total

        if fnm.split(".")[-1].lower() in ["csv", "txt"]:
            encoding = find_code(binary)
            txt = binary.decode(encoding, errors="ignore")
            return len(txt.split("\n"))

    def to_text(self, filename: str = None, binary: bytes = None) -> str:
        """读取本地 Excel/CSV 文件或直接处理传入的二进制数据并返回拼接后的文本"""

        if filename is not None and os.path.exists(filename):
            print(filename)
            # 读取本地文件
            with open(filename, "rb") as f:
                binary = f.read()
        # 如果传入了 binary 数据，则直接使用它
        if binary is not None:
            sections = [x for x in self(binary) if x]
            return ''.join(sections)

    def can_parse(self, file_path, file_bytes):
        # 支持 xlsx/xls/csv
        ext = file_path.lower().split('.')[-1]
        if ext.lower() in ['xlsx', 'xls', 'csv']:
            return True
        return False

    def parse(self, file_path=None, file_bytes=None):
        return self.to_text(filename=file_path, binary=file_bytes)


if __name__ == "__main__":
    path = r'C:\Users\<USER>\Desktop\项目列表交接时间安排表.xlsx'
    psr = ExcelParser()
    content = psr.to_text(path)
    print(content)
