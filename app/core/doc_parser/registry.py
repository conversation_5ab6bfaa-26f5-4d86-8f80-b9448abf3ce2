# -*- encoding: utf-8 -*-
"""
@File   :doc_registry.py
@Time   :2025/5/22 16:20
<AUTHOR>
"""
import os

from app.core.doc_parser.docx_parser import DocxParser
from app.core.doc_parser.html_parser import HtmlParser
from app.core.doc_parser.mineru2_parser import MinerUParserV2
from app.core.doc_parser.parser_fallback import FallbackParser
from app.core.doc_parser.pdf_parser import PdfParser
from app.core.doc_parser.txt_parser import TxtParser
from app.core.doc_parser.excel_parser import ExcelParser
from app.core.doc_parser.mineru1_parser import MineruOCRParserV1

ALL_PARSERS = [
    # MinerUParserV2(),
    # MineruOCRParserV1(),
    DocxParser(),
    PdfParser(),
    HtmlParser(),
    TxtParser(),
    ExcelParser(),
    FallbackParser()  # 兜底，始终返回True
]


def parse_file(file_path: str) -> str:
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    with open(file_path, "rb") as f:
        file_bytes = f.read()

    for parser in ALL_PARSERS:
        if parser.can_parse(file_path, file_bytes):
            return parser.parse(file_path, file_bytes)
    return ""


def parse_files_concurrently(file_paths: list[str], max_workers: int = 8):
    results = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_path = {executor.submit(parse_file, path): path for path in file_paths}
        for future in as_completed(future_to_path):
            path = future_to_path[future]
            try:
                result = future.result()
                results[path] = result
            except Exception as e:
                results[path] = f"❌ 解析失败: {e}"
    return results


if __name__ == "__main__":
    from concurrent.futures import ThreadPoolExecutor, as_completed

    # path = r"D:\WXWork\1688857100340944\Cache\File\2025-06\紫江企业2024年度报告_报表.docx"
    # print(parse_file(path))

    file_list = [
        r"C:\Users\<USER>\Documents\1. 手机银行-转账-转账汇款.docx",
        # r"D:\WXWork\1688857100340944\Cache\File\2025-06\紫江企业2024年度报告_报表(1).docx",
        # r"D:\WXWork\1688857100340944\Cache\File\2025-06\htmltoword001.docx",
        # r"D:\WXWork\1688857100340944\Cache\File\2025-06\会议纪要-项目管理-20250616.docx"

    ]
    result_map = parse_files_concurrently(file_list)

    for file, content in result_map.items():
        print(f"{file} -> {content}")
