# -*- encoding: utf-8 -*-
"""
@File   :html_parser.py
@Time   :2025/5/22 16:11
<AUTHOR>
"""
from app.core.doc_parser.base import BaseDocParser


class HtmlParser(BaseDocParser):
    def can_parse(self, file_path, file_bytes):
        # 后缀或魔数或内容检测
        if file_path.lower().endswith(('.html', '.htm')):
            return True
        try:
            if b"<html" in file_bytes[:4096].lower():
                return True
        except Exception:
            pass
        return False

    def parse(self, file_path, file_bytes):
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(file_bytes, "html.parser")
        return soup.get_text()
