# -*- encoding: utf-8 -*-
"""
@File   :txt_parser.py
@Time   :2025/5/22 16:07
<AUTHOR>
"""
from app.core.doc_parser.base import BaseDocParser


class TxtParser(BaseDocParser):
    def can_parse(self, file_path, file_bytes):
        if file_path.lower().endswith('.txt'):
            return True
        # 检测纯文本特征：基本全是可打印字符
        try:
            sample = file_bytes[:1024]
            if all((32 <= b <= 127 or b in (9, 10, 13)) for b in sample):
                return True
        except Exception:
            pass
        return False

    def parse(self, file_path, file_bytes):
        try:
            return file_bytes.decode("utf-8", errors="ignore")
        except Exception:
            return ""
