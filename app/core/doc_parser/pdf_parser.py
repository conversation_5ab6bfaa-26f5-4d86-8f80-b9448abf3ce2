# -*- encoding: utf-8 -*-
"""
@File   :pdf_parser.py
@Time   :2025/5/22 16:10
<AUTHOR>
"""
import pdfplumber

from app.core.doc_parser.base import BaseDocParser


class PdfParser(BaseDocParser):
    def can_parse(self, file_path, file_bytes):
        # PDF文件魔数：%PDF-
        return file_bytes.startswith(b'%PDF-')

    def parse(self, file_path, file_bytes):

        try:
            with pdfplumber.open(file_path) as pdf:
                return "\n".join([page.extract_text() or "" for page in pdf.pages])
        except Exception:
            return ""
