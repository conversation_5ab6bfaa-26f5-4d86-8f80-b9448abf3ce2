# -*- encoding: utf-8 -*-
"""
@File   :docx_parser.py
@Time   :2025/5/22 16:12
<AUTHOR>
"""
import zipfile
from docx import Document

from app.core.doc_parser.base import BaseDocParser

from loguru import logger as logging
from docx import Document
import re
import pandas as pd
from collections import Counter
from io import BytesIO
from docx.image.exceptions import (
    InvalidImageStreamError,
    UnexpectedEndOfFileError,
    UnrecognizedImageError,
)

# 确保你已经安装了 python-docx 库
# pip install python-docx

from docx import Document
from docx.document import Document as DocumentObject
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.table import Table
from docx.text.paragraph import Paragraph


def escape_markdown(text: str) -> str:
    # 只做表格的|转义
    return text.replace("|", "\\|")


def extract_heading_level(style_name: str):
    # 标准“Heading 1”或“标题 1”
    match = re.match(r"^(Heading|标题)\s?(\d+)", style_name)
    if match:
        return int(match.group(2))
    # 兼容“[1级]”“【1级】”“(1级)”或“一级”
    match = re.search(r"[\[【\(]?(\d+)级[\]】\)]?", style_name)
    if match:
        return int(match.group(1))
    # 兼容“一级”“二级”这种中文
    cn_level = {
        "一级": 1,
        "二级": 2,
        "三级": 3,
        "四级": 4,
        "五级": 5,
    }
    for k, v in cn_level.items():
        if k in style_name:
            return v
    return None


def parse_word_to_markdown(file_path: str) -> str:
    try:
        doc = Document(file_path)
        markdown_blocks = []

        for child in doc._body._element:
            if isinstance(child, CT_P):
                p = Paragraph(child, doc)
                text = p.text.strip()
                if not text:
                    continue

                style_name = p.style.name
                level = extract_heading_level(style_name)
                if level:
                    markdown_blocks.append(f"{'#' * level} {text}")
                elif style_name.lower().startswith("list"):
                    # 简单检测“列表段落”
                    markdown_blocks.append(f"- {text}")
                else:
                    # 富文本加粗斜体
                    md_runs = []
                    for run in p.runs:
                        t = run.text.replace("\n", "<br/>")
                        if run.bold:
                            t = f"**{t}**"
                        if run.italic:
                            t = f"*{t}*"
                        md_runs.append(t)
                    markdown_blocks.append("".join(md_runs) if md_runs else text)
            elif isinstance(child, CT_Tbl):
                table = Table(child, doc)
                if not table.rows:
                    continue
                md_table = []
                header_cells = [
                    escape_markdown(cell.text.strip().replace("\n", " "))
                    for cell in table.rows[0].cells
                ]
                md_table.append("| " + " | ".join(header_cells) + " |")
                separator = ["---"] * len(header_cells)
                md_table.append("| " + " | ".join(separator) + " |")
                for row in table.rows[1:]:
                    data_cells = [
                        escape_markdown(cell.text.strip().replace("\n", " "))
                        for cell in row.cells
                    ]
                    md_table.append("| " + " | ".join(data_cells) + " |")
                markdown_blocks.append("\n".join(md_table))
        return "\n\n".join(markdown_blocks)
    except Exception as e:
        import traceback

        print(traceback.format_exc())
        return ""


class DocxParser(BaseDocParser):
    def can_parse(self, file_path, file_bytes):
        ext = file_path.lower().split(".")[-1]
        # 支持 docx
        if ext == "docx":
            if file_bytes[:2] == b"PK":
                try:
                    with zipfile.ZipFile(file_path) as z:
                        if "[Content_Types].xml" in z.namelist():
                            return True
                        # 伪docx兼容性过滤
                        if any(
                            name.endswith((".htm", ".html")) for name in z.namelist()
                        ):
                            return False
                except Exception:
                    return False
                return True
        # 支持 doc（老格式）
        if ext == "doc":
            # 魔数 D0 CF 11 E0（OLE格式）
            if file_bytes[:8] == b"\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1":
                return True
        # 还可以加内容判断防止伪造
        return False

    def parse(self, file_path, file_bytes=None):
        try:
            return parse_word_to_markdown(file_path)
        except Exception:
            try:
                from tika import parser

                return parser.from_file(file_path).get("content", "") or ""
            except Exception:
                return ""


if __name__ == "__main__":
    d = DocxParser()
    print(
        d.parse(
            r"D:\WXWork\1688857100340944\Cache\File\2025-06\BG2025ZA10409紫江企业2024年度报告1_附注.docx"
        )
    )
