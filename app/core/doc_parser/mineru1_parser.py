# -*- encoding: utf-8 -*-
"""
@File   :mineru_ocr_parser.py
@Time   :2025/6/23 14:43
<AUTHOR>
"""
import base64
import requests
from loguru import logger
from time import time

from app.core.doc_parser.base import BaseDocParser

MINERU_OCR_URL = "http://172.17.10.244:2020"
MINERU_SUPPORT_EXT = {"pdf", "docx", "doc"}


class MineruOCRParserV1(BaseDocParser):
    def can_parse(self, file_path: str, file_bytes: bytes) -> bool:
        ext = file_path.lower().split(".")[-1]
        return ext in MINERU_SUPPORT_EXT

    def parse(self, file_path: str, file_bytes: bytes) -> str:
        def to_b64(path: str) -> str:
            with open(path, "rb") as f:
                return base64.b64encode(f.read()).decode("utf-8")

        def get_remote_markdown(result_url: str) -> str:
            url = f"{MINERU_OCR_URL}{result_url}"
            logger.info(f"📥 获取 markdown：{url}")
            resp = requests.get(url)
            if resp.status_code != 200:
                raise Exception(f"无法获取 markdown 内容：{resp.status_code} - {resp.text}")
            return resp.json().get("content", "")

        start = time()
        try:
            response = requests.post(
                f"{MINERU_OCR_URL}/predict",
                json={"file": to_b64(file_path), "kwargs": {}},
            )
            if response.status_code != 200:
                logger.error(f"远程解析失败：{response.status_code} - {response.text}")
                return ""

            json_data = response.json()
            result_url = json_data.get("result_url")
            if not result_url:
                logger.warning("返回结果中未包含 result_url")
                return ""

            content = get_remote_markdown(result_url)
            logger.info(f"📄 MinerU OCR解析完成，耗时：{time() - start:.2f}s")
            return content
        except Exception as e:
            logger.exception(f"MineruOCR解析异常：{e}")
            return ""
