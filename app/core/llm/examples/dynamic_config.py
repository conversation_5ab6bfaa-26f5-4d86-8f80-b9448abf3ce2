# -*- encoding: utf-8 -*-
"""
@File    :dynamic_config.py
@Time    :2024/3/26 15:45
<AUTHOR>
"""

from app.core.llm.manager import LLMManager
import asyncio


async def example_dynamic_config():
    # 1. 使用默认配置获取客户端
    default_client = LLMManager.get_client("openai")
    response = await default_client.aask("你好，这是默认配置")
    print(f"默认配置响应: {response}")

    # 2. 使用新配置更新客户端
    new_config = {
        "provider": "openai",
        "base_url": "https://api.openai.com/v1",
        "api_key": "your-new-api-key",
        "model": "gpt-4",
        "timeout": 90,
        "max_retries": 5
    }

    updated_client = LLMManager.update_config("openai", new_config)
    response = await updated_client.aask("你好，这是新配置")
    print(f"新配置响应: {response}")

    # 3. 直接使用配置创建新的客户端
    deepseek_config = {
        "provider": "deepseek",
        "base_url": "https://api.deepseek.com/v1",
        "api_key": "your-deepseek-api-key",
        "model": "deepseek-chat",
        "timeout": 60
    }

    deepseek_client = LLMManager.get_client(config=deepseek_config)
    response = await deepseek_client.aask("你好，这是DeepSeek配置")
    print(f"DeepSeek配置响应: {response}")


if __name__ == "__main__":
    asyncio.run(example_dynamic_config())
