# -*- encoding: utf-8 -*-
"""
@File    :config.py
@Time    :2024/3/26 15:45
<AUTHOR>
"""

import os
import re
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel
from loguru import logger


class LLMConfig(BaseModel):
    provider: str
    base_url: str
    api_key: Optional[str]
    model: str
    embeddings_model: Optional[str] = None
    timeout: int = 60
    max_retries: int = 3
    rate_limit: Dict[str, int] = {"requests_per_minute": 60}

    class Config:
        extra = "allow"

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "LLMConfig":
        required = {"provider", "base_url", "model", "api_key", "temperature", "max_tokens", "timeout"}
        missing = required - set(config_dict)
        if missing:
            raise ValueError(f"配置缺少字段: {missing}")
        return cls(**config_dict)


def resolve_env_vars(text: str) -> str:
    """支持多个 ${ENV_VAR[:default]} 替换"""
    if not isinstance(text, str):
        return text

    pattern = re.compile(r"\$\{(\w+)(?::([^}]*))?\}")
    return pattern.sub(lambda m: os.environ.get(m[1], m[2] or ""), text)


def resolve_config_values(config: Any) -> Any:
    """递归替换 dict/list/str 中的环境变量"""
    if isinstance(config, dict):
        return {k: resolve_config_values(v) for k, v in config.items()}
    elif isinstance(config, list):
        return [resolve_config_values(i) for i in config]
    elif isinstance(config, str):
        return resolve_env_vars(config)
    else:
        return config


def load_yaml_config(path: str) -> Dict[str, Any]:
    try:
        with open(path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        logger.error(f"加载配置失败: {path}, 错误: {e}")
        return {}


def load_llm_config(provider: Optional[str] = None) -> LLMConfig:
    provider = provider or os.environ.get("LLM_ENGINE", "openai")
    config_file = Path(__file__).parent.parent.parent / "configs" / "llm.yaml"
    config_data = load_yaml_config(str(config_file))

    config_data = resolve_config_values(config_data)

    if provider not in config_data:
        logger.warning(f"{provider} 未配置，使用环境变量作为默认")
        fallback = {
            "provider": provider,
            "base_url": os.getenv("DEFAULT_LLM_API_URL", ""),
            "api_key": os.getenv("DEFAULT_LLM_API_KEY", ""),
            "model": os.getenv("DEFAULT_LLM_MODEL", "gpt-3.5-turbo"),
            "timeout": int(os.getenv("LLM_TIMEOUT", 60)),
            "max_retries": int(os.getenv("LLM_MAX_RETRIES", 3)),
            "rate_limit": {"requests_per_minute": int(os.getenv("LLM_RATE_LIMIT", 60))}
        }
        return LLMConfig.from_dict(fallback)

    return LLMConfig.from_dict(config_data[provider])


