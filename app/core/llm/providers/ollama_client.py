# -*- encoding: utf-8 -*-
"""
@File   :ollama_client.py
@Time   :2025/5/30 14:51
<AUTHOR>
"""
from typing import Optional, List, Dict, Generator

from app.api.chat.chat_server import ChatResponse
from app.core.llm.providers.base import BaseLLMClient


class OllamaClient(BaseLLMClient):
    def __init__(
        self, base_url: str, api_key: Optional[str], model: str, timeout: int = 60
    ):
        super().__init__("ollama", base_url, api_key, model, timeout)


    def chat(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.5,
        max_tokens: int = 2048,
        **kwargs
    ) -> ChatResponse:
        pass

    def chat_stream(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.5,
        max_tokens: int = 2048,
        **kwargs
    ) -> Generator[str, None, None]:
        pass
