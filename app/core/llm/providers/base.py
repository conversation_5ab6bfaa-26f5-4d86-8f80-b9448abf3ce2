# -*- encoding: utf-8 -*-
"""
@File    :base.py
@Time    :2024/3/26 15:45
<AUTHOR>
"""

from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Generator, Callable, AsyncGenerator
from pydantic import BaseModel

from app.core.llm.monitor.base import <PERSON><PERSON><PERSON>
from app.core.llm.monitor.console_monitor import <PERSON>soleLoggerMoni<PERSON>
from app.core.llm.monitor.monitor_manager import MonitorManager
from app.configs.config import settings


class PromptTokensDetails(BaseModel):
    cached_tokens: Optional[int] = 0


class UsageInfo(BaseModel):
    """Token使用信息"""

    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    prompt_cache_hit_tokens: Optional[int] = 0
    prompt_cache_miss_tokens: Optional[int] = 0
    prompt_tokens_details: Optional[PromptTokensDetails] = None

    class Config:
        extra = "allow"


class ChatResponse(BaseModel):
    """聊天响应"""

    content: str
    usage: Optional[UsageInfo] = None


class BaseLLMClient(ABC):
    def __init__(
        self,
        provider: str,
        base_url: str,
        api_key: Optional[str],
        model: str,
        timeout: int = 60,
        enable_monitor: bool = settings.enable_monitor,
        temperature: float = 0.5,
        max_tokens: int = 2048,
    ):
        self.provider = provider
        self.base_url = base_url
        self.api_key = api_key
        self.model = model
        self.timeout = timeout
        self.enable_monitor = enable_monitor
        self.temperature = temperature
        self.max_tokens = max_tokens

        self._last_call_params: dict = {}  # 记录最近一次调用参数

    @staticmethod
    def usage_to_dict(usage_info: UsageInfo) -> dict:
        """统一输出所有 usage 字段"""
        return {
            "prompt_tokens": usage_info.prompt_tokens,
            "completion_tokens": usage_info.completion_tokens,
            "total_tokens": usage_info.total_tokens,
            "cache_hit_tokens": getattr(usage_info, "prompt_cache_hit_tokens", 0)
            or getattr(usage_info, "model_extra", {}).get("prompt_cache_hit_tokens", 0),
            "cache_miss_tokens": getattr(usage_info, "prompt_cache_miss_tokens", 0)
            or getattr(usage_info, "model_extra", {}).get(
                "prompt_cache_miss_tokens", 0
            ),
            "cached_tokens": getattr(
                getattr(usage_info, "prompt_tokens_details", None), "cached_tokens", 0
            ),
        }

    @abstractmethod
    def chat(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.5,
        max_tokens: int = 2048,
        **kwargs
    ) -> ChatResponse:
        """非流式对话接口，返回完整响应"""

    @abstractmethod
    def chat_stream(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.5,
        max_tokens: int = 2048,
        **kwargs
    ) -> Generator[str, None, None]:
        """流式对话接口，返回响应片段生成器"""

    @abstractmethod
    async def chat_stream_async(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.5,
        max_tokens: int = 2048,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """异步流式对话接口，返回响应片段的异步生成器"""

    def get_model_info(self):
        return {
            "provider": self.provider,
            "model": self.model,
            "base_url": self.base_url,
            "api_key": self.api_key,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "timeout": self.timeout,
            "last_call_params": self._last_call_params,
        }

    def _run_with_monitor(
        self,
        messages: List[Dict[str, str]],
        fn: Callable,
        prompt: Optional[str] = None,
        is_stream: bool = False,
        **kwargs
    ):
        # 记录调用参数但不修改默认配置
        self._last_call_params = {
            "temperature": kwargs.get("temperature", self.temperature),
            "max_tokens": kwargs.get("max_tokens", self.max_tokens),
            **kwargs,
        }

        if not self.enable_monitor:
            return fn(messages, **kwargs)

        event = LLMEvent(
            model=self.model,
            provider=self.provider,
            messages=messages,
            prompt=prompt,
        )
        MonitorManager.register(ConsoleLoggerMonitor())
        MonitorManager.before(event)

        try:
            if is_stream:
                result_gen = fn(messages, **kwargs)
                chunks = []

                def monitored_generator():
                    try:
                        for chunk in result_gen:
                            chunks.append(chunk)
                            yield chunk
                        event.response = "".join(chunks)
                        event.success = True
                    except Exception as e:
                        event.success = False
                        event.error = str(e)
                        raise
                    finally:
                        MonitorManager.after(event)

                return monitored_generator()

            else:
                result = fn(messages, **kwargs)
                if isinstance(result, ChatResponse):
                    event.response = result.content
                    event.usage = (
                        self.usage_to_dict(result.usage) if result.usage else None
                    )
                event.success = True
                MonitorManager.after(event)
                return result

        except Exception as e:
            event.success = False
            event.error = str(e)
            MonitorManager.after(event)
            raise

    async def _run_with_monitor_async(
            self,
            messages: List[Dict[str, str]],
            fn: Callable[..., AsyncGenerator[str, None]],
            prompt: Optional[str] = None,
            **kwargs
    ) -> AsyncGenerator[str, None]:
        self._last_call_params = {
            "temperature": kwargs.get("temperature", self.temperature),
            "max_tokens": kwargs.get("max_tokens", self.max_tokens),
            **kwargs,
        }
        if not self.enable_monitor:
            async for chunk in fn(messages, **kwargs):
                yield chunk
            return

        event = LLMEvent(
            model=self.model,
            provider=self.provider,
            messages=messages,
            prompt=prompt,
        )
        MonitorManager.register(ConsoleLoggerMonitor())
        MonitorManager.before(event)

        chunks = []
        try:
            async for chunk in fn(messages, **kwargs):
                chunks.append(chunk)
                yield chunk

            event.response = "".join(chunks)
            event.success = True

        except Exception as e:
            event.success = False
            event.error = str(e)
            raise

        finally:
            MonitorManager.after(event)
