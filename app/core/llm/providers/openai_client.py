# -*- encoding: utf-8 -*-
"""
@File    :openai_client.py
@Time    :2024/3/26 15:45
<AUTHOR>
"""

import openai
from typing import Optional, List, Dict, Generator, AsyncGenerator

from app.core.llm.providers.base import BaseLLMClient, ChatResponse, UsageInfo
from app.core.llm.retry_handler import sync_retry


class OpenAIClient(BaseLLMClient):


    def __init__(
        self,
        base_url: str,
        api_key: Optional[str],
        model: str,
        timeout: int = 60,
        temperature: float = 0.5,  # ✅ 支持初始化自定义
        max_tokens: int = 2048,  # ✅ 支持初始化自定义
    ):
        super().__init__(
            provider="openai",
            base_url=base_url,
            api_key=api_key,
            model=model,
            timeout=timeout,
            temperature=temperature,
            max_tokens=max_tokens,
        )
        self.client = openai.OpenAI(
            base_url=self.base_url,
            api_key=self.api_key,
            timeout=self.timeout,
        )
        self.async_client = openai.AsyncClient(
            base_url=self.base_url,
            api_key=self.api_key,
            timeout=self.timeout,
        )

    @sync_retry()
    def chat(
        self,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs,
    ) -> ChatResponse:
        # ✅ fallback 到默认配置
        temperature = temperature if temperature is not None else self.temperature
        max_tokens = max_tokens if max_tokens is not None else self.max_tokens

        def core_logic(msgs, **kw):
            response = self.client.chat.completions.create(
                model=self.model,
                messages=msgs,
                stream=False,
                **kw,
            )

            content = response.choices[0].message.content
            usage_data = response.usage

            return ChatResponse(
                content=content,
                usage=UsageInfo(
                    prompt_tokens=usage_data.prompt_tokens,
                    completion_tokens=usage_data.completion_tokens,
                    total_tokens=usage_data.total_tokens,
                ),
            )

        return self._run_with_monitor(
            messages,
            core_logic,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs,
        )

    @sync_retry()
    def chat_stream(
        self,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs,
    ) -> Generator[str, None, None]:
        temperature = temperature if temperature is not None else self.temperature
        max_tokens = max_tokens if max_tokens is not None else self.max_tokens

        def core_stream_logic(msgs, **kw):
            response = self.client.chat.completions.create(
                model=self.model,
                messages=msgs,
                stream=True,
                **kw,
            )

            thinking_opened = False
            answering_started = False

            for chunk in response:
                if not chunk.choices:
                    continue

                delta = chunk.choices[0].delta

                if hasattr(delta, "reasoning_content") and delta.reasoning_content:
                    if not thinking_opened:
                        yield "<think>"
                        thinking_opened = True
                    yield delta.reasoning_content
                    continue

                if hasattr(delta, "content") and delta.content:
                    if thinking_opened:
                        yield "</think>\n"
                        thinking_opened = False
                    if not answering_started:
                        answering_started = True
                    yield delta.content
                    continue

                if hasattr(chunk, "usage") and chunk.usage:
                    usage = UsageInfo(**chunk.usage.to_dict())
                    yield f"<usage>{usage}</usage>"

            if thinking_opened:
                yield "</think>\n"

        return self._run_with_monitor(
            messages,
            core_stream_logic,
            is_stream=True,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs,
        )

    async def chat_stream_async(
            self,
            messages: List[Dict[str, str]],
            temperature: Optional[float] = None,
            max_tokens: Optional[int] = None,
            **kwargs,
    ) -> AsyncGenerator[str, None]:
        temperature = temperature if temperature is not None else self.temperature
        max_tokens = max_tokens if max_tokens is not None else self.max_tokens

        async def core_stream_logic(msgs, **kw) -> AsyncGenerator[str, None]:
            response = await self.async_client.chat.completions.create(
                model=self.model,
                messages=msgs,
                stream=True,
                **kw,
            )

            thinking_opened = False
            answering_started = False

            async for chunk in response:
                if not chunk.choices:
                    continue

                delta = chunk.choices[0].delta

                # 思考过程
                if hasattr(delta, "reasoning_content") and delta.reasoning_content:
                    if not thinking_opened:
                        yield "<think>"
                        thinking_opened = True
                    yield delta.reasoning_content
                    continue

                # 正式回答
                if hasattr(delta, "content") and delta.content:
                    if thinking_opened:
                        yield "</think>\n"
                        thinking_opened = False
                    if not answering_started:
                        answering_started = True
                    yield delta.content
                    continue

                # 使用信息
                if hasattr(chunk, "usage") and chunk.usage:
                    usage = UsageInfo(**chunk.usage.to_dict())
                    yield f"<usage>{usage}</usage>"

            if thinking_opened:
                yield "</think>\n"

        # 包装监控
        return self._run_with_monitor_async(
            messages,
            core_stream_logic,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs,
        )


if __name__ == "__main__":
    client = OpenAIClient(
        base_url="http://idc-vllm.bdo.com.cn/v1",
        api_key="q5A53chTPgb4wkMHZhZUH2c1wd4ph6Aw",
        model="QwQ-32B",
    )
    message = [{"role": "user", "content": "你好"}]
    print(client.chat(messages=message))

    # message = [{"role": "user", "content": "你好"}, {"role": "assistant", "content": "你好！有什么问题我可以帮助你吗？"}, {"role": "user", "content": "你好"}]
    # for chunk in client.chat_stream(
    #         messages=message
    # ):
    #     print(chunk, end="", flush=True)
