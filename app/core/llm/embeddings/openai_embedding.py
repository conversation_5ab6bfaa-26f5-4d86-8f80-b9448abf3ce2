# -*- encoding: utf-8 -*-
"""
@File   :openai_embedding.py
@Time   :2025/5/30 17:05
<AUTHOR>
"""
# app/core/llm/embeddings/openai_embedding.py
import openai
from app.core.llm.embeddings.base import BaseEmbeddingClient, EmbeddingResponse


class OpenAIEmbeddingClient(BaseEmbeddingClient):
    def __init__(self, model, api_key, base_url, timeout=60):
        super().__init__(model, api_key, base_url, timeout)
        self.client = openai.OpenAI(api_key=api_key, base_url=base_url, timeout=timeout)

    def embed(self, texts):
        response = self.client.embeddings.create(model=self.model, input=texts)
        return EmbeddingResponse(
            embeddings=[item.embedding for item in response.data],
            model=self.model,
            input_tokens=response.usage.total_tokens,
        )
