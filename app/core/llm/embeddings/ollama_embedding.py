# -*- encoding: utf-8 -*-
"""
@File   :ollama_embedding.py
@Time   :2025/5/30 17:10
<AUTHOR>
"""
from typing import List

import requests
from loguru import logger

from app.core.llm.tokenizer import tokenizer
from app.core.llm.embeddings.base import BaseEmbeddingClient, EmbeddingResponse


class OllamaEmbeddingClient(BaseEmbeddingClient):
    def embed(self, texts: List[str]) -> EmbeddingResponse:
        url = f"{self.base_url}/api/embed"
        # 构建请求头
        headers = {
            "Content-Type": "application/json"
        }
        payload = {
            "input": texts,
            "model": self.model,
            "prompt": None
        }

        try:
            response = requests.post(url, json=payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            embeddings = [data["embeddings"]] if isinstance(data["embeddings"][0], float) else data["embeddings"]
            tokens = self.count_total_tokens(texts, provider="openai_general")
            return EmbeddingResponse(embeddings=embeddings, model=self.model, input_tokens=tokens)

        except Exception as e:
            logger.error(f"Ollama embedding 请求失败: {e}")
            raise
