import os
from pathlib import Path
from typing import Dict, Any, Optional
import yaml
from pydantic import BaseModel
from loguru import logger


class EmbeddingConfig(BaseModel):
    """Embedding 配置模型"""
    provider: str
    base_url: str
    api_key: Optional[str]
    model: str
    timeout: int = 60
    max_retries: int = 3

    class Config:
        extra = "allow"

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "EmbeddingConfig":
        required_fields = {"provider", "base_url", "model"}
        missing = required_fields - config_dict.keys()
        if missing:
            raise ValueError(f"Embedding 配置缺少字段: {missing}")
        return cls(**config_dict)


def load_yaml_config(config_path: str) -> Dict[str, Any]:
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"加载 Embedding 配置失败: {e}")
        return {}


def resolve_env_vars(value: str) -> str:
    if not isinstance(value, str) or '${' not in value:
        return value

    start = value.find('${')
    end = value.find('}', start)
    if start != -1 and end != -1:
        raw = value[start + 2:end]
        if ':' in raw:
            env_var, default = raw.split(':', 1)
        else:
            env_var, default = raw, None
        resolved = os.environ.get(env_var, default)
        if resolved is None:
            logger.warning(f"环境变量 {env_var} 未定义，且无默认值")
            return value
        return value[:start] + resolved + value[end + 1:]

    return value


def resolve_config_values(config: Dict[str, Any]) -> Dict[str, Any]:
    resolved = {}
    for k, v in config.items():
        if isinstance(v, dict):
            resolved[k] = resolve_config_values(v)
        elif isinstance(v, str):
            resolved[k] = resolve_env_vars(v)
        else:
            resolved[k] = v
    return resolved


def load_embedding_config(provider: Optional[str] = None) -> EmbeddingConfig:
    provider = provider or os.environ.get("EMBEDDING_ENGINE", "ollama")
    config_dir = Path(__file__).parent.parent.parent.parent / "configs"
    config_path = config_dir / "embedding.yaml"

    raw = load_yaml_config(str(config_path))
    if not raw or provider not in raw:
        logger.warning(f"未找到 {provider} 的 embedding 配置，使用环境变量 fallback")
        raw = {
            provider: {
                "provider": provider,
                "base_url": os.environ.get("DEFAULT_EMBEDDING_API_URL", ""),
                "api_key": os.environ.get("DEFAULT_EMBEDDING_API_KEY", ""),
                "model": os.environ.get("DEFAULT_EMBEDDING_MODEL", "text-embedding-ada-002"),
                "timeout": int(os.environ.get("EMBEDDING_TIMEOUT", "60")),
                "max_retries": int(os.environ.get("EMBEDDING_MAX_RETRIES", "3"))
            }
        }

    parsed = resolve_config_values(raw)
    return EmbeddingConfig.from_dict(parsed[provider])
