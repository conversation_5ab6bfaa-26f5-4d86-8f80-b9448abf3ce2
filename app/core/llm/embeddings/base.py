# -*- encoding: utf-8 -*-
"""
@File   :base.py
@Time   :2025/5/30 17:03
<AUTHOR>
"""
from abc import ABC, abstractmethod
from typing import List
from pydantic import BaseModel

from app.core.llm.tokenizer import tokenizer


class EmbeddingResponse(BaseModel):
    embeddings: List[List[float]]
    model: str
    input_tokens: int = None


class BaseEmbeddingClient(ABC):
    def __init__(self, model: str, api_key: str, base_url: str, timeout: int = 60):
        self.model = model
        self.api_key = api_key
        self.base_url = base_url
        self.timeout = timeout

    @abstractmethod
    def embed(self, texts: List[str]) -> EmbeddingResponse:
        """同步嵌入向量接口"""

    @staticmethod
    def count_total_tokens(
            texts: List[str], provider: str, model_name: str = None
    ) -> int:
        return sum(
            tokenizer.get_token_length(text, provider=provider, model_name=model_name)
            for text in texts
        )
