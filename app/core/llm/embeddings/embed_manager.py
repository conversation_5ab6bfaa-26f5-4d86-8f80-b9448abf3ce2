# -*- encoding: utf-8 -*-
"""
@File   :manager.py
@Time   :2025/5/30 17:05
<AUTHOR>
"""

from typing import Dict, Type, Optional, Any
from loguru import logger

from app.core.llm.embeddings.base import BaseEmbeddingClient
from app.core.llm.embeddings.config import EmbeddingConfig, load_embedding_config
from app.core.llm.embeddings.ollama_embedding import OllamaEmbeddingClient
from app.core.llm.embeddings.openai_embedding import OpenAIEmbeddingClient


class EmbeddingManager:
    _instances: Dict[str, BaseEmbeddingClient] = {}
    _providers: Dict[str, Type[BaseEmbeddingClient]] = {
        "openai": OpenAIEmbeddingClient,
        "ollama": OllamaEmbeddingClient,
    }

    @classmethod
    def register_provider(cls, name: str, client_class: Type[BaseEmbeddingClient]):
        cls._providers[name] = client_class
        logger.info(f"Embedding provider '{name}' registered")

    @classmethod
    def get_client(cls, provider: Optional[str] = None, config: Optional[Dict[str, Any]] = None) -> BaseEmbeddingClient:
        if config:
            embedding_config = EmbeddingConfig.from_dict(config)
            provider = embedding_config.provider
        else:
            embedding_config = load_embedding_config(provider)
            provider = embedding_config.provider

        if provider not in cls._instances:
            if provider not in cls._providers:
                raise ValueError(f"未注册的Embedding提供者: {provider}")
            cls._instances[provider] = cls._providers[provider](
                base_url=embedding_config.base_url,
                api_key=embedding_config.api_key,
                model=embedding_config.model,
                timeout=embedding_config.timeout,
            )
        return cls._instances[provider]

    @classmethod
    def update_config(cls, provider: str, config: Dict[str, Any]) -> BaseEmbeddingClient:
        if provider in cls._instances:
            del cls._instances[provider]
        return cls.get_client(provider, config)

    @classmethod
    def clear_instances(cls):
        cls._instances.clear()


if __name__ == "__main__":
    # 测试代码
    # 创建一个EmbeddingManager实例
    manager = EmbeddingManager()

    # 获取一个EmbeddingClient实例
    client = manager.get_client("ollama")

    print(client.embed(["hello world"]))
