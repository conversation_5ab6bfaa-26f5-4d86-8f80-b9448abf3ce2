# -*- encoding: utf-8 -*-
"""
@File    :tokenizer.py
@Time    :2024/3/26 15:45
<AUTHOR>
"""
import tiktoken
from transformers import AutoTokenizer
from typing import Optional


class TokenizerManager:
    _instance = None
    _tokenizers = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(TokenizerManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.initialized = True

    def get_token_length(self, text: str, provider: str = "openai", model_name: Optional[str] = None) -> int:
        """获取文本的token长度"""
        if provider == "openai":
            return self._openai_token_len(text, model_name or "gpt-3.5-turbo")
        elif provider == "qwen":
            return self._qwen_token_len(text, model_name or 'Qwen/QwQ-32B')
        elif provider == "deepseek":
            return self._deepseek_token_len(text)
        elif provider == 'openai_general':
            return self._openai_general_token_len(text, model_name)
        else:
            return self._simple_token_len(text)

    def _openai_token_len(self, text: str, model_name: str = "gpt-3.5-turbo") -> int:
        """OpenAI token长度计算"""
        try:
            import tiktoken
            if model_name not in self._tokenizers:
                self._tokenizers[model_name] = tiktoken.encoding_for_model(model_name)
            return len(self._tokenizers[model_name].encode(text))
        except:
            return self._simple_token_len(text)

    def _qwen_token_len(self, text: str, model_name: str = 'Qwen/QwQ-32B') -> int:
        """千问模型token长度计算"""
        try:
            if model_name not in self._tokenizers:
                self._tokenizers[model_name] = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
            return len(self._tokenizers[model_name].tokenize(text))
        except:
            return self._simple_token_len(text)

    def _deepseek_token_len(self, text: str, model_name: str = "deepseek-chat") -> int:
        """DeepSeek模型token长度计算"""
        try:
            if 'deepseek' not in self._tokenizers:
                self._tokenizers['deepseek'] = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
            return len(self._tokenizers[model_name].tokenize(text))
        except:
            return self._simple_token_len(text)

    @staticmethod
    def _simple_token_len(text: str) -> int:
        """简单的token长度估算"""
        return max(1, len(text) // 4)

    def _openai_general_token_len(self, text: str, model_name: str = "cl100k_base"):
        try:
            if 'openai_general' not in self._tokenizers:
                self._tokenizers['openai_general'] = tiktoken.encoding_for_model(model_name)
            return len(self._tokenizers[model_name].encode(text))
        except:
            return self._simple_token_len(text)


# 全局单例
tokenizer = TokenizerManager()

