# -*- encoding: utf-8 -*-
"""
@File    :manager.py
@Time    :2024/3/26 15:45
<AUTHOR>
"""
from loguru import logger
from typing import Dict, Type, Optional, Any

from app.core.llm.config import LLMConfig, load_llm_config
from app.core.llm.providers.base import Base<PERSON><PERSON>lient
from app.core.llm.providers.openai_client import OpenAIClient


class LLMManager:
    _instances: Dict[str, BaseLLMClient] = {}
    _providers = {
        "openai": OpenAIClient,
        # 其他提供者在这里注册
    }

    @classmethod
    def register_provider(cls, name: str, provider_class: Type[BaseLLMClient]):
        """注册新的LLM提供者"""
        cls._providers[name] = provider_class
        logger.info(f"Registered LLM provider: {name}")

    @classmethod
    def get_client(cls, provider: str = None, config: Optional[Dict[str, Any]] = None) -> BaseLLMClient:
        """获取LLM客户端实例

        Args:
            provider: LLM提供者名称
            config: 可选的配置字典，如果提供则使用此配置而不是从文件加载

        Returns:
            LLM客户端实例
        """
        if config:
            llm_config = LLMConfig.from_dict(config)
            provider = llm_config.provider
        else:
            llm_config = load_llm_config(provider)
        print_llm_confg = llm_config.model_dump()
        print_llm_confg["api_key"] = '******'
        logger.info(f"LLM config: {print_llm_confg}")
        if provider not in cls._instances:
            if provider not in cls._providers:
                raise ValueError(f"未注册的LLM提供者: {provider}")

            provider_class = cls._providers[provider]
            cls._instances[provider] = provider_class(
                base_url=llm_config.base_url,
                api_key=llm_config.api_key,
                model=llm_config.model,
                timeout=llm_config.timeout,
            )

        return cls._instances[provider]

    @classmethod
    def update_config(cls, provider: str, config: Dict[str, Any]) -> BaseLLMClient:
        """更新LLM配置并重新初始化客户端

        Args:
            provider: LLM提供者名称
            config: 新的配置字典

        Returns:
            更新后的LLM客户端实例
        """
        # 验证并创建新配置
        llm_config = LLMConfig.from_dict(config)

        # 确保provider匹配
        if llm_config.provider != provider:
            raise ValueError(f"配置中的provider({llm_config.provider})与指定的provider({provider})不匹配")

        # 如果存在旧实例，先清除
        if provider in cls._instances:
            del cls._instances[provider]

        # 使用新配置创建实例
        return cls.get_client(provider=provider, config=config)

    @classmethod
    def clear_instances(cls):
        """清除所有实例"""
        cls._instances.clear()


if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    llm = LLMManager.get_client('openai')
    print(llm.chat([{"role": "user", "content": "你好"}], temperature=0.5))
