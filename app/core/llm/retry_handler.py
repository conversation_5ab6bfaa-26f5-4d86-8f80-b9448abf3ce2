# -*- encoding: utf-8 -*-
"""
@File    :retry_handler.py
@Time    :2024/3/26 15:45
<AUTHOR>
"""

import asyncio
import time
from functools import wraps
from typing import TypeVar, Callable, Any
from loguru import logger

T = TypeVar("T")


def async_retry(
        max_retries: int = 3,
        initial_delay: float = 1.0,
        max_delay: float = 60.0,
        backoff_factor: float = 2.0,
        exceptions: tuple = (Exception,),
):
    """异步重试装饰器

    Args:
        max_retries: 最大重试次数
        initial_delay: 初始延迟时间(秒)
        max_delay: 最大延迟时间(秒)
        backoff_factor: 退避因子
        exceptions: 需要重试的异常类型

    Returns:
        装饰器函数
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            delay = initial_delay
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e

                    if attempt == max_retries:
                        logger.error(
                            f"Failed after {max_retries} retries. Last error: {str(e)}"
                        )
                        raise

                    # 计算下一次重试的延迟时间
                    delay = min(delay * backoff_factor, max_delay)

                    logger.warning(
                        f"Attempt {attempt + 1}/{max_retries} failed: {str(e)}. "
                        f"Retrying in {delay:.1f} seconds..."
                    )

                    # 等待后重试
                    await asyncio.sleep(delay)

            # 不应该到达这里，但为了类型检查完整性
            raise last_exception or Exception("Unexpected retry failure")

        return wrapper

    return decorator


def sync_retry(
        max_retries: int = 3,
        initial_delay: float = 1.0,
        max_delay: float = 60.0,
        backoff_factor: float = 2.0,
        exceptions: tuple = (Exception,),
) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """
    同步重试装饰器

    Args:
        max_retries: 最大重试次数
        initial_delay: 初始延迟时间（秒）
        max_delay: 最大等待时间
        backoff_factor: 退避倍数
        exceptions: 要捕获并重试的异常类型

    Returns:
        可装饰同步函数的重试逻辑
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            delay = initial_delay
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e

                    if attempt == max_retries:
                        logger.error(
                            f"Failed after {max_retries} retries. Last error: {str(e)}"
                        )
                        raise

                    logger.warning(
                        f"Attempt {attempt + 1}/{max_retries} failed: {str(e)}. "
                        f"Retrying in {delay:.1f} seconds..."
                    )

                    time.sleep(delay)
                    delay = min(delay * backoff_factor, max_delay)

            raise last_exception or Exception("Unexpected retry failure")

        return wrapper

    return decorator
