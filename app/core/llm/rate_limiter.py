# -*- encoding: utf-8 -*-
"""
@File    :rate_limiter.py
@Time    :2024/3/26 15:31
<AUTHOR>
"""

import asyncio
import threading
import time
from datetime import datetime
from functools import wraps


class RateLimiter:
    def __init__(self, rate: int, per: float = 60.0):
        """
        :param rate: 允许的最大请求数
        :param per: 时间窗口（秒），默认 60s
        """
        self.rate = rate
        self.per = per
        self.tokens = rate
        self.last_check = time.monotonic()
        self._async_lock = asyncio.Lock()
        self._sync_lock = threading.Lock()

    def _refill_sync(self):
        now = time.monotonic()
        elapsed = now - self.last_check
        self.last_check = now
        refill_tokens = (elapsed / self.per) * self.rate
        self.tokens = min(self.rate, self.tokens + refill_tokens)

    def acquire_sync(self):
        with self._sync_lock:
            self._refill_sync()
            if self.tokens >= 1:
                self.tokens -= 1
                return
            sleep_time = self.per / self.rate
        time.sleep(sleep_time)  # 注意要在锁外 sleep
        with self._sync_lock:
            self._refill_sync()
            self.tokens = max(0, self.tokens - 1)

    async def _refill_async(self):
        now = time.monotonic()
        elapsed = now - self.last_check
        self.last_check = now
        refill_tokens = (elapsed / self.per) * self.rate
        self.tokens = min(self.rate, self.tokens + refill_tokens)

    async def acquire(self):
        async with self._async_lock:
            await self._refill_async()
            if self.tokens >= 1:
                self.tokens -= 1
                return
        # 在锁外 sleep，再次尝试
        await asyncio.sleep(self.per / self.rate)
        async with self._async_lock:
            await self._refill_async()
            self.tokens = max(0, self.tokens - 1)


def rate_limit(rate: int = 60, per: float = 60.0):
    """
    一个装饰器工厂，用于限制函数执行的速率。

    参数:
    - rate: int, 默认60。在指定时间范围内允许的最大调用次数。
    - per: float, 默认60.0。指定的时间范围，单位为秒。

    返回:
    - 一个装饰器，用于应用速率限制到函数上。

    用途:
    - 防止函数在短时间内被频繁调用，常用于API调用限制、防止暴力破解等场景。
    """

    # 初始化RateLimiter实例，配置请求速率和时间范围
    limiter = RateLimiter(rate, per)

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):

            # 尝试获取速率限制器的许可，如果未达到限制则继续执行
            await limiter.acquire()
            # 执行原函数并返回结果
            return await func(*args, **kwargs)

        return wrapper

    return decorator


def rate_limit_sync(rate: int = 60, per: float = 60.0):
    # 初始化RateLimiter实例，配置请求速率和时间范围
    limiter = RateLimiter(rate, per)

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            loop = asyncio.get_event_loop()
            loop.run_until_complete(limiter.acquire())
            return func(*args, **kwargs)

        return wrapper

    return decorator
