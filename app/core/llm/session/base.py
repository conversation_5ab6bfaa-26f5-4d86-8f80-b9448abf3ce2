# -*- encoding: utf-8 -*-
"""
@File   :base.py
@Time   :2025/6/3 16:13
<AUTHOR>
"""
import json
import re
import threading
import uuid
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any, Generator, Union

from loguru import logger

from app.core.db.manager import DBManager
from app.core.llm.manager import LLMManager
from app.core.llm.session.model import Session, Message
from app.core.memory.manager import MemoryManager
from app.core.memory.config import MemoryConfig
from app.core.llm.providers.base import ChatResponse
from app.configs.config import settings
from app.core.security.crypto import CryptoManager


class SessionManager:
    _initialized = False
    _lock = threading.Lock()

    # ✅ 所有资源都作为类属性统一缓存
    storage = None
    session_collection = None
    llm_client = None
    memory_manager = None
    llm_manager = None
    enable_summary = True

    def __init__(self, *args, **kwargs):
        with SessionManager._lock:
            if not SessionManager._initialized:
                logger.info("SessionManager 初始化...")
                DBManager.initialize(mongo_config=settings.get_mongo_config,
                                     mysql_config=settings.get_mysql_config,
                                     redis_config=settings.get_redis_config)
                SessionManager.storage = DBManager.get_mongo()
                SessionManager.session_collection = SessionManager.storage.get_collection("sessions")

                SessionManager.llm_manager = LLMManager()
                SessionManager.llm_client = SessionManager.llm_manager.get_client(settings.default_llm_provider)

                SessionManager.memory_manager = MemoryManager(
                    MemoryConfig(),
                    self,
                    llm_client=SessionManager.llm_client
                )
                SessionManager._initialized = True

        # ✅ 实例属性从类变量赋值，确保每个实例都能访问
        self.storage = SessionManager.storage
        self.session_collection = SessionManager.session_collection
        self.llm_client = SessionManager.llm_client
        self.llm_manager = SessionManager.llm_manager
        self.memory_manager = SessionManager.memory_manager

    @classmethod
    def get_instance(cls):
        if not cls._initialized:
            cls()
        return cls()

    def _switch_llm_configuration(self, session_id: str):
        """
        切换llm配置
        Args:
            session_id: 会话ID

        Returns:

        """
        session = self.get_session(session_id)
        llm_config = session.metadata.get("llm_config", {})
        encrypted_api_key = llm_config.get("api_key")

        if not encrypted_api_key:
            raise ValueError("未找到加密的 API Key")

        crypto = CryptoManager()
        llm_config["api_key"] = crypto.decrypt(encrypted_api_key)
        self.llm_manager.update_config(llm_config.pop("provider"), llm_config)

    def _build_prompt_messages(self, session_id: str, user_message: str) -> List[Dict[str, str]]:
        session = self.get_session(session_id)
        session_config = self.memory_manager.mongo_storage._get_current_metadata(session_id)
        history = self.memory_manager.get_messages(session_id)

        messages = []

        if session.history_summary:
            messages.append({
                "role": "system",
                "content": f"以下是会话摘要，请参考其继续回答：\n{session.history_summary}"
            })

        prompt = session_config.get("system_prompt")
        if prompt:
            messages.append({"role": "system", "content": prompt})

        messages += [{"role": msg.role, "content": msg.content} for msg in history]
        messages.append({"role": "user", "content": user_message})

        return messages

    def chat(
            self,
            session_id: str,
            message: str,
            stream: bool = False,
            **kwargs
    ) -> Union[ChatResponse, Generator[str, None, None]]:
        if not self.llm_client:
            raise ValueError("LLM client not set")

        session_config = self.memory_manager.mongo_storage._get_current_metadata(session_id)
        llm_params = {
            "temperature": kwargs.pop("temperature", session_config.get("temperature", 0.7)),
            "max_tokens": kwargs.pop("max_tokens", session_config.get("max_tokens", 2048)),
            **kwargs
        }

        messages = self._build_prompt_messages(session_id, message)
        self.add_message(session_id, "user", message, metadata={"mode": "chat"})

        if stream:
            response_stream = self.llm_client.chat_stream(messages, **llm_params)

            def process_stream():
                full_response = []
                for chunk in response_stream:
                    if isinstance(chunk, str):
                        full_response.append(chunk)
                        yield chunk

                complete_response = "".join(full_response)
                self.add_message(session_id, "assistant", complete_response, metadata={"mode": "chat"})

            return process_stream()
        else:
            response = self.llm_client.chat(messages, **llm_params)
            self.add_message(session_id, "assistant", response.content, metadata={"mode": "chat"})
            return response

    def add_message(self, session_id: str, role: str, content: str, metadata: Optional[Dict[str, Any]] = None):
        msg = Message(
            id=str(uuid.uuid4()),
            role=role,
            content=content,
            timestamp=datetime.now(),
            metadata=metadata or {}
        )
        self.memory_manager.add_message(session_id, msg)

        if self.enable_summary and role == "user":
            self.memory_manager._check_and_summarize_history(session_id)

    def create_session(
            self,
            user_id: str,
            name: Optional[str] = None,
            metadata: Optional[dict] = None,
            model: Optional[str] = None,
            system_prompt: Optional[str] = None
    ) -> str:
        session_id = str(uuid.uuid4())
        llm_config = self.llm_client.get_model_info()
        crypto = CryptoManager()
        encryption_api_key = crypto.encrypt(llm_config["api_key"])
        llm_config["api_key"] = encryption_api_key
        if isinstance(metadata, dict):
            metadata.update({"llm_config": llm_config})
        else:
            metadata = {"llm_config": llm_config}
        session = Session(
            session_id=session_id,
            user_id=user_id,
            name=name or f"会话-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            created_at=datetime.now(),
            updated_at=datetime.now(),
            metadata=metadata
        )
        self.session_collection.insert_one(session.model_dump())

        if model or system_prompt:
            self.memory_manager.update_session_config(
                session_id,
                model=model,
                system_prompt=system_prompt,
                reason="初始化配置"
            )

        return session_id

    def get_history_message(self, session_id: str,
                            role: Optional[str] = None,
                            keyword: Optional[str] = None) -> List[Message]:
        """
        获取历史消息
        Args:
            session_id: 会话ID
            role: 角色
            keyword: 关键字

        Returns:

        """
        return self.memory_manager.get_messages(session_id, role=role, keyword=keyword)

    def get_session(self, session_id: Optional[str] = None, field: str = None, user_id: Optional[str] = None) -> Union[
        Session, List[Session], dict, List[dict]]:
        """
        获取会话数据

        - 传入 session_id：获取单个会话（原有逻辑）
        - 传入 user_id：获取该用户的所有会话列表
        - 可选 field：返回指定字段（单个字段模式）
        """
        if session_id:
            query = {"session_id": session_id, "deleted": False}
            if field:
                projection = {field: 1, "_id": 0}
                session_data = self.session_collection.find_one(query, projection)
                if not session_data:
                    raise ValueError("Session not found")
                return session_data
            session_data = self.session_collection.find_one(query)
            return Session.model_validate(session_data) if session_data else None

        elif user_id:
            query = {"user_id": user_id, "deleted": False}
            session_cursor = self.session_collection.find(query)
            session_list = [Session.model_validate(item) for item in session_cursor]
            return session_list

        else:
            raise ValueError("必须提供 session_id 或 user_id")

    def generate_session_title(self, question: str, answer: str) -> str:
        """
        根据第一轮对话自动生成会话标题

        Args:
            question: 用户的第一个问题
            answer: AI的第一个回答

        Returns:
            str: 生成的标题
        """
        prompt = f"""
                请根据以下用户问题和AI回答，生成一个简短且符合对话内容，一句话即可概括的标题，不超过20个字：
                【用户问题】
                {question}
                【AI回答】
                {answer}
                【遵循规则】
                {{"title": "xxx"}}
                """
        messages = [{"role": "user", "content": prompt}]
        title = question[:20] + "..."
        try:
            # 这里假设LLMClient有chat方法，非流式返回完整字符串
            response = self.llm_client.chat(messages)
            reply = response.content  # 或完整 LLM 回复内容
            match = re.search(r"\{.*?}", reply, re.DOTALL)
            if match:
                try:
                    data = json.loads(match.group())
                    title = data.get("title", title)
                except json.JSONDecodeError:
                    pass

        except ValueError:
            pass

        return title

    def ask(self, message: str, prompt: str = None) -> ChatResponse:
        """
        快速问答
        Args:
            message: 提问消息
            prompt: 提示词

        Returns:

        """
        message = [{"role": "user", "content": message}]
        if prompt:
            message.insert(0, {"role": "system", "content": prompt})
        return self.llm_client.chat(message)

    def delete_session(self, session_id: str):
        """
        删除会话记录 (物理删除,谨慎操作)
        Args:
            session_id: 会话ID

        Returns:

        """
        self.session_collection.delete_one({"session_id": session_id})

    def clear_history(self):
        """
        清空历史记录
        Returns:

        """
        pass

    def session_exists(self, session_id: str) -> bool:
        """
        会话是否存在
        Args:
            session_id: 会话ID

        Returns: True 或 False

        """
        return self.session_collection.find_one({"session_id": session_id}) is not None

    def reset_session_history(self, session_id: str) -> bool:
        try:
            reset_time = datetime.now()

            # 更新 Redis
            redis_conn = DBManager.get_redis().get_connection()
            redis_conn.set(f"memory_reset_at:{session_id}", reset_time.isoformat())

            # 更新 Mongo
            self.session_collection.update_one(
                {"session_id": session_id},
                {"$set": {"reset_at": reset_time}}
            )

            return True
        except Exception as e:
            logger.error(f"重置会话历史失败: {e}")
            return False

    def soft_delete_session(self, session_id: str):
        """
        逻辑删除会话记录 (标记为已删除)
        """
        self.session_collection.update_one(
            {"session_id": session_id},
            {"$set": {"deleted": True, "update_time": datetime.now()}}
        )


if __name__ == "__main__":
    s = SessionManager()
    s_id = '95a639b4-41db-4307-a321-be422e22afdb'

    print(s.get_history_message(s_id))
    # s_id = s.create_session("test_user")
    # print(s.ask("你好"))
    # print(s.llm_client.get_model_info())
    # print(s.get_session('41567878-1a88-40b1-8136-498b22adab29'))
    # print(s.get_session(s_id).doc_ids)
    # print(s.generate_session_title("你好", "你好，有什么可以帮你的"))
