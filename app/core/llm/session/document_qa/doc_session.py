# -*- encoding: utf-8 -*-
"""
@File   :doc_session.py
@Time   :2025/6/9 14:19
<AUTHOR>
"""
from loguru import logger
from typing import List, Dict, Tuple, BinaryIO
from datetime import datetime, timezone

from app.core.llm.prompts.doc_reader_prompt import build_doc_reader_prompt, build_deleted_doc_info
from app.core.llm.session.base import SessionManager
from app.core.llm.session.document_qa.file_manager import FileManager


class DocSessionManager(SessionManager):
    """
    面向“文档问答”的 SessionManager 派生类：
    - 支持上下文构建
    - 支持文档 QA 流式回复
    """

    def __init__(self, *args, file_manager: FileManager = None, **kwargs):
        super().__init__(*args, **kwargs)
        self.file_manager = file_manager or FileManager()

    def add_document_to_session(self, session_id: str, doc_id: str) -> bool:
        """
        将文档添加到会话

        Args:
            session_id: 会话ID
            doc_id: 文档ID

        Returns:
            bool: 操作是否成功
        """
        # 获取会话和文档信息
        session = self.get_session(session_id)
        if not session:
            return False

        doc_info = self.file_manager.get_file_info(doc_id)
        if not doc_info:
            return False

        # 检查文档是否已在会话中
        if doc_id in session.doc_ids:
            return True

        # 获取文档字符数和文件名
        char_count = doc_info.get("char_count", 0)
        file_name = doc_info.get("file_name", "")

        # 智能清理deleted_files中的相关文件记录
        # 当用户重新上传文件时，自动清理之前删除的相似文件记录，避免提示词冲突
        if file_name:
            cleaned_files = self._smart_clean_deleted_files(session_id, file_name)
            if cleaned_files:
                logger.info(f"智能清理了deleted_files中的相关文件记录: {cleaned_files} -> 新文件: {file_name}")

        # 更新会话
        self.session_collection.update_one(
            {"session_id": session_id},
            {
                "$push": {"doc_ids": doc_id},
                "$inc": {"total_chars": char_count},
                "$set": {"updated_at": datetime.now(timezone.utc)},
            },
        )
        return True

    def save_file_to_session(
        self, user_id: str, file: BinaryIO, filename: str, session_id: str
    ) -> str:
        """
        保存文件到指定会话

        Args:
            user_id: 用户ID
            file: 文件对象
            filename: 文件名
            session_id: 会话ID

        Returns:
            str: 文档ID
        """
        # 获取会话
        session = self.get_session(session_id)
        if not session:
            raise ValueError(f"会话 {session_id} 不存在")

        # 保存文件
        doc_id = self.file_manager.save_file(user_id, file, filename)

        # 添加文档到会话
        self.add_document_to_session(session_id, doc_id)

        return doc_id

    def save_file_to_session_with_content(
        self, user_id: str, filename: str, session_id: str, parsed_content: str,
        file_type: str = "", file_size: int = 0
    ) -> Tuple:
        """
        保存已解析的文件内容到指定会话（用于OCR任务）

        Args:
            user_id: 用户ID
            filename: 文件名
            session_id: 会话ID
            parsed_content: 已解析的内容
            file_type: 文件类型
            file_size: 文件大小

        Returns:
            str: 文档ID
        """
        import uuid
        from app.core.llm.session.document_qa.utils import get_local_datetime

        # 获取会话
        session = self.get_session(session_id)
        if not session:
            raise ValueError(f"会话 {session_id} 不存在")

        # 生成文档ID
        doc_id = str(uuid.uuid4())

        # 构建文件信息（不需要实际文件路径）
        file_info = {
            "doc_id": doc_id,
            "user_id": user_id,
            "file_name": filename,
            "file_path": "",  # OCR任务不需要本地文件路径
            "file_type": file_type,
            "file_size": file_size,
            "status": "extracted",  # 直接标记为已提取
            "created_at": get_local_datetime(),
            "updated_at": get_local_datetime(),
            "is_quantized": False,
            "is_vectorized": False,
            "char_count": len(parsed_content),
            "content": "",  #
        }

        # 保存文件信息到MongoDB
        self.file_manager.collection.insert_one(file_info)
        logger.info(f"已保存OCR解析内容到MongoDB: doc_id={doc_id}, 内容长度={len(parsed_content)}")

        # 处理文档内容（分块）
        try:
            chunks = self.file_manager.file_processor.process_content(
                parsed_content, filename, doc_id
            )

            # 更新文档信息
            self.file_manager.collection.update_one(
                {"doc_id": doc_id},
                {
                    "$set": {
                        "status": "processed",
                        "chunk_count": len(chunks),
                        "updated_at": get_local_datetime(),
                    }
                },
            )

            # 添加文档到会话
            self.add_document_to_session(session_id, doc_id)

            logger.info(f"OCR文档处理完成: doc_id={doc_id}, chunks={len(chunks)}")
            return doc_id, len(chunks)

        except Exception as e:
            logger.error(f"处理OCR文档内容失败: {e}")
            # 删除已创建的文档记录
            self.file_manager.collection.delete_one({"doc_id": doc_id})
            raise Exception(f"处理OCR文档内容失败: {e}")

    def get_session_context_for_query(self, session_id: str) -> Tuple[str, List[Dict]]:
        """
        获取会话中所有文档的上下文，包括向量化和非向量化的文档

        Args:
            session_id: 会话ID

        Returns:
            Tuple[str, List[Dict]]: 上下文文本和详细结果
        """
        session = self.get_session(session_id)
        if not session:
            return "", []

        doc_ids = session.doc_ids
        if not doc_ids:
            return "", []

        context_parts = []
        detailed_results = []
        total_chars = 0
        # 返回所有文档内容
        for idx, doc_id in enumerate(doc_ids, 1):
            if total_chars >= self.file_manager.max_token_len:
                break
            doc_info = self.file_manager.get_file_info(doc_id)
            doc_content = self.file_manager.get_document_content(doc_id)
            if doc_content:
                file_name = doc_info.get("file_name", "未知文件")
                context_parts.append(f"{idx}. 《{file_name}》\n内容摘要：")
                context_parts.append(doc_content)
                context_parts.append("\n")

                detailed_results.append(
                    {
                        "doc_id": doc_id,
                        "file_name": file_name,
                        "content": doc_content,
                        "score": 1.0,  # 非向量检索，默认得分
                    }
                )
                total_chars += len(doc_content)

        # 合并上下文
        context = "\n".join(context_parts)
        if total_chars > self.file_manager.max_token_len:
            context = context[: self.file_manager.max_token_len]
        return context, detailed_results

    def check_session_and_doc_status(self, session_id: str, user_id: str, doc_id: str):
        # 检查会话和文档是否存在
        session_result = self.session_collection.find_one(
            {"session_id": session_id, "user_id": user_id}
        )
        if not session_result:
            return "会话不存在"
        doc_result = self.file_manager.get_file_info(doc_id)
        if not doc_result:
            return "文档不存在"
        return ""

    def doc_stream_ask(self, session_id: str, question: str):
        """
        流式问答

        Args:
            session_id: 会话ID
            question: 用户问题

        Yields:
            str: AI回复片段
        """
        try:
            # 获取会话信息
            session = self.get_session(session_id)
            if not session:
                raise ValueError(f"会话 {session_id} 不存在")
            # 检查是否需求切换配置
            if session.use_custom_llm_config:
                self._switch_llm_configuration(session_id)



            # 获取当前上下文和已删除文档
            context, results = self.get_session_context_for_query(session_id)
            session = self.get_session(session_id)
            total_chars = session.total_chars
            deleted_files = session.deleted_files

            # 检测文档状态变更并构建状态变更提示
            status_change_info = self._detect_document_status_changes(results, deleted_files)

            # 构建现存文档列表
            history_summary = session.history_summary
            deleted_doc_info = build_deleted_doc_info(deleted_files)

            # 如果有状态变更，在deleted_doc_info中添加说明
            if status_change_info:
                if deleted_doc_info:
                    deleted_doc_info += f"\n\n🔄 **文档状态变更提醒**：\n{status_change_info}"
                else:
                    deleted_doc_info = f"🔄 **文档状态变更提醒**：\n{status_change_info}"

            prompt = build_doc_reader_prompt(context, question, deleted_doc_info)
            messages = []
            if history_summary:
                messages.append(
                    {"role": "system", "content": f"以下是对话历史的摘要，仅供参考：\n{history_summary}"}
                )
            # ② 当前文档状态 prompt 作为第二个 system message（一定要有）
            messages.append({"role": "system", "content": prompt})

            # ③ 加入完整历史对话
            history_message = self.get_history_message(session_id)
            messages += [{"role": m.role, "content": m.content} for m in history_message]
            messages.append({"role": "user", "content": question})
            doc_info = [
                {k: v for k, v in doc.items() if k != "content"}
                for doc in results
            ]

            logger.info(f'{session_id} 存在以下文档：\n {doc_info}')
            # 流式调用LLM
            ai_reply = ""
            usage_info = None
            first_chunk_sent = False
            proportion = None

            if total_chars > self.file_manager.max_token_len:
                proportion = self.file_manager.max_token_len / total_chars * 100

            for chunk in self.llm_client.chat_stream(
                messages, stream_options={"include_usage": True}
            ):
                # 捕获 usage 信息
                if isinstance(chunk, dict) and "__usage__" in chunk:
                    usage_info = chunk["__usage__"]
                    continue  # 不加入到文本中
                if not first_chunk_sent:
                    if proportion is not None:
                        # 第一个 chunk，加上 progress 提示
                        yield f"<progress>⚠️ 超出字数限制，小杏仁只阅读了前 {round(proportion, 2)}%</progress>\n{chunk}"
                    else:
                        yield chunk
                    first_chunk_sent = True
                else:
                    yield chunk

                ai_reply += chunk
            if usage_info is not None:
                usage = self.llm_client.usage_to_dict(usage_info)
            else:
                usage = {}

            # 添加用户消息
            self.add_message(session_id, "user", question)
            # 添加AI回复
            self.add_message(session_id, "assistant", ai_reply, metadata={"usage": usage})
        except Exception as e:
            logger.exception(f"文档解读问答异常: {e}")
            yield "系统繁忙，获取回复失败。"

    def _smart_clean_deleted_files(self, session_id: str, new_file_name: str) -> list:
        """
        智能清理deleted_files中的相关文件记录

        清理策略：
        1. 完全匹配的文件名
        2. 相似的文件名（去除数字后缀、版本号等）
        3. 同类型的文件（相同扩展名且基础名称相似）

        Args:
            session_id: 会话ID
            new_file_name: 新上传的文件名

        Returns:
            list: 被清理的文件名列表
        """
        try:
            # 获取当前会话的deleted_files
            session = self.session_collection.find_one({"session_id": session_id})
            if not session or not session.get("deleted_files"):
                return []

            deleted_files = session.get("deleted_files", [])
            files_to_clean = []

            # 解析新文件名
            new_base_name, new_ext = self._parse_filename(new_file_name)

            for deleted_file in deleted_files:
                deleted_name = deleted_file.get("file_name", "")
                if not deleted_name:
                    continue

                # 解析已删除文件名
                deleted_base_name, deleted_ext = self._parse_filename(deleted_name)

                should_clean = False

                # 策略1：完全匹配
                if deleted_name == new_file_name:
                    should_clean = True

                # 策略2：相同扩展名且基础名称相似
                elif deleted_ext == new_ext and self._is_similar_basename(deleted_base_name, new_base_name):
                    should_clean = True

                if should_clean:
                    files_to_clean.append(deleted_name)

            # 执行清理
            if files_to_clean:
                self.session_collection.update_one(
                    {"session_id": session_id},
                    {"$pull": {"deleted_files": {"file_name": {"$in": files_to_clean}}}}
                )

            return files_to_clean

        except Exception as e:
            logger.error(f"智能清理deleted_files失败: {e}")
            return []

    def _parse_filename(self, filename: str) -> tuple:
        """解析文件名，返回基础名称和扩展名"""
        import os
        base_name = os.path.splitext(filename)[0]
        ext = os.path.splitext(filename)[1].lower()
        return base_name, ext

    def _is_similar_basename(self, name1: str, name2: str) -> bool:
        """判断两个基础文件名是否相似"""
        import re

        # 移除数字后缀和常见版本标识
        def normalize_name(name):
            # 移除末尾的数字、括号内容、版本号等
            name = re.sub(r'\d+$', '', name)  # 移除末尾数字
            name = re.sub(r'\(\d+\)$', '', name)  # 移除 (1), (2) 等
            name = re.sub(r'[-_]\d+$', '', name)  # 移除 -1, _2 等
            name = re.sub(r'[-_]v\d+$', '', name, re.IGNORECASE)  # 移除 -v1, _V2 等
            name = re.sub(r'[-_]副本$', '', name)  # 移除副本标识
            name = re.sub(r'[-_]copy$', '', name, re.IGNORECASE)  # 移除copy标识
            return name.strip().lower()

        normalized1 = normalize_name(name1)
        normalized2 = normalize_name(name2)

        # 如果标准化后的名称相同，认为是相似的
        if normalized1 == normalized2:
            return True

        # 如果一个是另一个的前缀（长度差不超过3），也认为是相似的
        if len(normalized1) > 0 and len(normalized2) > 0:
            shorter = min(normalized1, normalized2, key=len)
            longer = max(normalized1, normalized2, key=len)
            if longer.startswith(shorter) and len(longer) - len(shorter) <= 3:
                return True

        return False

    def _detect_document_status_changes(self, current_docs: list, deleted_files: list) -> str:
        """
        检测文档状态变更，生成状态变更提示

        Args:
            current_docs: 当前可用文档列表
            deleted_files: 已删除文档列表

        Returns:
            str: 状态变更提示信息
        """
        if not deleted_files or not current_docs:
            return ""

        status_changes = []
        current_file_names = [doc.get("file_name", "") for doc in current_docs]

        for deleted_file in deleted_files:
            deleted_name = deleted_file.get("file_name", "")
            if not deleted_name:
                continue

            # 检查是否有完全匹配的文件重新上传
            if deleted_name in current_file_names:
                status_changes.append(f"文档 [{deleted_name}] 已重新上传，当前可用")
                continue

            # 检查是否有相似的文件重新上传
            deleted_base, deleted_ext = self._parse_filename(deleted_name)
            for current_name in current_file_names:
                current_base, current_ext = self._parse_filename(current_name)
                if (deleted_ext == current_ext and
                    self._is_similar_basename(deleted_base, current_base)):
                    status_changes.append(f"相似文档已重新上传：[{deleted_name}] -> [{current_name}]，请使用当前可用的文档")
                    break

        if status_changes:
            return "\n".join(status_changes)
        return ""


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()
    session_manager = DocSessionManager()

    # session_id = session_manager.create_session('test_user', '测试会话')
    # path = r'F:\gitlab\AlmondMind\pyproject.toml'
    # with open(path, 'r', encoding='utf-8')as f:
    #     content = f.read()
    #     content = io.BytesIO(content.encode('utf-8'))
    # session_manager.save_file_to_session('test_user', content, 'pyproject.toml', session_id)
    se_id = "41d34509-4046-4e8a-bf95-35f3115ac875"
    for ch in session_manager.doc_stream_ask(se_id, "简单总结文档内容"):
        print(ch, end="")
