# -*- encoding: utf-8 -*-
"""
@File   :file_processor.py
@Time   :2025/5/24 14:30
<AUTHOR> Engineer
"""

import uuid
from typing import List, Optional

from langchain_text_splitters import RecursiveCharacterTextSplitter, CharacterTextSplitter

from loguru import logger

from app.core.db.mongo import MongoClient
from app.core.doc_parser.registry import parse_file
from app.core.llm.session.document_qa.utils import get_default_splitter_config, get_local_datetime
from app.utils.exceptions import DocReaderException


class FileProcessor:
    """
    文件处理器：负责文档解析、文本提取和分块
    """
    _index_initialized = False  # 类变量，用于确保索引只建一次

    def __init__(
            self,
            mongo_storage: Optional[MongoClient],
            max_token_len,
            enable_quantization,
            collection,
            chunk_collection,
            splitter_config=None
    ):
        # MongoDB 连接
        self.mongo_ctx = mongo_storage
        self.collection = collection
        self.chunk_collection = chunk_collection

        # 处理配置
        self.max_token_len = max_token_len
        self.enable_quantization = enable_quantization

        # 分割器配置
        self.splitter_config = splitter_config or get_default_splitter_config()

        if not FileProcessor._index_initialized:
            self._ensure_indexes()
            FileProcessor._index_initialized = True  # 只初始化一次

    def process_document(self, doc_id: str, file_path: str, file_name: str, parsed_content: str = None) -> int:
        """
        处理文档：提取文本并分块存储

        Args:
            doc_id: 文档ID
            file_path: 文件路径
            file_name: 文件名
            parsed_content: 已解析的内容（如果提供则跳过本地解析）

        Returns:
            int: 文本总字符数
        """
        try:
            # 提取文本
            if parsed_content:
                # 使用已解析的内容（OCR结果）
                text_content = parsed_content
                logger.info(f"使用已解析的内容: {file_name}, 长度: {len(text_content)}")
            else:
                # 本地解析文件
                text_content = parse_file(file_path)
                logger.info(f"本地解析完成: {file_name}, 长度: {len(text_content)}")
            if not text_content:
                logger.error(f"文档解析结果为空，可能是文档内容无法识别或文档已损坏: {file_name}")
                raise DocReaderException("文档解析结果为空，可能是文档内容无法识别或文档已损坏")

            # 标记为已提取
            self.collection.update_one(
                {"doc_id": doc_id},
                {"$set": {
                    "status": "extracted",
                    "updated_at": get_local_datetime()
                }}
            )

            # 分块
            logger.info(f"✅ 正在处理文档 {doc_id} {file_name}")
            chunks = self._split_text(text_content)
            logger.info(f"分块完成，共 {len(chunks)} 个块")
            doc = self.collection.find_one({"doc_id": doc_id})
            user_id = doc.get("user_id", "")

            # 构建批量写入数据
            now = get_local_datetime()
            chunk_docs = []
            total_chars = 0
            for i, chunk in enumerate(chunks):
                total_chars += len(chunk)
                chunk_docs.append({
                    "chunk_id": str(uuid.uuid4()),
                    "doc_id": doc_id,
                    "user_id": user_id,
                    "file_name": file_name,
                    "chunk_index": i,
                    "chunk_content": chunk,
                    "created_at": now,
                    "is_vectorized": False,
                    "is_quantized": False  # 可根据是否启用量化策略后续设置
                })

            if chunk_docs:
                self.chunk_collection.insert_many(chunk_docs)
                logger.info(f"已向数据库插入 {len(chunk_docs)} 个块")

            # 更新状态
            self.collection.update_one(
                {"doc_id": doc_id},
                {"$set": {
                    "status": "processed",
                    "chunk_count": len(chunks),
                    "updated_at": get_local_datetime()
                }}
            )
            logger.info(f"文档 {doc_id} 处理完成，共 {len(chunks)} 个块")
            return total_chars

        except Exception as e:
            raise Exception(f"处理文档失败: {str(e)}")

    def process_content(self, content: str, file_name: str, doc_id: str) -> List[str]:
        """
        处理已解析的内容：直接分块存储

        Args:
            content: 已解析的文本内容
            file_name: 文件名
            doc_id: 文档ID

        Returns:
            List[str]: 分块列表
        """
        try:
            # 分块
            logger.info(f"✅ 正在处理已解析内容 {doc_id} {file_name}")
            chunks = self._split_text(content)
            logger.info(f"分块完成，共 {len(chunks)} 个块")

            doc = self.collection.find_one({"doc_id": doc_id})
            if not doc:
                logger.error(f"文档不存在: doc_id={doc_id}")
                return []

            user_id = doc.get("user_id", "")

            # 构建批量写入数据
            now = get_local_datetime()
            chunk_docs = []
            for i, chunk in enumerate(chunks):
                chunk_docs.append({
                    "chunk_id": str(uuid.uuid4()),
                    "doc_id": doc_id,
                    "user_id": user_id,
                    "file_name": file_name,
                    "chunk_index": i,
                    "chunk_content": chunk,
                    "created_at": now,
                    "is_vectorized": False,
                    "is_quantized": False
                })

            if chunk_docs:
                self.chunk_collection.insert_many(chunk_docs)
                logger.info(f"已向数据库插入 {len(chunk_docs)} 个块")

            # 更新状态
            self.collection.update_one(
                {"doc_id": doc_id},
                {"$set": {
                    "status": "processed",
                    "chunk_count": len(chunks),
                    "updated_at": get_local_datetime()
                }}
            )
            logger.info(f"文档内容处理完成: {doc_id}, 共 {len(chunks)} 个块")
            return chunks

        except Exception as e:
            logger.error(f"处理文档内容失败: {e}")
            return []

    def _split_text(self, text: str) -> List[str]:
        """
        将文本分块

        Args:
            text: 要分块的文本

        Returns:
            List[str]: 文本块列表
        """
        # 获取分割器配置
        splitter_type = self.splitter_config.get("type", "recursive")
        params = self.splitter_config.get("params", {})
        chunk_size = params.get("chunk_size", 1024)
        chunk_overlap = params.get("chunk_overlap", 64)

        # 根据配置创建对应的 TextSplitter
        if splitter_type == "recursive":
            splitter = RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                separators=["\n\n", "\n", "。", "！", "？", ".", "!", "?", " ", "#"]
            )
        else:
            splitter = CharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                separator="\n"
            )

        return splitter.split_text(text)

    def _ensure_indexes(self):
        self.chunk_collection.create_index([("doc_id", 1), ("chunk_index", 1)])
        self.chunk_collection.create_index("chunk_id", unique=True)


# 用法示例
if __name__ == "__main__":
    # 初始化文件处理器
    file_processor = FileProcessor()

    # 处理文档
    # file_processor.process_document("test_doc_id", "test.pdf", "test.pdf")
