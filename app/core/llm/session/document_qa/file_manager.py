# -*- encoding: utf-8 -*-
"""
@File   :file_manager.py
@Time   :2025/5/24 13:40
<AUTHOR> Engineer
"""

import os
import uuid
from typing import List, Dict, Tuple, BinaryIO, Optional

from app.configs.document_config import doc_settings
from app.core.db.manager import DBManager
from app.core.db.mongo import MongoClient
from app.core.llm.session.document_qa.file_processor import FileProcessor
from app.core.llm.session.document_qa.utils import get_local_datetime


class FileManager:
    """
    文件管理器：处理文件上传、存储和检索
    """

    def __init__(
        self,
        mongo_storage: Optional[MongoClient] = None,
        upload_dir=doc_settings.default_upload_dir,
        max_token_len=doc_settings.default_max_token_len,
        enable_quantization=doc_settings.default_enable_quantization,
        auto_vectorize=doc_settings.default_auto_vectorize,
    ):
        if mongo_storage is None:
            DBManager.initialize(mongo_config=doc_settings.get_mongo_config)
            self.mongo_ctx = DBManager.get_mongo()
        else:
            self.mongo_ctx = mongo_storage
        self.collection = self.mongo_ctx.db[doc_settings.doc_collection]
        self.chunk_collection = self.mongo_ctx.db[doc_settings.doc_chunk_collection]

        # 文件存储目录
        self.upload_dir = upload_dir
        if not os.path.exists(self.upload_dir):
            os.makedirs(self.upload_dir, exist_ok=True)

        # 文档处理配置
        self.max_token_len = max_token_len
        self.enable_quantization = enable_quantization
        self.auto_vectorize = auto_vectorize

        # 初始化文件处理器
        self.file_processor = FileProcessor(
            self.mongo_ctx,
            max_token_len,
            enable_quantization,
            self.collection,
            self.chunk_collection,
        )

    def save_file(self, user_id: str, file: BinaryIO, filename: str) -> str:
        """
        保存上传的文件并处理

        Args:
            user_id: 用户ID
            file: 文件对象
            filename: 文件名

        Returns:
            str: 文件ID
        """
        doc_id = str(uuid.uuid4())
        file_ext = os.path.splitext(filename)[1].lower()

        # 保存文件到上传目录
        upload_path = os.path.join(self.upload_dir, f"{doc_id}{file_ext}")
        os.makedirs(os.path.dirname(upload_path), exist_ok=True)
        with open(upload_path, "wb") as f:
            f.write(file.read())

        # 处理文件内容
        file_info = {
            "doc_id": doc_id,
            "user_id": user_id,
            "file_name": filename,
            "file_path": upload_path,
            "file_type": file_ext[1:] if file_ext else "",  # 去掉点号
            "file_size": os.path.getsize(upload_path),
            "status": "uploaded",
            "created_at": get_local_datetime(),
            "updated_at": get_local_datetime(),
            "is_quantized": False,
            "is_vectorized": False,
            "char_count": 0,
        }

        # 保存文件信息到MongoDB
        self.collection.insert_one(file_info)

        # 处理文档
        try:
            # 提取并分块文本
            total_chars = self.file_processor.process_document(
                doc_id, upload_path, filename
            )
            # 更新文档信息
            self.collection.update_one(
                {"doc_id": doc_id},
                {
                    "$set": {
                        "status": "processed",
                        "char_count": total_chars,
                        "updated_at": get_local_datetime(),
                    }
                },
            )
            return doc_id

        except Exception as e:
            # 更新处理失败状态
            self.collection.update_one(
                {"doc_id": doc_id},
                {
                    "$set": {
                        "status": "error",
                        "error_message": str(e),
                        "updated_at": get_local_datetime(),
                    }
                },
            )
            raise

    def get_file_info(self, doc_id: str, all_fields: bool = False) -> Dict:
        """
        获取文件信息

        Args:
            doc_id: 文档ID
            all_fields: 是否返回所有字段，默认否

        Returns:
            Dict: 文件信息
        """
        doc = self.collection.find_one({"doc_id": doc_id})
        if not doc:
            return {}
        if all_fields:
            return doc
        return {
            "doc_id": doc_id,
            "file_name": doc.get("file_name", ""),
            "file_type": doc.get("file_type", ""),
            "file_size": doc.get("file_size", 0),
            "status": doc.get("status", ""),
            "created_at": doc.get("created_at"),
            "updated_at": doc.get("updated_at"),
            "is_quantized": doc.get("is_quantized", False),
            "is_vectorized": doc.get("is_vectorized", False),
            "char_count": doc.get("char_count", 0),
            "error_message": doc.get("error_message", ""),
        }

    def get_document_content(self, doc_id: str, use_chunks: bool = True) -> str:
        """
        获取文档内容

        Args:
            doc_id: 文档ID
            use_chunks: 是否使用分块内容（True）或原始文本（False）

        Returns:
            str: 文档内容
        """
        if use_chunks:
            # 从分块集合中获取内容
            chunks = list(
                self.chunk_collection.find(
                    {"doc_id": doc_id}, {"chunk_content": 1, "chunk_index": 1}
                ).sort("chunk_index", 1)
            )

            if not chunks:
                return ""

            # 如果未启用自动向量化，限制拼接长度
            if not self.auto_vectorize:
                max_len = self.max_token_len
                content_parts = []
                current_len = 0

                for chunk in chunks:
                    part = chunk.get("chunk_content", "")
                    part_len = len(part)

                    if current_len + part_len > max_len:
                        # 剩余空间不足时只截取部分
                        remaining = max_len - current_len
                        if remaining > 0:
                            content_parts.append(part[:remaining])
                        break

                    content_parts.append(part)
                    current_len += part_len

                return "\n".join(content_parts)

            # 默认拼接所有内容
            return "\n".join(chunk.get("chunk_content", "") for chunk in chunks)
        else:
            # 从文档集合中获取原始文本
            doc = self.collection.find_one({"doc_id": doc_id})
            if not doc or "content" not in doc:
                return ""
            return doc["content"]

    def should_use_vector_search(self, doc_id: str) -> bool:
        """
        判断是否应该对文档使用向量检索

        Args:
            doc_id: 文档ID

        Returns:
            bool: 是否应使用向量检索
        """
        doc = self.collection.find_one({"doc_id": doc_id})
        if not doc:
            return False

        # 检查文档是否已向量化
        if doc.get("is_vectorized", False):
            return True

        # 检查文档字符数是否超过阈值
        char_count = doc.get("char_count", 0)
        return char_count > self.max_token_len

    def check_vectorization_status(self, doc_ids: List[str]) -> Dict[str, Dict]:
        """
        检查文档向量化状态

        Args:
            doc_ids: 文档ID列表

        Returns:
            Dict[str, Dict]: 文档向量化状态，key为doc_id
        """
        result = {}
        for doc_id in doc_ids:
            doc = self.collection.find_one({"doc_id": doc_id})
            if not doc:
                result[doc_id] = {"exists": False}
                continue

            # 计算向量化进度
            chunks = list(self.chunk_collection.find({"doc_id": doc_id}))
            total_chunks = len(chunks)
            vectorized_chunks = sum(
                1 for chunk in chunks if chunk.get("is_vectorized", False)
            )
            progress = (
                (vectorized_chunks / total_chunks) * 100 if total_chunks > 0 else 0
            )

            result[doc_id] = {
                "exists": True,
                "status": doc.get("status", ""),
                "is_vectorized": doc.get("is_vectorized", False),
                "is_quantized": doc.get("is_quantized", False),
                "char_count": doc.get("char_count", 0),
                "total_chunks": total_chunks,
                "vectorized_chunks": vectorized_chunks,
                "progress": round(progress, 2),
                "should_use_vector": self.should_use_vector_search(doc_id),
            }

        return result

    def delete_document(self, doc_id: str) -> bool:
        """
        删除文档及其相关数据

        Args:
            doc_id: 文档ID

        Returns:
            bool: 操作是否成功
        """
        try:
            # 获取文档信息
            doc = self.collection.find_one({"doc_id": doc_id})
            if not doc:
                return False

            # 删除文件
            file_path = doc.get("file_path", "")
            if file_path and os.path.exists(file_path):
                os.remove(file_path)

            # 删除数据库记录
            self.collection.delete_one({"doc_id": doc_id})
            self.chunk_collection.delete_many({"doc_id": doc_id})


            return True
        except Exception as e:
            print(f"删除文档失败: {str(e)}")
            return False


# 用法示例
if __name__ == "__main__":
    # 初始化文件管理器
    file_manager = FileManager(auto_vectorize=True, enable_quantization=True)
