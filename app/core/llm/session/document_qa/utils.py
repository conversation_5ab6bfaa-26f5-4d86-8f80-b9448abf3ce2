# -*- encoding: utf-8 -*-
"""
@File   :utils.py
@Time   :2025/6/9 17:40
<AUTHOR>
"""
from datetime import datetime
from typing import Dict, Any

from app.configs.document_config import doc_settings


def get_local_datetime():
    """
    获取当前时间
    :return:
    """
    return datetime.now()


def get_default_splitter_config() -> Dict[str, Any]:
    """返回默认的文本分割配置"""
    return {
        "type": doc_settings.default_splitter_type,
        "params": {
            "chunk_size": doc_settings.default_chunk_size,
            "chunk_overlap": doc_settings.default_chunk_overlap,
            "add_start_index": True,
        },
    }
