# -*- encoding: utf-8 -*-
"""
@File   :model.py
@Time   :2025/6/3 16:43
<AUTHOR>
"""
from pydantic import BaseModel
from typing import List, Optional, Dict
from datetime import datetime


class Message(BaseModel):
    id: str
    role: str
    content: str
    timestamp: datetime
    user_id: Optional[str] = None
    metadata: Optional[dict] = None


class Session(BaseModel):
    session_id: str
    user_id: str
    name: str
    created_at: datetime
    updated_at: datetime
    doc_ids: List[str] = []
    message_count: int = 0
    total_chars: int = 0
    vectorized: bool = False
    vectorized_char_count: int = 0
    last_vectorized_time: Optional[datetime] = None
    status: str = "active"
    title: str = ""
    history_summary: str = ""
    is_summarized: bool = False
    files: List[Dict] = []
    deleted_files: List[Dict] = []
    use_custom_llm_config: Optional[bool] = None  # 支持 None、True、False
    metadata: Optional[dict] = None
    summarized_message_count: int = 0
    reset_at: Optional[datetime] = None
    batches: Optional[List] = []
    deleted: bool = False

    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat(),
        }
