# -*- encoding: utf-8 -*-
"""
@File   :chat_session.py
@Time   :2025/6/10 12:18
<AUTHOR>
"""
import asyncio
import json
from typing import Tuple, List, AsyncGenerator

from loguru import logger

from app.core.llm.session.base import SessionManager
from app.utils.monitor import send_monitor_event
from app.utils.sync2async import sync_to_async_generator
from app.v1.core.directknowleg_hit import hit_question, _merge_context
from app.v1.core.enterprise_knowledge import sse_stream, detect_error_content
from app.v1.core.net_api import NetServer

from app.schemas.web_search import web_config


class GeneralChatSessionManager(SessionManager):
    """
    通用对话 Session 管理器：
    - 支持上下文记忆
    - 支持信息增强：联网、企业知识库等
    """

    _initialized = False
    _web_search_enricher = None  # 类级别单例

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if GeneralChatSessionManager._web_search_enricher is None:
            GeneralChatSessionManager._web_search_enricher = NetServer()

        self.web_search_enricher = GeneralChatSessionManager._web_search_enricher
        self.web_engine = web_config.engine
        self.web_top_k = web_config.top_k

    async def web_search_context(self, question: str) -> str:
        """
        web搜索上下文
        Args:
            question: 问题

        Returns:

        """
        content = await self.web_search_enricher.search(
            question, engine=self.web_engine, results=self.web_top_k
        )
        if len(content) > 70000:
            content = content[:70000]
        return content

    def web_search_context_sync(self, question: str) -> str:
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop.run_until_complete(self.web_search_context(question))

    def enterprise_knowledge_base_context(
            self,
            dataset_id: str,
            question: str,
            kind: bool = True,
    ) -> Tuple[str, List[dict]]:
        """
        企业知识库检索：返回拼接后的上下文 + 原始片段列表
        """
        if not dataset_id or dataset_id == "d5fb110a-1f4d-11f0-b0a5-86a404970cbc":
            if not dataset_id:
                dataset_id = "a0880f08-2102-11f0-b213-926528472ef0"

            return "__sse_mode__", [{"dataset_id": dataset_id}]  # 特殊标记，告诉外面自己处理

        try:
            hit_json = hit_question(question, dataset_id, kind=kind)
            paragraph_list = hit_json.get("data", [])
        except Exception as e:
            # fallback 兜底，不是 maxkb 错误结构
            send_monitor_event(
                title="📡 企业知识库内部调用异常",
                content=f"❌ 错误：{str(e)}\n📥 问题：{question}\n📌 数据集：{dataset_id}",
                tags=["maxkb", "exception"],
            )
            return "系统繁忙，获取回复失败。", []

        if not paragraph_list:
            return "没有找到和您提问相关的问题，请重新描述您的问题。", []

        for p in paragraph_list:
            p.setdefault("query", question)

        context_str = _merge_context(paragraph_list)

        # ✅ 关键：异常内容检测逻辑
        if detect_error_content(context_str, f"企业知识库内容检测 - 数据集:{dataset_id} - 问题:{question}"):
            return "系统繁忙，获取回复失败。", []

        # 字数限制
        if len(context_str) > 60000:
            context_str = context_str[:60000]

        return context_str, paragraph_list

    def _build_prompt_from_messages(self, messages: list[dict]) -> str:
        """
        将 messages 列表转换成一个对 LLM 友好的、单一的字符串提示。
        """
        prompt_parts = []
        # 分离出最后的 user question，其他的作为上下文
        user_question = ""

        if messages and messages[-1]["role"] == "user":
            user_question = messages[-1]["content"]
            context_messages = messages[:-1]
        else:
            # 兜底情况，虽然按你的逻辑不太可能发生
            context_messages = messages

        for msg in context_messages:
            role = msg["role"]
            content = msg["content"]
            if role == "system":
                # 系统提示和参考资料可以加上明确的标记
                prompt_parts.append(f"--- 系统指令与参考资料 ---\n{content}\n---")
            elif role == "user":
                prompt_parts.append(f"用户: {content}")
            elif role == "assistant":
                prompt_parts.append(f"AI助手: {content}")
            # 可以根据需要添加其他角色的处理

        # 将历史对话部分用换行符连接
        full_context = "\n\n".join(prompt_parts)

        # 最终组合成一个完整的提示
        final_prompt = f"""请根据以下背景信息和对话历史，回答用户最后提出的问题。

        {full_context}
    
        --- 当前任务 ---
        请回答以下问题：
        用户: {user_question}
        AI助手: """  # 这里的"AI助手: "是一个小技巧，引导模型开始生成回答

        return final_prompt

    @staticmethod
    async def yield_buffer_smoothly(text: str, delay: float = 0.02) -> AsyncGenerator[bytes, None]:
        for ch in text:
            yield ch.encode("utf-8")
            await asyncio.sleep(delay)

    async def chat_stream_ask(
            self,
            session_id: str,
            question: str,
            dataset_id: str = None,
            use_web: bool = False,
            use_enterprise_qa: bool = False,
            is_private=True,
            assistant_prompt: str = None,
    ):
        """
        通用对话流式接口，支持信息增强模块（如联网、企业知识库）

        Args:
            session_id: 会话 ID
            question: 用户输入
            dataset_id: 企业知识库 ID
            use_web: 是否启用联网搜索
            use_enterprise_qa: 是否启用企业知识库增强
            is_private: 是否是私人库
            assistant_prompt: 助手提示词

        Yields:
            str: AI 回复片段
        """
        try:
            # 1. 会话与 LLM 配置
            session = self.get_session(session_id)
            if not session:
                raise ValueError(f"会话 {session_id} 不存在")
            if session.use_custom_llm_config:
                self._switch_llm_configuration(session_id)

            # 使用助手提示词或默认系统提示词
            if assistant_prompt:
                system_prompt = assistant_prompt
            else:
                system_prompt = """
                你是Deep Audit，立信的审计专家。能用简洁、准确的语言回答用户提出的任何问题。
                请根据当前问题和上下文进行思考，给出清晰、有逻辑、实用的回答。

                你的职责包括：
                - 回答用户提出的各类问题（如技术、生活、建议、通识等）
                - 尊重历史对话内容，但不过度依赖上下文
                - 若启用了参考资料（如联网搜索、知识库），请优先依据资料进行回答
                - 不要杜撰事实；若无法确定，请坦诚说明你不知道
                - 避免无意义的客套话；直接进入主题，语言自然、清晰

                务必根据用户使用的语言来进行交流。
                """

            # 3. 上下文构建（历史摘要 + 历史消息）
            history_summary = session.history_summary
            history_messages = self.get_history_message(session_id)

            messages = []
            paragraph_list = []
            sse_mode = False

            if history_summary:
                messages.append(
                    {"role": "system", "content": f"以下是对话历史摘要，仅供参考：\n{history_summary}"}
                )

            # 4. 联网增强
            if use_web:
                logger.info(f"{session_id} 启用联网搜索")
                web_context = await self.web_search_context(question)
                messages.append(
                    {"role": "system", "content": f"以下是来自联网搜索的参考信息：\n{web_context}"}
                )

            # 5. 企业知识库增强
            if use_enterprise_qa:
                logger.info(f"{session_id} 启用企业知识库")
                context, paragraph_list = self.enterprise_knowledge_base_context(
                    dataset_id, question, is_private
                )
                if context:
                    if context == "__sse_mode__":
                        # 标记进入 SSE 模式，但不立即中断流程
                        sse_mode = True
                        dataset_id = paragraph_list[0]["dataset_id"]
                        logger.info(f"{session_id} 启用 SSE 模式")

                    else:
                        messages.append(
                            {"role": "system", "content": f"以下是来自企业知识库的参考信息：\n{context}"}
                        )

            # 6. 加入历史消息 + 当前问题
            messages += [
                {"role": m.role, "content": m.content} for m in history_messages
            ]
            messages.append({"role": "user", "content": question})

            messages.insert(0, {"role": "system", "content": system_prompt})

            usage_info = None
            reply_parts = []
            # <<< SSE 模式分支 >>>
            if sse_mode:
                logger.info(f"会话 {session_id} 进入 MaxKB SSE 模式")
                sync_gen = sse_stream(message=question, dataset_id=dataset_id)
                stream_gen = sync_to_async_generator(sync_gen)

                async for chunk in stream_gen:
                    if isinstance(chunk, bytes):
                        decoded_chunk = chunk.decode('utf-8', errors='ignore')
                    elif isinstance(chunk, str):
                        decoded_chunk = chunk
                    else:
                        continue

                    if detect_error_content(decoded_chunk, f"SSE最后防线检测 - 会话:{session_id} - 问题:{question}"):
                        yield "系统繁忙，获取回复失败。".encode('utf-8')
                        return

                    reply_parts.append(decoded_chunk)

                    # ✅ 判断是否是 <source>，直接输出
                    if "<source>" in decoded_chunk:
                        yield chunk.encode('utf-8') if isinstance(chunk, str) else chunk
                    else:
                        # ✅ 否则逐字平滑输出
                        async for byte_chunk in self.yield_buffer_smoothly(decoded_chunk, delay=0.02):
                            yield byte_chunk

                # 注意：第三方SSE流可能不返回 usage 信息，这里我们自己处理
                usage = {}
            else:
                # 7. 流式 LLM 回复
                stream_gen = await self.llm_client.chat_stream_async(
                    messages, stream_options={"include_usage": True}
                )
                async for chunk in stream_gen:
                    if isinstance(chunk, dict) and "__usage__" in chunk:
                        usage_info = chunk["__usage__"]
                        continue
                    # 累加字符串，但 yield 编码后的字节
                    reply_parts.append(chunk)
                    yield chunk.encode('utf-8')

                usage = self.llm_client.usage_to_dict(usage_info) if usage_info else {}
            ai_reply = "".join(reply_parts)
            # 添加用户输入
            self.add_message(session_id, "user", question)
            # 添加AI回复
            self.add_message(
                session_id, "assistant", ai_reply, metadata={"usage": usage}
            )

            # 9. 追加 <source>（企业知识库增强时）
            if not sse_mode and use_enterprise_qa and dataset_id and paragraph_list:
                src_json = json.dumps(paragraph_list, ensure_ascii=False)
                yield f"<source>{src_json}</source>\n"
        except Exception as e:
            logger.exception(f"LLM 流式回复错误：{e}")
            yield "系统繁忙，获取回复失败。"
