# -*- encoding: utf-8 -*-
"""
@File   :base.py
@Time   :2025/5/30 18:09
<AUTHOR>
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Optional

from openai import BaseModel


class LLMEvent(BaseModel):
    model: str
    provider: str
    messages: List[Dict[str, str]]
    prompt: Optional[str] = None
    response: Optional[str] = None
    usage: Optional[dict] = None
    success: bool = True
    error: Optional[str] = None


class BaseMonitor(ABC):
    def __init__(self, include_fields: Optional[List[str]] = None):
        self.include_fields = include_fields or [
            "provider", "model", "messages", "prompt", "response", "usage", "success", "error"
        ]

    @abstractmethod
    def before(self, event: LLMEvent): ...

    @abstractmethod
    def after(self, event: LLMEvent): ...
