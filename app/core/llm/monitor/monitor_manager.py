# -*- encoding: utf-8 -*-
"""
@File   :monitor_manager.py
@Time   :2025/5/30 18:19
<AUTHOR>
"""
from typing import List
from app.core.llm.monitor.base import BaseMonitor, LLMEvent



class MonitorManager:
    _monitors: List[BaseMonitor] = []

    @classmethod
    def register(cls, monitor: BaseMonitor):
        cls._monitors.append(monitor)

    @classmethod
    def clear(cls):
        cls._monitors.clear()

    @classmethod
    def before(cls, event: LLMEvent):
        for monitor in cls._monitors:
            monitor.before(event)

    @classmethod
    def after(cls, event: LLMEvent):
        for monitor in cls._monitors:
            monitor.after(event)



