# -*- encoding: utf-8 -*-
"""
@File   :console_monitor.py
@Time   :2025/5/30 18:15
<AUTHOR>
"""
from loguru import logger
from app.core.llm.monitor.base import BaseMonitor, LLMEvent


class ConsoleLoggerMonitor(BaseMonitor):
    def _log_fields(self, event: LLMEvent, prefix: str, only_event=False):
        data = event.to_dict(exclude_none=True)
        filtered_data = {k: v for k, v in data.items() if k in self.include_fields}
        messages = filtered_data.pop("messages", [])

        if not only_event:
            # before 阶段：输出系统提示词、用户问题
            system_msg = next((m for m in messages if m.get("role") == "system"), None)
            if system_msg:
                content = system_msg.get("content", "")
                logger.info(f"{prefix} 系统提示词: {content}")
            user_msgs = [m for m in messages if m.get("role") == "user"]
            if user_msgs:
                last_user = user_msgs[-1]
                logger.info(f"{prefix} 用户问题最新一条: {last_user.get('content', '')}")

        # after 阶段：单独格式化响应内容
        if only_event:
            model = filtered_data.get("model", "-")
            provider = filtered_data.get("provider", "-")
            response = filtered_data.get("response", "")
            # 尝试美化显示
            logger.info(f"{prefix} [模型: {model}, Provider: {provider}]")
            logger.info(f"{prefix} LLM 响应内容：\n{response}\n")
        else:
            logger.info(f"{prefix} event: {filtered_data}")

    def before(self, event: LLMEvent):
        logger.info(f"🟡 [LLM BEFORE] Provider: {event.provider}, Model: {event.model}")
        self._log_fields(event, prefix="🟡", only_event=False)

    def after(self, event: LLMEvent):
        if event.success:
            logger.info(f"🟢 [LLM AFTER] Success: Provider: {event.provider}, Model: {event.model}")
        else:
            logger.error(f"🔴 [LLM AFTER] Failed: Provider: {event.provider}, Error: {event.error}")
        self._log_fields(event, prefix="🟢" if event.success else "🔴", only_event=True)
