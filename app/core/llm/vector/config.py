from typing import Dict, Any

# 默认配置
DEFAULT_VECTOR_STORE = "faiss"  # 默认使用 faiss
DEFAULT_FAISS_INDEX_FOLDER = "data/faiss_indexes"  # faiss 索引存储目录
DEFAULT_MILVUS_URI = "http://localhost:19530"  # milvus 服务地址
DEFAULT_MILVUS_TOKEN = ""  # milvus 认证 token
DEFAULT_MILVUS_DB = "default"  # milvus 默认数据库


# 向量存储配置获取函数
def get_vector_store_config() -> Dict[str, Any]:
    """返回向量存储配置"""
    config = {
        "store_type": DEFAULT_VECTOR_STORE,
    }

    # 根据存储类型添加特定配置
    if DEFAULT_VECTOR_STORE == "faiss":
        config.update({
            "index_folder": DEFAULT_FAISS_INDEX_FOLDER
        })
    elif DEFAULT_VECTOR_STORE == "milvus":
        config.update({
            "uri": DEFAULT_MILVUS_URI,
            "token": DEFAULT_MILVUS_TOKEN,
            "db_name": DEFAULT_MILVUS_DB
        })

    return config
