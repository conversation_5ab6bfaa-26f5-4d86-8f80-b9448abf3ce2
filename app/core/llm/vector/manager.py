import logging
from typing import List, Dict, Any, Tuple, Optional

from .base import VectorItem
from .config import get_vector_store_config
from .providers.milvus_client import MilvusClient
from .providers.faiss_client import FaissClient

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VectorManager:
    """向量管理器：负责文档块的向量化、存储和检索"""

    def __init__(
            self,
            collection_name: str = "doc_vectors",
            embedding_config: Optional[Dict[str, Any]] = None,
            top_k: int = 5
    ):
        # 获取向量存储配置
        vector_store_config = get_vector_store_config()
        self.vector_store_type = vector_store_config["store_type"]
        self.collection_name = collection_name

        # 根据存储类型初始化向量数据库客户端
        if self.vector_store_type == "milvus":
            self.vector_db = MilvusClient(
                uri=vector_store_config.get("uri"),
                db_name=vector_store_config.get("db_name"),
                token=vector_store_config.get("token")
            )
            logger.info("使用Milvus作为向量存储")
        elif self.vector_store_type == "faiss":
            self.vector_db = FaissClient(
                index_folder=vector_store_config.get("index_folder")
            )
            logger.info(f"使用Faiss作为向量存储，索引目录: {vector_store_config.get('index_folder')}")
        else:
            # 默认使用Faiss
            logger.warning(f"未知向量存储类型: {self.vector_store_type}，默认使用Faiss")
            self.vector_store_type = "faiss"
            self.vector_db = FaissClient(
                index_folder=vector_store_config.get("index_folder")
            )

        # 嵌入配置
        self.embedding_config = embedding_config or {}
        self.embedding_dimension = self.embedding_config.get("dimension", 1024)
        self.embedding_model = self.embedding_config.get("model", "bge-large-zh")

        # 检索配置
        self.top_k = top_k

        # 确保向量集合存在
        if not self.vector_db.has_collection(self.collection_name):
            try:
                self._create_vector_collection()
                logger.info(f"向量集合 {self.collection_name} 已创建")
            except Exception as e:
                logger.error(f"创建向量集合失败: {str(e)}")

    def _create_vector_collection(self) -> bool:
        """创建向量集合"""
        return self.vector_db.create_collection(
            collection_name=self.collection_name,
            dimension=self.embedding_dimension
        )

    def upsert_vectors(self, vectors: List[VectorItem]) -> bool:
        """更新或插入向量"""
        return self.vector_db.upsert(self.collection_name, vectors)

    def search_similar(
            self,
            query_vector: List[float],
            filter_dict: Optional[Dict[str, Any]] = None,
            top_k: Optional[int] = None
    ) -> Tuple[List[str], List[str], List[Dict[str, Any]], List[float]]:
        """搜索相似向量"""
        if top_k is None:
            top_k = self.top_k

        # 执行搜索
        result = self.vector_db.search(
            collection_name=self.collection_name,
            vectors=[query_vector],
            limit=top_k,
            filter_dict=filter_dict
        )

        if not result.ids or not result.ids[0]:
            return [], [], [], []

        return (
            result.ids[0],
            result.documents[0],
            result.metadatas[0],
            result.distances[0] if result.distances else []
        )

    def get_vectors(self, ids: List[str]) -> Tuple[List[str], List[str], List[Dict[str, Any]]]:
        """获取指定ID的向量"""
        result = self.vector_db.get(self.collection_name, ids)
        if not result.ids or not result.ids[0]:
            return [], [], []

        return result.ids[0], result.documents[0], result.metadatas[0]
