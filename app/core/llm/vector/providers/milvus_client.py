from pymilvus import MilvusClient as Client
from pymilvus import FieldSchema, DataType
import logging
from typing import List, Dict, Any, Optional

from ..base import VectorDBBase, VectorItem, SearchResult, GetResult
from ..config import DEFAULT_MILVUS_URI, DEFAULT_MILVUS_DB, DEFAULT_MILVUS_TOKEN

log = logging.getLogger(__name__)

# Milvus 配置
MILVUS_INDEX_TYPE = "HNSW"
MILVUS_METRIC_TYPE = "COSINE"
MILVUS_HNSW_M = 8
MILVUS_HNSW_EFCONSTRUCTION = 200
MILVUS_IVF_FLAT_NLIST = 128


class MilvusClient(VectorDBBase):
    """Milvus 向量数据库客户端"""

    def __init__(self, uri=None, db_name=None, token=None):
        self.collection_prefix = "aicenter"
        self.client = Client(
            uri=uri or DEFAULT_MILVUS_URI,
            db_name=db_name or DEFAULT_MILVUS_DB,
            token=token or DEFAULT_MILVUS_TOKEN
        )

    def has_collection(self, collection_name: str) -> bool:
        """检查集合是否存在"""
        try:
            return self.client.has_collection(collection_name)
        except Exception as e:
            log.error(f"检查集合失败: {str(e)}")
            return False

    def create_collection(self, collection_name: str, dimension: int) -> bool:
        """创建新的向量集合"""
        try:
            if self.has_collection(collection_name):
                return True

            # 创建集合
            self.client.create_collection(
                collection_name=collection_name,
                dimension=dimension,
                metric_type=MILVUS_METRIC_TYPE
            )

            # 创建索引
            self.client.create_index(
                collection_name=collection_name,
                field_name="vector",
                index_type=MILVUS_INDEX_TYPE,
                metric_type=MILVUS_METRIC_TYPE,
                params={
                    "M": MILVUS_HNSW_M,
                    "efConstruction": MILVUS_HNSW_EFCONSTRUCTION
                }
            )

            return True
        except Exception as e:
            log.error(f"创建集合失败: {str(e)}")
            return False

    def drop_collection(self, collection_name: str) -> bool:
        """删除向量集合"""
        try:
            if self.has_collection(collection_name):
                self.client.drop_collection(collection_name)
            return True
        except Exception as e:
            log.error(f"删除集合失败: {str(e)}")
            return False

    def upsert(self, collection_name: str, vectors: List[VectorItem]) -> bool:
        """更新或插入向量"""
        try:
            if not vectors:
                return True

            # 准备数据
            data = {
                "id": [v.id for v in vectors],
                "vector": [v.vector for v in vectors],
                "text": [v.text for v in vectors],
                "metadata": [v.metadata or {} for v in vectors]
            }

            # 插入数据
            self.client.insert(collection_name, data)
            return True
        except Exception as e:
            log.error(f"更新向量失败: {str(e)}")
            return False

    def search(
            self,
            collection_name: str,
            vectors: List[List[float]],
            limit: int = 5,
            filter_dict: Optional[Dict[str, Any]] = None
    ) -> SearchResult:
        """搜索相似向量"""
        try:
            # 执行搜索
            results = self.client.search(
                collection_name=collection_name,
                data=vectors,
                limit=limit,
                filter=filter_dict
            )

            # 处理结果
            ids = []
            documents = []
            metadatas = []
            distances = []

            for hits in results:
                _ids = []
                _documents = []
                _metadatas = []
                _distances = []

                for hit in hits:
                    _ids.append(hit.id)
                    _documents.append(hit.entity.get('text'))
                    _metadatas.append(hit.entity.get('metadata', {}))
                    _distances.append(hit.distance)

                ids.append(_ids)
                documents.append(_documents)
                metadatas.append(_metadatas)
                distances.append(_distances)

            return SearchResult(
                ids=ids,
                documents=documents,
                metadatas=metadatas,
                distances=distances
            )
        except Exception as e:
            log.error(f"搜索向量失败: {str(e)}")
            return SearchResult([], [], [], [])

    def get(self, collection_name: str, ids: List[str]) -> GetResult:
        """获取指定ID的向量"""
        try:
            # 获取数据
            results = self.client.get(collection_name, ids)

            # 处理结果
            _ids = []
            _documents = []
            _metadatas = []

            for item in results:
                _ids.append(item.get("id"))
                _documents.append(item.get("text"))
                _metadatas.append(item.get("metadata", {}))

            return GetResult(
                ids=[_ids],
                documents=[_documents],
                metadatas=[_metadatas]
            )
        except Exception as e:
            log.error(f"获取向量失败: {str(e)}")
            return GetResult([], [], [])
