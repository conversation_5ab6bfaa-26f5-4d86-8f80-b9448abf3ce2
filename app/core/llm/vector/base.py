from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional


@dataclass
class VectorItem:
    """向量项数据类"""
    id: str
    text: str
    vector: List[float]
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class SearchResult:
    """搜索结果数据类"""
    ids: List[List[str]]
    documents: List[List[str]]
    metadatas: List[List[Dict[str, Any]]]
    distances: Optional[List[List[float]]] = None


@dataclass
class GetResult:
    """获取结果数据类"""
    ids: List[List[str]]
    documents: List[List[str]]
    metadatas: List[List[Dict[str, Any]]]


class VectorDBBase(ABC):
    """向量数据库基类"""

    @abstractmethod
    def has_collection(self, collection_name: str) -> bool:
        """检查集合是否存在"""
        pass

    @abstractmethod
    def create_collection(self, collection_name: str, dimension: int) -> bool:
        """创建新的向量集合"""
        pass

    @abstractmethod
    def drop_collection(self, collection_name: str) -> bool:
        """删除向量集合"""
        pass

    @abstractmethod
    def upsert(self, collection_name: str, vectors: List[VectorItem]) -> bool:
        """更新或插入向量"""
        pass

    @abstractmethod
    def search(
            self,
            collection_name: str,
            vectors: List[List[float]],
            limit: int = 5,
            filter_dict: Optional[Dict[str, Any]] = None
    ) -> SearchResult:
        """搜索相似向量"""
        pass

    @abstractmethod
    def get(
            self,
            collection_name: str,
            ids: List[str]
    ) -> GetResult:
        """获取指定ID的向量"""
        pass
