# -*- encoding: utf-8 -*-
"""
@File   :maxkb_service.py
@Time   :2025/6/16 19:17
<AUTHOR>
"""
from typing import Union

from loguru import logger

from app.maxkb.sync_client import MaxKBSyncClient


def upload_text_to_maxkb(
    filename: str, text: str, maxkb_url: str, maxkb_id: str
) -> Union[dict, bool]:
    """
    将纯文本内容推送至 MaxKB（使用 MaxKBSyncClient）
    """
    try:
        document_content = [(filename, text.encode("utf-8"))]
        client = MaxKBSyncClient(base_url=maxkb_url, username="admin", password="adminyzw@1")
        result = client.process_and_import_documents(maxkb_id, document_content)

        if not result.get("data"):
            logger.error(f"❌ MaxKB 推送失败: {result}")
            return False

        logger.info(f"✅ MaxKB 推送成功: doc_id={maxkb_id}")
        return result

    except Exception as e:
        logger.exception(f"MaxKB 上传异常: {e}")
        return False
