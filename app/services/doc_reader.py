# -*- encoding: utf-8 -*-
"""
@File   :doc_reader.py
@Time   :2025/6/10 10:23
<AUTHOR>
"""


from bson import ObjectId
from starlette.responses import StreamingResponse


def objid_to_str(data):
    if isinstance(data, dict):
        return {k: objid_to_str(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [objid_to_str(item) for item in data]
    elif isinstance(data, ObjectId):
        return str(data)
    else:
        return data


def error_stream(message: str):
    async def generator():
        yield message

    return StreamingResponse(generator(), media_type="text/event-stream")
