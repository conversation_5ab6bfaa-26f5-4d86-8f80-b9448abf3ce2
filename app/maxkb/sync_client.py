# -*- encoding: utf-8 -*-
"""
@File   :sync_client.py
@Time   :2025/5/10 22:59
<AUTHOR>
"""
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

import requests
from io import BytesIO
from typing import List, Dict, Any, Tuple
from urllib.parse import quote, unquote
from tqdm import tqdm


from app.core.logger import logger
from app.maxkb.exceptions import AuthenticationError, APIError


class MaxKBSyncClient:
    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
    ):
        self.base_url = base_url.rstrip("/")
        self._username = username
        self._password = password
        self._token = None
        self._session = requests.Session()
        self.authenticate()

    def authenticate(self):
        response = self._session.post(
            f"{self.base_url}/api/user/login",
            json={"username": self._username, "password": self._password},
        )
        if response.status_code != 200:
            raise AuthenticationError(f"Authentication failed: {response.text}")
        self._token = response.json().get("data")
        logger.info("Successfully authenticated with MaxKB server")

    def _request(self, method: str, endpoint: str, return_data=True, **kwargs) -> Dict:
        if not self._token:
            self.authenticate()

        headers = kwargs.pop("headers", {})
        headers["Authorization"] = self._token
        url = f"{self.base_url}/api/{endpoint.lstrip('/')}"

        try:
            response = self._session.request(method, url, headers=headers, **kwargs)
            if response.status_code != 200:
                if response.status_code == 401:
                    self.authenticate()
                    return self._request(method, endpoint, return_data, **kwargs)
                raise APIError(response.status_code, response.text)
            data = response.json()
            return data.get("data") if return_data else data
        except requests.RequestException as e:
            raise APIError(500, f"Request failed: {str(e)}")

    def create_knowledge_base(self, name: str, desc: str = "") -> Dict:
        return self._request("POST", "dataset", json={"name": name, "desc": desc})

    def create_document(self, dataset_id: str, name: str) -> Dict:
        return self._request(
            "POST", f"dataset/{dataset_id}/document", json={"name": name}
        )

    def add_paragraphs(
        self,
        dataset_id: str,
        document_id: str,
        chunks: List[Dict[str, str]],
        batch_size: int = 10,
        max_workers: int = 3,
        max_qps: int = 3,
        doc_info=None,
    ) -> Dict[str, List[Any]]:
        """
        分批并发推送段落，每段一个请求，控制最大 QPS 和线程数，支持 tqdm 全局进度条
        """
        results = {"success": [], "failed": []}
        qps_lock = threading.Lock()
        last_request_time = [0.0]
        progress_lock = threading.Lock()
        total_chunks = len(chunks)

        pbar = tqdm(total=total_chunks, desc="📤 推送段落进度", ncols=100)

        def push_batch(batch: List[Dict[str, str]]) -> Tuple[List[Any], List[Any]]:
            batch_results_success = []
            batch_results_failed = []
            for chunk in batch:
                try:
                    # 控制 QPS
                    with qps_lock:
                        now = time.time()
                        elapsed = now - last_request_time[0]
                        if elapsed < 1.0 / max_qps:
                            time.sleep(1.0 / max_qps - elapsed)
                        last_request_time[0] = time.time()

                    result = self._request(
                        "POST",
                        f"dataset/{dataset_id}/document/{document_id}/paragraph",
                        json={
                            "content": chunk["content"],
                            "title": chunk.get("title", ""),
                            "is_active": True,
                        },
                    )
                    logger.info(f"✅ 成功添加段落: {chunk['content'][:30]}...")
                    logger.debug(f"🆎 doc_info {doc_info}")
                    batch_results_success.append(result)
                except Exception as e:
                    logger.error(f"❌ 失败段落: {chunk['content'][:30]}... => {e}")
                    logger.debug(f"🆎 doc_info {doc_info}")
                    batch_results_failed.append({"chunk": chunk, "error": str(e)})
                finally:
                    with progress_lock:
                        pbar.update(1)
            return batch_results_success, batch_results_failed

        chunk_batches = [
            chunks[i : i + batch_size] for i in range(0, len(chunks), batch_size)
        ]

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_batch = {
                executor.submit(push_batch, batch): batch for batch in chunk_batches
            }

            for future in as_completed(future_to_batch):
                try:
                    success, failed = future.result()
                    results["success"].extend(success)
                    results["failed"].extend(failed)
                except Exception as e:
                    logger.error(f"❌ 批处理线程异常: {e}")
                    for chunk in future_to_batch[future]:
                        results["failed"].append({"chunk": chunk, "error": str(e)})

        pbar.close()
        logger.info(
            f"🏁 所有段落处理完毕，总成功: {len(results['success'])}，总失败: {len(results['failed'])}"
        )
        return results

    def add_document(
        self, dataset_id: str, document_name: str, chunks: List[Dict[str, str]]
    ) -> Dict:
        doc = self.create_document(dataset_id, document_name)
        paragraphs = self.add_paragraphs(dataset_id, doc["id"], chunks)
        return {"document": doc, "paragraphs": paragraphs}

    def create_blank_document(self, knowledge_base_id: str, names: List[str]) -> Dict:
        if not names:
            raise ValueError("names cannot be empty")
        payload = [{"name": name} for name in names]
        endpoint = f"dataset/{knowledge_base_id}/document/_bach"
        return self._request("POST", endpoint, json=payload)

    def vectorization(self, knowledge_base_id: str, document_id: str):
        if not knowledge_base_id or not document_id:
            raise ValueError("knowledge_base_id and document_id cannot be empty")
        payload = {"state_list": ["0", "1", "2", "3", "4", "5", "n"]}
        endpoint = f"dataset/{knowledge_base_id}/document/{document_id}/refresh"
        return self._request("PUT", endpoint, return_data=False, json=payload)

    def _split_documents(self, documents: List[Tuple[str, bytes]]) -> Dict[str, Any]:
        endpoint = "/dataset/document/split"
        MAX_BATCH_SIZE = 30

        def _upload_batch(batch_documents: List[Tuple[str, bytes]]) -> Dict:
            files = []
            for name, content in batch_documents:
                file_obj = BytesIO(content)
                file_obj.seek(0)
                encoded_filename = quote(name)
                files.append(
                    ("file", (encoded_filename, file_obj, "application/octet-stream"))
                )

            return self._request("POST", endpoint, return_data=False, files=files)

        batches = [
            documents[i : i + MAX_BATCH_SIZE]
            for i in range(0, len(documents), MAX_BATCH_SIZE)
        ]
        success_data = []
        errors = []

        for batch in batches:
            try:
                result = _upload_batch(batch)
                if "data" in result and result["data"]:
                    success_data.extend(result["data"])
                else:
                    errors.extend(result)
            except Exception as e:
                for filename, _ in batch:
                    errors.extend(f"{filename}: {str(e)}")
                logger.exception(f"批量上传失败: {str(e)}")

        if errors:
            raise APIError(status_code=500, message=f"部分文档解析失败:\n" + "\n".join(errors))

        return {"code": 200, "message": "成功", "data": success_data}

    def _batch_import_documents(
        self, dataset_id: str, documents: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        endpoint = f"/dataset/{dataset_id}/document/_bach"
        return self._request("POST", endpoint, json=documents, return_data=False)

    def process_and_import_documents(
        self, dataset_id: str, documents: List[Tuple[str, bytes]]
    ) -> Dict[str, Any]:
        split_results = self._split_documents(documents)
        batch_data = []
        for doc in split_results["data"]:

            name = doc["name"]
            if "%" in name:
                name = unquote(name)
            batch_data.append({"name": unquote(name), "paragraphs": doc["content"]})
        result = self._batch_import_documents(dataset_id, batch_data)
        return result

    def get_documents(
        self, document_id: str = None, page: int = 1, page_size: int = 30
    ):
        endpoint = f"/dataset/{page}/{page_size}"
        results = self._request("GET", endpoint, return_data=True)
        assert isinstance(results, dict), "JSON ERROR"
        if results:
            records = results.get("records", [])
            if document_id:
                results = [result for result in records if result["id"] == document_id]
        return results


if __name__ == "__main__":
    maxkb = MaxKBSyncClient(base_url="http://172.17.10.72:8891")
    docs = maxkb.get_documents("c8935a90-3136-11f0-a284-0206092105a1", 1, 70)
    print(docs)
