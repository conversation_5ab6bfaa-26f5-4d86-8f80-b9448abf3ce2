"""Exceptions for MaxKB Server."""


class MaxKBError(Exception):
    """Base exception for MaxKB Server."""
    pass


class AuthenticationError(MaxKBError):
    """Raised when authentication fails."""
    pass


class APIError(MaxKBError):
    """Raised when API request fails."""

    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message
        super().__init__(f"API Error {status_code}: {message}")


class RateLimitError(MaxKBError):
    """Raised when API rate limit is exceeded."""
    pass


class ValidationError(MaxKBError):
    """Raised when request validation fails."""
    pass
