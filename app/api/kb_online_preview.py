# -*- encoding: utf-8 -*-
"""
@File   :kb_online_preview.py
@Time   :2025/5/27 20:31
<AUTHOR>
"""
import uuid

from fastapi import APIRouter, Query, Header, HTTPException
from loguru import logger
from pydantic import BaseModel
from wps_callback_kit import wps

from app.core.db.manager import DBManager
from app.core.db.models import KBEnterprise
from app.v1.core.tokens import decode_token

router = APIRouter(prefix="/api/wps", tags=["企业知识库提供在线预览"])


class PreviewRequest(BaseModel):
    file_name: str


@router.post("/kb/preview")
async def preview_kb_file(body: PreviewRequest, Authorization: str = Header(None)):
    # 权限验证
    username = decode_token(Authorization)
    if not username:
        raise HTTPException(status_code=403, detail="无权限访问")

    # 查询 MySQL 获取 file_path
    try:
        with DBManager.get_mysql().session() as session:
            kb: KBEnterprise = (
                session.query(KBEnterprise)
                .filter(KBEnterprise.file_name == body.file_name)
                .first()
            )

            if not kb:
                raise HTTPException(status_code=404, detail="该文件暂不支持在线预览")

            file_path = kb.file_path
    except Exception as e:
        logger.exception(f"MySQL 查询失败: {e}")
        raise HTTPException(status_code=500, detail="数据库查询异常")

    # 调用 WPS 服务生成预览链接
    try:
        user_id = "admin"
        wps_url = wps.run(str(uuid.uuid4()), file_path, user_id)
    except Exception as e:
        logger.exception(f"WPS 预览失败: {e}")
        wps_url = ""

    return {
        "status_code": 200,
        "data": {"file_name": body.file_name, "wps_url": wps_url},
    }
