# -*- encoding: utf-8 -*-
"""
@File   :session.py
@Time   :2025/6/10 13:33
<AUTHOR>
"""

from fastapi import APIRouter, Header
from loguru import logger

from app.core.llm.session.base import SessionManager
from app.v1.core.tokens import decode_token

router = APIRouter(prefix="/api/session", tags=["会话管理"])


@router.get("/new", summary="新建会话")
def get_session_id(Authorization: str = Header(None)):
    """
    获取新的会话ID（所有基于会话的模块都可共用）
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "content": "无权限访问"}

        session_manager = SessionManager()  # 使用抽象的通用管理器
        session_id = session_manager.create_session(username)

        return {
            "status_code": 200,
            "message": "会话ID创建成功",
            "data": {"session_id": session_id},
        }
    except Exception as e:
        logger.exception(f"获取新的会话ID失败: {e}")
        return {"status_code": 500, "message": "会话ID创建失败", "data": None}


@router.post("/{session_id}/reset", summary="重置会话清空上下文")
def reset_session_history(session_id: str, Authorization: str = Header(None)):
    """
    重置指定会话的历史上下文（不删除原始消息，仅重置记忆起点）
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        session_manager = SessionManager()
        session = session_manager.get_session(session_id)
        if not session:
            return {"status_code": 404, "message": "会话不存在", "data": None}

        # 重置逻辑：记录 reset_at 时间到 Redis 和 Mongo
        session_manager.reset_session_history(session_id=session_id)

        return {
            "status_code": 200,
            "message": "会话上下文已重置",
            "data": {"session_id": session_id},
        }

    except Exception as e:
        logger.exception(f"重置会话历史失败: {e}")
        return {"status_code": 500, "message": "会话上下文重置失败", "data": None}
