# -*- encoding: utf-8 -*-
"""
@File   :doc_reader_batch.py
@Time   :2025/6/29
<AUTHOR>
"""
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Header, HTTPException, Body, Query
from loguru import logger
from pydantic import BaseModel, Field

from app.core.llm.session.document_qa.doc_session import DocSessionManager
from app.v1.core.tokens import decode_token

router = APIRouter(prefix="/api/doc_reader", tags=["文档解读"])


def _ensure_datetime(dt):
    if isinstance(dt, datetime):
        return dt
    if isinstance(dt, str):
        try:
            return datetime.fromisoformat(dt).strftime("%Y-%m-%d %H:%M:%S")
        except ValueError:
            return None
    return None


class RenameRequest(BaseModel):
    session_id: str = Field(..., description="会话 ID")
    defined_name: str = Field(..., description="用户定义的新名称")
    modify_type: str = Field(..., description="修改类型（session/file）")
    file_id: Optional[str] = Field(None, description="文件 ID（当 modify_type 为 file 时必填）")


class PinRequest(BaseModel):
    session_id: str = Field(..., description="会话 ID")
    is_pinned: bool = Field(..., description="是否置顶会话（true 表示置顶）")


@router.get("/chat/list",
            description="获取文档解读所有会话的文档列表（支持单会话过滤）",
            summary="获取会话的文档列表")
async def get_session_batches(
        session_id: Optional[str] = None,
        Authorization: str = Header(None),
):
    """
    获取会话的文档批次列表，支持传入 session_id 精确查询，也可不传获取所有会话。
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        session_manager = DocSessionManager()

        # 获取会话列表（所有或指定）
        if session_id:
            raw_sessions = [session_manager.get_session(session_id)]
        else:
            raw_sessions = session_manager.get_session(user_id=username)

        result = []

        for session in raw_sessions:
            if not session:
                continue

            # 权限检查
            if session.user_id != username:
                continue


            session_dict = session.model_dump()
            all_files = session_dict.get("files", [])
            if not all_files:
                continue

            # 计算整体状态
            session_status = _calculate_session_status(all_files)

            # 主扩展名
            ext_names = [f.get("file_type", "") for f in all_files if f.get("file_type")]
            main_ext = max(set(ext_names), key=ext_names.count) if ext_names else ""

            # 上传时间
            upload_times = [f.get("upload_time") for f in all_files if f.get("upload_time")]
            create_time = min(upload_times) if upload_times else None
            update_time = max(upload_times) if upload_times else None

            # 用户自定义会话名
            batches = session_dict.get("batches") or []  # None 则替换为空列表

            defined_name = next(
                (b.get("defined_name") for b in reversed(batches) if b.get("defined_name")),
                None
            )
            is_pinned = any(b.get("is_pinned") for b in batches)

            # 构造数据
            session_data = {
                "chatId": session.session_id,
                "fileName": defined_name or _get_default_session_name(all_files),
                "createTime": _ensure_datetime(create_time) if create_time else "",
                "updateTime": _ensure_datetime(update_time) if update_time else "",
                "fileCount": len(all_files),
                "extName": main_ext,
                "status": session_status,
                "isPinned": is_pinned,
                "files": []
            }

            for file_info in all_files:
                upload_time = file_info.get("upload_time")
                time_str = _ensure_datetime(upload_time)
                session_data["files"].append({
                    "id": file_info.get("doc_id"),
                    "fileId": file_info.get("doc_id"),
                    "fileName": file_info.get("filename", "").split(".")[0],
                    "originFileName": file_info.get("filename", ""),
                    "extName": file_info.get("file_type", ""),
                    "fileStatus": file_info.get("file_status", "pending"),
                    "parseType": file_info.get("parse_type", "TEXT"),
                    "taskId": file_info.get("task_id"),
                    "createTime": time_str,
                    "updateTime": time_str,
                    "wpsUrl": file_info.get("wps_url", ""),
                    "errorMessage": file_info.get("error_message", "")
                })

            session_data["files"].sort(
                key=lambda x: x["createTime"] or "1970-01-01 00:00:00", reverse=True
            )
            result.append(session_data)

        if not result:
            return {"status_code": 200, "message": "请先上传文档", "data": []}
        # 基于时间降序
        result.sort(key=lambda x: x["createTime"] or "1970-01-01 00:00:00", reverse=True)

        return {
            "status_code": 200,
            "message": "获取文档会话列表成功",
            "data": result,
        }

    except Exception as e:
        logger.exception(f"Error while getting session batches: {e}")
        return {"status_code": 500, "message": "获取文档会话列表失败", "data": None}


@router.get("/chat/files",
            description="获取指定会话的所有文件信息",
            summary="获取会话文件")
async def get_session_files(
        session_id: str = Query(..., description="会话ID"),
        Authorization: str = Header(None),
):
    """
    获取指定会话下的文件信息（用于点击会话后加载文件详情）
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        session_manager = DocSessionManager()
        session = session_manager.get_session(session_id)

        if not session or session.user_id != username:
            return {"status_code": 404, "message": "会话不存在或无权限", "data": None}

        session_dict = session.model_dump()
        all_files = session_dict.get("files", []) or []
        if not all_files:
            return {"status_code": 200, "message": "请先上传文档", "data": []}

        result = []
        for file_info in all_files:
            upload_time = file_info.get("upload_time")
            time_str = _ensure_datetime(upload_time)

            result.append({
                "id": file_info.get("doc_id"),
                "fileId": file_info.get("doc_id"),
                "fileName": file_info.get("filename", "").split(".")[0],
                "originFileName": file_info.get("filename", ""),
                "extName": file_info.get("file_type", ""),
                "fileStatus": file_info.get("file_status", "pending"),
                "parseType": file_info.get("parse_type", "TEXT"),
                "taskId": file_info.get("task_id"),
                "createTime": time_str,
                "updateTime": time_str,
                "wpsUrl": file_info.get("wps_url", ""),
                "errorMessage": file_info.get("error_message", "")
            })

        # 按时间倒序
        result.sort(key=lambda x: x["createTime"] or "1970-01-01 00:00:00", reverse=True)

        return {
            "status_code": 200,
            "message": "获取文件列表成功",
            "data": result
        }

    except Exception as e:
        logger.exception(f"Error while getting session files: {e}")
        return {"status_code": 500, "message": "获取文件列表失败", "data": None}


@router.get("/file/status", description="查询文档解读任务状态", summary="查询文档解读任务状态")
async def get_task_status(
        file_ids: str,
        Authorization: str = Header(None),
):
    """
    查询文档解读任务状态
    支持多个file_id查询，用逗号分隔
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"flag": False, "code": 403, "desc": "无权限访问", "data": None}

        from app.core.doc_reader.adapter import DocReaderAdapter
        from app.configs.config import settings

        # 创建适配器
        adapter = DocReaderAdapter(
            parser_host=settings.parser_base_url,
            api_key=settings.parser_api_key
        )

        # 解析file_ids，支持逗号分隔的多个ID
        file_id_list = [fid.strip() for fid in file_ids.split(',') if fid.strip()]

        if not file_id_list:
            return {"flag": False, "code": 400, "desc": "file_ids参数不能为空", "data": None}

        # 查询多个任务状态
        result_data = []
        for file_id in file_id_list:
            task_status = adapter.get_task_status(file_id)

            if task_status:
                # 映射状态字段
                file_status = task_status.get("status", "unknown")
                # 将内部状态映射为前端需要的状态
                if file_status == "completed":
                    file_status = "completed"
                elif file_status in ["pending", "processing"]:
                    file_status = "processing"
                elif file_status == "failed":
                    file_status = "failed"
                else:
                    file_status = "unknown"

                result_data.append({
                    "fileStatus": file_status,
                    "fileId": file_id
                })
            else:
                # 任务不存在，标记为失败
                result_data.append({
                    "fileStatus": "failed",
                    "fileId": file_id
                })

        return {
            "flag": True,
            "code": 0,
            "desc": None,
            "data": result_data
        }

    except Exception as e:
        logger.exception(f"Error while getting task status: {e}")
        return {"flag": False, "code": 500, "desc": "获取任务状态失败", "data": None}


@router.post("/file/rename", description="重命名会话或文件", summary="重命名会话或文件")
async def rename_session(
        data: RenameRequest = Body(...),
        Authorization: str = Header(None),
):
    """
    重命名会话或文件
    - modify_type = "session": 重命名会话
    - modify_type = "file": 重命名文件（需要提供 file_id）
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        # 参数验证
        if data.modify_type not in ["session", "file"]:
            return {"status_code": 400, "message": "modify_type 必须为 session 或 file", "data": None}

        if data.modify_type == "file" and not data.file_id:
            return {"status_code": 400, "message": "重命名文件时必须提供 file_id", "data": None}
        if len(data.defined_name) > 30:
            return {"status_code": 400, "message": "名称长度不能超过30个字符", "data": None}

        session_manager = DocSessionManager()
        session = session_manager.get_session(data.session_id)

        if not session:
            return {"status_code": 404, "message": "会话不存在", "data": None}

        # 检查权限
        if session.user_id != username:
            return {"status_code": 403, "message": "无权限访问此会话", "data": None}

        if data.modify_type == "session":
            # 会话重命名逻辑
            # 更新MongoDB中的会话名称（在最新的batch中记录）
            # 如果没有batch，创建一个虚拟的batch记录来存储会话级别的设置
            session_manager.session_collection.update_one(
                {"session_id": data.session_id},
                {
                    "$set": {"session_name": data.defined_name},
                    "$push": {
                        "batches": {
                            "$each": [],
                            "$position": 0,
                            "$slice": -1  # 只保留最新的一个
                        }
                    }
                }
            )

            # 如果有batches，更新最新的batch
            batches = session.batches or []
            if batches:
                # 更新最新的batch
                session_manager.session_collection.update_one(
                    {"session_id": data.session_id},
                    {"$set": {"batches.0.defined_name": data.defined_name}}
                )
            else:
                # 创建一个新的batch记录
                new_batch = {
                    "batch_id": f"session_{data.session_id}",
                    "file_count": len(session.files or []),
                    "upload_time": session.created_at.isoformat(),
                    "defined_name": data.defined_name,
                    "is_pinned": False,
                    "main_ext": "",
                    "status": "completed"
                }
                session_manager.session_collection.update_one(
                    {"session_id": data.session_id},
                    {"$push": {"batches": new_batch}}
                )

            return {
                "status_code": 200,
                "message": "会话重命名成功",
                "data": {"session_id": data.session_id, "defined_name": data.defined_name},
            }

        elif data.modify_type == "file":
            # 文件重命名逻辑
            # 检查文件是否存在于会话中
            file_found = False
            original_filename = ""
            for file_info in session.files or []:
                if file_info.get("doc_id") == data.file_id:
                    file_found = True
                    original_filename = file_info.get("filename", "")
                    break

            if not file_found:
                return {"status_code": 404, "message": "文件不存在于此会话中", "data": None}

            # 保持文件扩展名不变，只修改文件名部分
            import os
            original_name, ext = os.path.splitext(original_filename)
            new_filename = data.defined_name + ext

            # 更新会话中的文件信息
            session_manager.session_collection.update_one(
                {"session_id": data.session_id, "files.doc_id": data.file_id},
                {"$set": {"files.$.filename": new_filename}}
            )

            # 更新文档集合中的文件名
            session_manager.file_manager.collection.update_one(
                {"doc_id": data.file_id},
                {"$set": {"file_name": new_filename}}
            )

            return {
                "status_code": 200,
                "message": "文件重命名成功",
                "data": {
                    "session_id": data.session_id,
                    "file_id": data.file_id,
                    "old_filename": original_filename,
                    "new_filename": new_filename
                },
            }

    except Exception as e:
        logger.exception(f"Error while renaming session: {e}")
        return {"status_code": 500, "message": "重命名失败", "data": None}


@router.post("/file/pin", description="置顶/取消置顶会话", summary="置顶/取消置顶会话")
async def pin_session(
        request: PinRequest = Body(...),
        Authorization: str = Header(None),
):
    """
    置顶/取消置顶会话
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        session_manager = DocSessionManager()
        session = session_manager.get_session(request.session_id)

        if not session:
            return {"status_code": 404, "message": "会话不存在", "data": None}

        # 检查权限
        if session.user_id != username:
            return {"status_code": 403, "message": "无权限访问此会话", "data": None}

        # 更新MongoDB中的会话置顶状态
        # 如果有batches，更新最新的batch
        batches = session.batches or []
        if batches:
            # 更新最新的batch
            session_manager.session_collection.update_one(
                {"session_id": request.session_id},
                {"$set": {"batches.0.is_pinned": request.is_pinned}}
            )
        else:
            # 创建一个新的batch记录
            new_batch = {
                "batch_id": f"session_{request.session_id}",
                "file_count": len(session.files or []),
                "upload_time": session.created_at.isoformat(),
                "defined_name": None,
                "is_pinned": request.is_pinned,
                "main_ext": "",
                "status": "completed"
            }
            session_manager.session_collection.update_one(
                {"session_id": request.session_id},
                {"$push": {"batches": new_batch}}
            )

        return {
            "status_code": 200,
            "message": "置顶状态更新成功",
            "data": {"session_id": request.session_id, "is_pinned": request.is_pinned},
        }

    except Exception as e:
        logger.exception(f"Error while updating pin status: {e}")
        return {"status_code": 500, "message": "置顶状态更新失败", "data": None}


def _calculate_batch_status(files: List[dict]) -> str:
    """计算批次整体状态"""
    if not files:
        return "pending"

    statuses = [f.get("file_status", "pending") for f in files]

    if all(s == "completed" for s in statuses):
        return "completed"
    elif any(s == "failed" for s in statuses):
        return "mixed" if any(s == "completed" for s in statuses) else "failed"
    elif any(s == "processing" for s in statuses):
        return "processing"
    else:
        return "pending"


def _calculate_session_status(files: List[dict]) -> str:
    """计算会话状态（所有文件的整体状态）"""
    if not files:
        return "pending"

    statuses = [f.get("file_status", "pending") for f in files]

    if all(s == "completed" for s in statuses):
        return "completed"
    elif any(s == "failed" for s in statuses):
        return "mixed" if any(s == "completed" for s in statuses) else "failed"
    elif any(s == "processing" for s in statuses):
        return "processing"
    else:
        return "pending"


def _get_default_batch_name(files: List[dict]) -> str:
    """获取批次默认名称"""
    if not files:
        return "空批次"

    if len(files) == 1:
        return files[0].get("filename", "未知文件")
    else:
        return "多文档问答"


def _get_default_session_name(files: List[dict]) -> str:
    """生成默认会话名称"""
    if not files:
        return "未命名会话"

    if len(files) == 1:
        filename = files[0].get("filename", "未知文件")
        return filename.split(".")[0][:30]
    else:
        return f"多文档问答"
