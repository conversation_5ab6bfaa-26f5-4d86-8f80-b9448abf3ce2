# -*- encoding: utf-8 -*-
"""
@File   :doc_reader.py
@Time   :2025/6/10 10:12
<AUTHOR>
"""
import io
import os
import uuid
from datetime import datetime, timezone
from typing import List, Optional

from bson import ObjectId
from fastapi import APIRouter, Header, Query, UploadFile, File, Form, HTTPException, Body
from loguru import logger
from starlette.responses import StreamingResponse
from wps_callback_kit import wps

from app.core.db.manager import DBManager
from app.core.db.models import DocReaderTask
from app.core.llm.session.document_qa.doc_session import DocSessionManager
from app.core.llm.session.model import Message
from app.schemas.doc_reader import ChatRequest, ChatMessageRequest, FileDeleteRequest, BatchFileDeleteRequest, \
    FileRestoreRequest
from app.services.doc_reader import error_stream
from app.v1.core.tokens import decode_token, download_file, download_single_law_csv
from app.v1.core.utils import get_dir, is_audio_file

from app.v1.db.user_project import query_project
from app.v1.db.permission_info import get_project
import os
import uuid

router = APIRouter(prefix="/api/doc_reader", tags=["文档解读"])


@router.get("/session_id")
def get_session_id(Authorization: str = Header(None)):
    """
    获取新的会话ID
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "content": "无权限访问"}
        session_manager = DocSessionManager()
        session_id = session_manager.create_session(username)
        return {
            "status_code": 200,
            "message": "会话ID创建成功",
            "data": {"session_id": session_id},
        }
    except Exception as e:
        logger.exception(f"获取新的会话ID失败: {e}")
        return {"status_code": 500, "message": "会话ID创建失败", "data": None}


# @router.get("/session_record")
def get_session_record(
        session_id: str = Query(..., description="会话ID"), Authorization: str = Header(None)
):
    """
    获取指定会话的对话记录
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        # 伪代码：根据 session_id 查询会话内容
        session_manager = DocSessionManager()
        records = session_manager.get_session(session_id)
        if not records:
            return {"status_code": 404, "message": "会话不存在", "data": None}
        return {
            "status_code": 200,
            "message": "获取会话记录成功",
            "data": {"session_id": session_id, "records": records.model_dump(exclude={"metadata"})},
        }
    except Exception as e:
        logger.exception(f"获取会话记录失败: {e}")
        return {"status_code": 500, "message": "获取会话记录失败", "data": None}


@router.post("/file", description="批量上传文件进行文档解读", summary="批量上传文件进行文档解读")
async def upload_files(
        file: List[UploadFile] = File(..., description="待上传的文件列表"),
        session_id: str = Form(..., description="会话ID"),
        Authorization: str = Header(None),
):
    """
    批量上传文件进行文档解读
    """
    files = file
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        logger.info(f"批量上传文件: session_id={session_id}, file_count={len(files)}")

        # 导入适配器
        from app.core.doc_reader.adapter import DocReaderAdapter
        from app.configs.config import settings

        doc_reader_adapter = DocReaderAdapter(
            parser_host=settings.parser_base_url,
            api_key=settings.parser_api_key
        )

        file_infos = []
        success_files = []
        failed_files = []

        # 只读取文件内容，不进行阻塞的文档解析
        for file in files:
            try:
                # 读取文件内容
                content = await file.read()
                filename = file.filename

                # 基本文件验证
                if not filename or len(content) == 0:
                    raise Exception("文件为空或文件名无效")

                # 检查文件大小（可选）
                # if len(content) > 100 * 1024 * 1024:  # 100MB限制
                #     raise Exception("文件大小超过限制")

                # 根据文件扩展名判断文件类型
                file_ext = os.path.splitext(filename)[1].lower()
                file_type = file_ext[1:] if file_ext else "unknown"

                # 生成临时的doc_id（后续异步任务中会创建真正的文档记录）
                temp_doc_id = str(uuid.uuid4())

                file_infos.append({
                    "temp_doc_id": temp_doc_id,
                    "filename": filename,
                    "file_bytes": content,
                    "file_type": file_type,
                    "file_size": len(content)
                })

                success_files.append({
                    "temp_doc_id": temp_doc_id,
                    "filename": filename,
                    "file_type": file_type,
                    "file_size": len(content)
                })

            except Exception as parse_error:
                logger.error(f"文件预处理失败: {file.filename}, error={parse_error}")
                failed_files.append({
                    "filename": file.filename,
                    "error": str(parse_error)
                })
                # 不要直接返回，继续处理其他文件

        # 如果所有文件都失败了，才返回错误
        if not success_files:
            return {
                "status_code": 422,
                "message": "所有文件预处理失败",
                "data": {"failed_files": failed_files}
            }

        # 批量提交异步任务（包含文档保存和解析）
        batch_result = doc_reader_adapter.submit_batch_tasks(
            username=username,
            session_id=session_id,
            file_infos=file_infos
        )

        batch_id = batch_result["batch_id"]
        task_ids = batch_result["task_ids"]

        # 构造返回数据
        response_data = {
            "batch_id": batch_id,
            "file_ids": task_ids,
            "success_count": len(success_files),
            "failed_count": len(failed_files),
            "files": success_files
        }

        if failed_files:
            response_data["failed_files"] = failed_files

        message = f"批量上传完成，成功提交 {len(success_files)} 个异步任务"
        if failed_files:
            message += f"，失败 {len(failed_files)} 个文件"

        return {
            "status_code": 200,
            "message": message,
            "data": response_data,
        }
    except Exception as e:
        logger.exception(f"文件上传失败: {e}")
        return {"status_code": 500, "message": "文件上传失败", "data": None}


@router.post("/chat/completions",
             description="文档解读 Chat 接口，对话问答（流式输出）",
             summary="文档解读 Chat 接口，对话问答（流式输出）")
async def chat_completions(data: ChatRequest, Authorization: str = Header(None)):
    """
    文档解读 Chat 接口，对话问答（流式输出）
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return error_stream("🚫 无权限访问")

        session_id = data.session_id
        message = data.message
        session_manager = DocSessionManager()
        session_info = session_manager.get_session(session_id, "doc_ids")
        if not session_info:
            return error_stream("😄 请上传文档后问答")

        def stream_generator():
            for chunk in session_manager.doc_stream_ask(session_id, message):
                if isinstance(chunk, str):
                    yield chunk

        # 指定 text/event-stream 适配SSE
        return StreamingResponse(stream_generator(), media_type="text/event-stream")

    except Exception as e:
        logger.exception(f"文档解读chat接口失败: {e}")
        return error_stream("❌ 文档解读服务器错误")


@router.post("/select_knowledge_files", description="从知识库选择文档进行多文档解读",
             summary="从知识库选择文档进行多文档解读")
async def select_knowledge_files(
        file_ids: List[str] = Body(..., description="选中的文件ID列表"),
        session_id: str = Body(..., description="会话ID"),
        project_id: str = Body(..., description="项目ID"),
        is_private: bool = Body(True, description="是否私人库"),
        Authorization: str = Header(None),
):
    """
    从个人库/知识库选择文档进行多文档解读
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        logger.info(f"从知识库选择文档: session_id={session_id}, file_count={len(file_ids)}, project_id={project_id}")
        if not session_id:
            return {"status_code": 400, "message": "会话不存在", "data": None}
        if not project_id:
            return {"status_code": 400, "message": "知识库不存在", "data": None}
        if not isinstance(is_private, bool):
            return {"status_code": 400, "message": "未明确的知识库", "data": None}

        # 导入适配器
        from app.core.doc_reader.adapter import DocReaderAdapter
        from app.configs.config import settings
        from app.v1.models.FileInfo import File

        doc_reader_adapter = DocReaderAdapter(
            parser_host=settings.parser_base_url,
            api_key=settings.parser_api_key
        )

        # 根据file_ids查询文件信息
        files_info = []
        success_files = []
        failed_files = []

        # 获取项目信息用于构造文件路径
        projects = list(query_project(username, is_private))
        if not is_private:
            # 如果是项目库，还需要获取有权限的项目
            projects.extend(get_project(username))

        project_info = None
        for proj in projects:
            if proj.project_id == project_id:
                project_info = proj
                break

        if not project_info:
            return {
                "status_code": 404,
                "message": "项目不存在或无权限访问",
                "data": None
            }

        project_name = project_info.project_name

        for file_id in file_ids:
            try:
                # 查询文件信息
                file_obj = File.objects.filter(id=ObjectId(file_id), project_id=project_id).first()
                if not file_obj:
                    failed_files.append({
                        "file_id": file_id,
                        "error": "文件不存在"
                    })
                    continue

                # 构造文件路径
                file_dir = get_dir(is_private, username, project_name)
                os.makedirs(file_dir, exist_ok=True)
                file_path = os.path.join(file_dir, file_obj.file_name)

                # 检查文件是否存在，不存在则下载
                if not os.path.exists(file_path):
                    if file_obj.source == 'sacp' and not is_private:
                        # 项目库文件下载
                        download_result = download_file(
                            username, project_name, file_obj.file_id,
                            file_obj.file_name, file_dir, file_obj.filename_extension
                        )
                        if download_result:
                            failed_files.append({
                                "file_id": file_id,
                                "filename": file_obj.file_name,
                                "error": "项目库文件下载失败"
                            })
                            continue
                    elif file_obj.source == 'law':
                        # 法规库文件下载
                        if not download_single_law_csv(file_obj.file_id, file_path):
                            failed_files.append({
                                "file_id": file_id,
                                "filename": file_obj.file_name,
                                "error": "法规库文件下载失败"
                            })
                            continue
                    else:
                        failed_files.append({
                            "file_id": file_id,
                            "filename": file_obj.file_name,
                            "error": "文件不存在且无法下载"
                        })
                        continue

                # 读取文件内容
                with open(file_path, 'rb') as f:
                    file_content = f.read()

                # 构造文件信息，符合DocReaderAdapter期望的格式
                temp_doc_id = str(uuid.uuid4())
                file_ext = os.path.splitext(file_obj.file_name)[1][1:].lower()  # 去掉点号

                file_info = {
                    "temp_doc_id": temp_doc_id,
                    "filename": file_obj.file_name,
                    "file_bytes": file_content,
                    "file_type": file_ext,
                    "file_size": len(file_content)
                }

                files_info.append(file_info)
                success_files.append({
                    "file_id": file_id,
                    "filename": file_obj.file_name,
                    "size": len(file_content)
                })

            except Exception as file_error:
                logger.error(f"处理文件失败: file_id={file_id}, error={file_error}")
                failed_files.append({
                    "file_id": file_id,
                    "error": str(file_error)
                })

        # 如果所有文件都失败了，返回错误
        if not files_info:
            return {
                "status_code": 422,
                "message": "所有文件处理失败",
                "data": {"failed_files": failed_files}
            }

        # 批量提交异步任务（包含文档保存和解析）
        batch_result = doc_reader_adapter.submit_batch_tasks(
            username=username,
            session_id=session_id,
            file_infos=files_info
        )

        batch_id = batch_result["batch_id"]
        task_ids = batch_result["task_ids"]

        # 构造返回数据
        response_data = {
            "batch_id": batch_id,
            "file_ids": task_ids,
            "success_count": len(success_files),
            "failed_count": len(failed_files),
            "files": success_files
        }

        if failed_files:
            response_data["failed_files"] = failed_files

        return {
            "status_code": 200,
            "message": "知识库文档选择成功",
            "data": response_data
        }

    except Exception as e:
        logger.exception(f"Error in select knowledge files: {e}")
        return {
            "status_code": 500,
            "message": f"选择知识库文档失败: {str(e)}",
            "data": None
        }


@router.get("/file/{session_id}/{doc_id}")
async def get_file_data(
        session_id: str, doc_id: str, Authorization: str = Header(None)
):
    """
    获取指定 session_id 和 doc_id 的文件二进制数据
    """
    try:
        username = decode_token(Authorization)
        if not username:
            raise HTTPException(status_code=403, detail="无权限访问")
        # 初始化 FileManager 和 SessionManager
        session_manager = DocSessionManager()
        # 检查会话是否存在
        session_status = session_manager.check_session_and_doc_status(
            session_id, username, doc_id
        )
        if session_status:
            raise HTTPException(status_code=404, detail=session_status)
        # 查找文件信息
        file_info = session_manager.file_manager.get_file_info(session_id)
        if not file_info:
            raise HTTPException(status_code=404, detail="找不到文件")

        # 获取文件路径
        file_path = file_info.get("file_path")
        if not file_path or not os.path.isfile(file_path):
            raise HTTPException(status_code=404, detail="磁盘上不存在文件")

        # 读取文件内容并返回
        with open(file_path, "rb") as file:
            file_data = io.BytesIO(file.read())

        # 返回二进制文件
        return StreamingResponse(
            file_data,
            media_type="application/octet-stream",
            headers={
                "Content-Disposition": f"attachment; filename={file_info.get('filename')}"
            },
        )
    except HTTPException as e:
        # 抛出 HTTP 异常
        raise e
    except Exception as e:
        # 日志记录异常
        logger.exception(f"Error while getting file data: {e}")
        raise HTTPException(status_code=500, detail="检索文件数据失败")


@router.post("/file/delete")
async def delete_file(data: FileDeleteRequest, Authorization: str = Header(None)):
    """
    删除指定 doc_id / temp_doc_id / task_id 的文件，并移除会话中的关联，
    同时记录到 deleted_files，可选地取消异步任务和清理文件。
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        session_manager = DocSessionManager()
        is_temp_doc = False

        # 1. 查询文件信息
        file_info = session_manager.file_manager.get_file_info(data.doc_id, all_fields=True)

        # 查询 session，如果后面需要取消任务等操作用得到
        session_record = session_manager.get_session(data.session_id)

        # 如果没查到，去 session 中找 temp_doc_id / task_id 对应的文件
        if not file_info and session_record and session_record.files:
            for file_record in session_record.files:
                if data.doc_id in [
                    file_record.get("temp_doc_id"),
                    file_record.get("task_id"),
                ]:
                    file_info = {
                        "doc_id": file_record.get("doc_id"),
                        "temp_doc_id": file_record.get("temp_doc_id"),
                        "task_id": file_record.get("task_id"),
                        "file_name": file_record.get("filename"),
                        "file_size": file_record.get("file_size", 0),
                        "file_type": file_record.get("file_type"),
                        "char_count": 0,
                    }
                    is_temp_doc = True
                    break

        if not file_info:
            return {"status_code": 404, "message": "未找到文件", "data": None}

        # 2. 校验权限
        if not session_record:
            session_record = session_manager.get_session(data.session_id)
        if not session_record:
            return {"status_code": 404, "message": "会话不存在", "data": None}
        if session_record.user_id != username:
            return {"status_code": 403, "message": "无权限访问此会话", "data": None}

        total_chars = session_record.total_chars
        char_count = file_info.get("char_count", 0)
        total_chars -= char_count

        # 3. 构建删除记录
        deleted_file_record = {
            "doc_id": file_info.get("doc_id") or data.doc_id,
            "temp_doc_id": file_info.get("temp_doc_id") if is_temp_doc else None,
            "file_name": file_info.get("file_name"),
            "file_type": file_info.get("file_type"),
            "wps_url": file_info.get("wps_url", ""),
            "deleted_at": datetime.now().isoformat(),
        }

        # 4. 添加到 deleted_files，同时更新总字符数
        session_manager.session_collection.update_one(
            {"session_id": data.session_id},
            {
                "$push": {"deleted_files": deleted_file_record},
                "$set": {"total_chars": total_chars},
            },
        )

        # 5. 移除 files 中的记录（支持 doc_id / temp_doc_id / task_id）
        session_manager.session_collection.update_one(
            {"session_id": data.session_id},
            {
                "$pull": {
                    "files": {
                        "$or": [
                            {"doc_id": data.doc_id},
                            {"temp_doc_id": data.doc_id},
                            {"task_id": data.doc_id},
                        ]
                    }
                }
            },
        )

        # 6. 如果是正式文档，则从 doc_ids 中移除
        if file_info.get("doc_id"):
            session_manager.session_collection.update_one(
                {"session_id": data.session_id},
                {"$pull": {"doc_ids": file_info["doc_id"]}},
            )

        # 7. 如果是临时文档，尝试取消异步任务并清理临时文件
        if is_temp_doc:
            try:
                with DBManager.get_mysql().session() as db:
                    task = db.query(DocReaderTask).filter(
                        DocReaderTask.session_id == data.session_id,
                        DocReaderTask.meta_info.contains(f'"temp_doc_id": "{data.doc_id}"')
                    ).first()

                    if task and task.status in ["pending", "processing"]:
                        task.status = "cancelled"
                        task.updated_at = datetime.now()
                        db.commit()
                        logger.info(f"已取消异步任务: task_id={task.task_id}, temp_doc_id={data.doc_id}")

                        # 清理临时文件
                        if task.meta_info and "temp_file_path" in task.meta_info:
                            temp_file_path = task.meta_info.get("temp_file_path")
                            if temp_file_path and os.path.isfile(temp_file_path):
                                try:
                                    os.remove(temp_file_path)
                                    logger.info(f"已清理临时文件: {temp_file_path}")
                                except Exception as e:
                                    logger.warning(f"清理临时文件失败: {temp_file_path}, error={e}")
            except Exception as e:
                logger.warning(f"取消异步任务失败: temp_doc_id={data.doc_id}, error={e}")

        # 8. 可选：物理删除文档（已注释）
        # try:
        #     session_manager.file_manager.delete_document(data.doc_id)
        #     logger.info(f"已物理删除文档数据: {data.doc_id}")
        # except Exception as e:
        #     logger.warning(f"物理删除文档数据失败: {e}")

        return {
            "status_code": 200,
            "message": "文件删除成功",
            "data": {
                "doc_id": data.doc_id,
                "is_temp_doc": is_temp_doc,
                "filename": file_info.get("file_name")
            },
        }

    except Exception as e:
        logger.exception(f"文件删除失败: {e}")
        return {"status_code": 500, "message": "文件删除失败", "data": None}



@router.post("/file/batch_delete")
async def batch_delete_files(data: BatchFileDeleteRequest, Authorization: str = Header(None)):
    """
    批量删除指定 doc_ids 的文件，并移除会话中的关联
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        session_manager = DocSessionManager()

        # 检查会话权限
        session_record = session_manager.get_session(data.session_id)
        if not session_record:
            return {"status_code": 404, "message": "会话不存在", "data": None}

        if session_record.user_id != username:
            return {"status_code": 403, "message": "无权限访问此会话", "data": None}

        deleted_files = []
        failed_files = []
        total_chars_reduced = 0

        for doc_id in data.doc_ids:
            try:
                # 查询文件信息
                file_info = session_manager.file_manager.get_file_info(doc_id, all_fields=True)
                if not file_info:
                    failed_files.append({"doc_id": doc_id, "reason": "文件不存在"})
                    continue

                char_count = file_info.get("char_count", 0)
                total_chars_reduced += char_count

                # 组装删除记录
                deleted_file_record = {
                    "doc_id": doc_id,
                    "file_name": file_info.get("file_name"),
                    "file_type": file_info.get("file_type"),
                    "wps_url": file_info.get("wps_url"),
                    "deleted_at": datetime.now().isoformat(),
                }

                # 添加到 deleted_files
                session_manager.session_collection.update_one(
                    {"session_id": data.session_id},
                    {"$push": {"deleted_files": deleted_file_record}},
                )

                # 从 files 和 doc_ids 中移除
                session_manager.session_collection.update_one(
                    {"session_id": data.session_id},
                    {"$pull": {"files": {"doc_id": doc_id}}},
                )
                session_manager.session_collection.update_one(
                    {"session_id": data.session_id},
                    {"$pull": {"doc_ids": doc_id}}
                )

                deleted_files.append(doc_id)

            except Exception as e:
                logger.warning(f"删除文件 {doc_id} 失败: {e}")
                failed_files.append({"doc_id": doc_id, "reason": str(e)})

        # 更新总字符数
        if total_chars_reduced > 0:
            session_manager.session_collection.update_one(
                {"session_id": data.session_id},
                {"$inc": {"total_chars": -total_chars_reduced}},
            )

        return {
            "status_code": 200,
            "message": f"批量删除完成，成功删除 {len(deleted_files)} 个文件",
            "data": {
                "session_id": data.session_id,
                "deleted_files": deleted_files,
                "failed_files": failed_files,
                "total_deleted": len(deleted_files),
                "total_failed": len(failed_files)
            },
        }
    except Exception as e:
        logger.exception(f"批量删除文件失败: {e}")
        return {"status_code": 500, "message": "批量删除文件失败", "data": None}


@router.post("/file/restore")
async def restore_file(data: FileRestoreRequest, Authorization: str = Header(None)):
    """
    恢复已删除的文件
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        session_manager = DocSessionManager()

        # 检查会话权限
        session_record = session_manager.get_session(data.session_id)
        if not session_record:
            return {"status_code": 404, "message": "会话不存在", "data": None}

        if session_record.user_id != username:
            return {"status_code": 403, "message": "无权限访问此会话", "data": None}

        # 查找要恢复的文件
        deleted_files = session_record.deleted_files or []
        target_file = None
        for deleted_file in deleted_files:
            if deleted_file.get("doc_id") == data.doc_id:
                target_file = deleted_file
                break

        if not target_file:
            return {"status_code": 404, "message": "未找到要恢复的文件", "data": None}

        # 检查文档数据是否还存在
        file_info = session_manager.file_manager.get_file_info(data.doc_id, all_fields=True)
        if not file_info:
            return {"status_code": 404, "message": "文档数据已丢失，无法恢复", "data": None}

        # 恢复文件：从 deleted_files 移除，添加到 files 和 doc_ids
        char_count = file_info.get("char_count", 0)

        # 重新构建文件信息
        restored_file = {
            "doc_id": data.doc_id,
            "filename": target_file.get("file_name"),
            "file_type": target_file.get("file_type"),
            "file_status": "completed",  # 假设之前已完成处理
            "upload_time": datetime.now().isoformat(),
            "wps_url": target_file.get("wps_url", ""),
            "task_id": "",  # 恢复的文件没有新的task_id
            "parse_type": "TEXT"  # 默认类型
        }

        # 更新MongoDB
        session_manager.session_collection.update_one(
            {"session_id": data.session_id},
            {
                "$pull": {"deleted_files": {"doc_id": data.doc_id}},
                "$push": {
                    "files": restored_file,
                    "doc_ids": data.doc_id
                },
                "$inc": {"total_chars": char_count}
            }
        )

        return {
            "status_code": 200,
            "message": "文件恢复成功",
            "data": {"doc_id": data.doc_id, "filename": target_file.get("file_name")},
        }

    except Exception as e:
        logger.exception(f"恢复文件失败: {e}")
        return {"status_code": 500, "message": "恢复文件失败", "data": None}


@router.post("/session/delete")
async def delete_session(
        session_id: str = Form(..., description="会话ID"),
        Authorization: str = Header(None)
):
    """
    删除整个会话及其所有相关数据

    Args:
        session_id: 会话ID
        Authorization: 认证头

    Returns:
        删除结果
    """
    try:
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        session_manager = DocSessionManager()

        # 检查会话是否存在
        session = session_manager.get_session(session_id)
        if not session:
            return {"status_code": 404, "message": "会话不存在", "data": None}

        # 检查权限
        if session.user_id != username:
            return {"status_code": 403, "message": "无权限删除此会话", "data": None}

        # 逻辑删除会话
        session_manager.soft_delete_session(session_id)

        # # 获取会话中的所有文档ID（包括已删除的）
        # all_doc_ids = session.doc_ids or []
        # deleted_files = session.deleted_files or []
        # deleted_doc_ids = [f.get("doc_id") for f in deleted_files if f.get("doc_id")]
        # all_doc_ids.extend(deleted_doc_ids)
        #
        # # 删除所有相关文档数据
        # for doc_id in all_doc_ids:
        #     try:
        #         session_manager.file_manager.delete_document(doc_id)
        #     except Exception as e:
        #         logger.warning(f"删除文档 {doc_id} 失败: {e}")
        #
        # # 删除相关的任务状态记录
        # try:
        #     from app.core.db.manager import DBManager
        #     from app.core.db.models.doc_reader_task import DocReaderTask
        #
        #     with DBManager.get_mysql().session() as db:
        #         # 删除该会话的所有任务记录
        #         deleted_tasks = db.query(DocReaderTask).filter(
        #             DocReaderTask.session_id == session_id
        #         ).delete()
        #         db.commit()
        #         logger.info(f"已删除 {deleted_tasks} 个任务记录")
        # except Exception as e:
        #     logger.warning(f"删除任务记录失败: {e}")
        #
        # # 删除会话的消息记录（Redis + MongoDB）
        # session_manager.memory_manager.delete_session(session_id)
        #
        # # 删除会话记录
        # session_manager.delete_session(session_id)

        return {
            "status_code": 200,
            "message": "会话删除成功",
            "data": {"session_id": session_id}
        }

    except Exception as e:
        logger.exception(f"删除会话失败: {e}")
        return {"status_code": 500, "message": "删除会话失败", "data": None}


@router.post("/chat/message")
async def get_chat_messages(
        data: ChatMessageRequest,
        Authorization: str = Header(None)
):
    """
    获取会话的分页历史消息列表

    Args:
        data: 消息查询请求数据
        Authorization: 认证头

    Returns:
        包含消息列表和分页信息的响应
    """
    try:
        # 验证用户权限
        username = decode_token(Authorization)
        if not username:
            return {"status_code": 403, "message": "无权限访问", "data": None}

        session_manager = DocSessionManager()

        # 检查会话是否存在
        session = session_manager.get_session(data.session_id)
        if not session:
            return {"status_code": 404, "message": "会话不存在", "data": None}

        # 计算分页参数
        if data.page is not None and data.page_size is not None:
            skip = (data.page - 1) * data.page_size
            limit = data.page_size
            # 获取分页消息列表（优先Redis，没有再查MongoDB）
            messages = session_manager.memory_manager.get_messages(
                session_id=data.session_id,
                role=data.filter_role,
                keyword=data.keyword,
                limit=limit,
                skip=skip
            )

            # 获取总消息数（用于分页计算）
            # 使用优化的count方法，直接从Redis计数器或MongoDB count_documents获取
            total = session_manager.memory_manager.get_messages_count(
                session_id=data.session_id,
                role=data.filter_role,
                keyword=data.keyword
            )

            # 计算是否还有更多数据
            has_more = skip + len(messages) < total

            # 构造响应数据
            response_data = {
                "messages": [msg.model_dump() for msg in messages],
                "total": total,
                "page": data.page,
                "page_size": data.page_size,
                "has_more": has_more
            }

            return {
                "status_code": 200,
                "message": "获取消息成功",
                "data": response_data
            }
        else:
            # 获取分页消息列表（优先Redis，没有再查MongoDB）
            messages = session_manager.memory_manager.get_messages(
                session_id=data.session_id,
                role=data.filter_role,
                keyword=data.keyword,
                limit=None,
                skip=None
            )
            # 构造响应数据
            response_data = {
                "messages": [msg.model_dump() for msg in messages],
            }

            return {
                "status_code": 200,
                "message": "获取消息成功",
                "data": response_data
            }



    except Exception as e:
        logger.exception(f"获取消息失败: {e}")
        return {"status_code": 500, "message": f"获取消息失败: {str(e)}", "data": None}
