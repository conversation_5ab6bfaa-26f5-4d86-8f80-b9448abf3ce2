# -*- encoding: utf-8 -*-
"""
@File    : UserInfo.py
@Time    : 2023/4/6 9:27
<AUTHOR> Qu ming
"""

from mongoengine import Document, StringField, DateTimeField, IntField, DecimalField
from datetime import datetime


class User(Document):
    name = StringField(required=True)
    question = StringField(required=True)
    reply = StringField(default=None)
    prompt_tokens = IntField(default=0)
    completion_tokens = IntField(default=0)
    charge = DecimalField(scale=7, default=0)
    fee = StringField(default=None)
    robot_name = StringField(default=None)
    type = StringField(default=None)
    model = StringField(default=None)
    source = StringField(default=None)
    assistant_id = StringField(default=None)
    project_id = StringField(default=None)
    create_time = DateTimeField(required=True, default=datetime.now)
