# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/8 19:42
@Auth ： qu ming
@File ：FileInfo.py
@IDE ：PyCharm
"""

from mongoengine import Document, StringField, DateTimeField, BooleanField, IntField, DynamicDocument
from datetime import datetime


class File(DynamicDocument):
    name = StringField(required=True)
    file_name = StringField(required=True)
    source = StringField(default=None)
    filename_extension = StringField(default=None)
    is_study = BooleanField(default=False)
    url = StringField(default=None)
    file_id = StringField(default=None)
    size = IntField(default=None)
    abstract = StringField(default=None)
    project_id = StringField(default=None)
    is_almond_parser = BooleanField(default=False)
    almond_parser_id = StringField(default=None)
    create_time = DateTimeField(required=True, default=datetime.now)

    meta = {
        'strict': False  # 允许未定义字段存在
    }
