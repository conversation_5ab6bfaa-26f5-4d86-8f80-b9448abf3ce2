# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/11 15:42
@Auth ： qu ming
@File ：UserProject.py
@IDE ：PyCharm
"""
import os

from mongoengine import Document, StringField, DateTimeField, BooleanField, <PERSON><PERSON><PERSON>
from datetime import datetime


class Project(Document):
    user = StringField(required=True)
    project_permission = ListField()
    project_name = StringField(required=True)
    project_id = StringField(required=True)
    project_introduce = StringField(required=True)
    file_dir = StringField(required=True)
    is_private = BooleanField(default=True)
    embedding_files = StringField(required=True)
    qa = BooleanField(default=False)
    assistant_id = StringField(required=False)
    create_time = DateTimeField(required=True, default=datetime.now)

    def to_dict(self):
        return {
            "username": self.user,
            'project_permission': self.project_permission,
            "project_name": self.project_name,
            "project_id": self.project_id,
            "project_introduce": self.project_introduce,
            "file_dir": self.file_dir,
            'file_names': os.listdir(self.file_dir),
            'is_private': self.is_private,
            "create_time": self.create_time.strftime('%Y-%m-%d %H:%M:%S'),
        }

    class Meta:
        table = "project"  # 数据库中的表名称
        table_description = '记录表'
