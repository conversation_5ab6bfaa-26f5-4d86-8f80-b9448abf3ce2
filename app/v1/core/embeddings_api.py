# -*- coding: utf-8 -*-
"""
@Time ： 2023/8/18 17:27
@Auth ： qu ming
@File ：embeddings_api.py
@IDE ：PyCharm
"""
import json
from typing import List

import requests


def get_embeddings(Authorization: str,
                   robot_name: str,
                   question: str):
    """
    根据api调取法律法规的回答

    """
    url = "http://172.17.6.126:9000/api/ChatServer"
    headers = {
        'content-type': 'application/json',
        'Authorization': Authorization,
    }
    data = {'robot_name': robot_name, 'question': question}
    try:
        content = requests.post(url, data=json.dumps(data), headers=headers, timeout=60).json()['content']
    except Exception as e:
        print(e)
        content = '财务数据暂时无法访问'
    return content


def train_api(name: str, username: str, file_name: str, texts: List):
    """
    根据api去训练文章的回答

    """
    url = "http://172.17.6.126:9000/api/train_robot"

    data = {'robot_name': name,
            'username': username,
            'file_name': file_name,
            'texts': texts}
    try:
        content = requests.post(url, data=json.dumps(data), timeout=60).json()['content']
    except Exception as e:
        print(e)
        content = '输出访问超时'
    return content


def query_embeddings_api(Authorization: str):
    """
    查询embeddings

    """
    url = "http://172.17.6.126:9000/api/query_embeddings"
    headers = {
        'content-type': 'application/json',
        'Authorization': Authorization,
    }
    try:
        content = requests.get(url, headers=headers, timeout=60).json()['content']
    except Exception as e:
        print(e)
        content = '输出访问超时'
    return content


if __name__ == '__main__':
   pass