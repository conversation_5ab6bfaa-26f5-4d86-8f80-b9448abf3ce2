# -*- coding: utf-8 -*-
"""
@Time ： 2023/9/1 10:20
@Auth ： qu ming
@File ：make_data.py
@IDE ：PyCharm
"""
from PyPDF2 import PdfReader
from langchain_community.document_loaders import UnstructuredFileLoader
from langchain_community.document_loaders import UnstructuredWordDocumentLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter


def split_document(file_path):
    """
    根据输入的文件路径去生成向量文件
    """

    # 初始化文本分割器
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=30
    )

    if '.txt' in file_path:
        loader = UnstructuredFileLoader(file_path)
        texts = text_splitter.split_documents(loader.load())
        return texts
    elif '.pdf' in file_path or '.PDF' in file_path:
        reader = PdfReader(file_path)
        raw_list = []
        for i, page in enumerate(reader.pages):
            text = page.extract_text()
            raw_list.append(text)
        texts = text_splitter.create_documents([i for i in raw_list],[{'source': file_name} for file_name in raw_list])
        return texts
    elif 'doc' in file_path or 'docx' in file_path:
        loader = UnstructuredWordDocumentLoader(file_path)
        texts = text_splitter.split_documents(loader.load())
        return texts


if __name__ == '__main__':
    print(split_document(r'C:\Users\<USER>\Desktop\瞿明明-Python-本科.pdf'))
