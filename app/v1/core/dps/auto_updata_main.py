#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
立信知识库全量同步 → MaxKB（自动分段 + 批量上传 + 删除旧数据）
作者: 严紫炜  |  Maintainer: ChatGPT
"""

import os
import csv
import json
import time
import datetime as dt
from pathlib import Path
from typing import List

import pymysql
import requests
from bs4 import BeautifulSoup
from pymongo import MongoClient

# ------------------ 配  置 ------------------
MONGO_URI = "mongodb://************:27017/"
MONGO_DB = "chatbot"
MONGO_COLLECTION = "dataset_application_map"

DB_CONFIG = dict(
    host="***********",
    port=5188,
    user="aireader",
    password="=9/)x%Y(",
    database="bdo_kb",
    charset="utf8mb4",
    cursorclass=pymysql.cursors.DictCursor,
)

USERNAME = "admin"
PASSWORD = "adminyzw@1"
FIXED_DATASET_ID = "6cb8692c-1ad9-11f0-bfd2-4eb070cf78f4"

BASE_URL = "http://************:8891"
LOGIN_URL = f"{BASE_URL}/api/user/login"
SPLIT_URL = f"{BASE_URL}/api/dataset/document/split"
BATCH_UPLOAD_URL = f"{BASE_URL}/api/dataset/{FIXED_DATASET_ID}/document/_bach"
LIST_URL = f"{BASE_URL}/api/dataset/{FIXED_DATASET_ID}/document/1/100"

DESKTOP = Path("/app/chatglm/maxkb_auto_updata")  # ← 修正
CSV_DIR = DESKTOP / "lx_kb_full"
CSV_DIR.mkdir(parents=True, exist_ok=True)


# ------------------ 工  具 ------------------
def get_primary_token() -> str:
    """
    登录并返回新的 JWT。若后端仍使用 Mongo 中缓存，可自行调整。
    """
    login = requests.post(LOGIN_URL,
                          json={"username": USERNAME, "password": PASSWORD},
                          timeout=20)
    login.raise_for_status()
    return login.json()["data"]


def clean_html(html: str) -> str:
    """最粗暴的 html → 纯文本（保留换行）"""
    soup = BeautifulSoup(html or "", "html.parser")
    # 把 <br>、<p> 当作换行
    for br in soup.find_all(["br", "p"]):
        br.append("\n")
    return "\n".join(
        line.strip() for line in soup.get_text().splitlines() if line.strip()
    )


# ------------------ DB 拉数 ------------------
def fetch_all_kb() -> List[dict]:
    sql = """
    SELECT a.title, a.content, a.catalogPath, ab1.optionName AS publishingDepartment,
           a.publishingDate, a.effectiveDate, a.expirationDate,
           ab2.optionName AS criteria, ab3.optionName AS textValidity,
           ab4.optionName AS textNature, ab5.optionName AS permission,
           a.documentNumber
    FROM kb_article a
    LEFT JOIN kb_attribute ab1 ON a.publishingDepartment = ab1.id
    LEFT JOIN kb_attribute ab2 ON a.criteria            = ab2.id
    LEFT JOIN kb_attribute ab3 ON a.textValidity        = ab3.id
    LEFT JOIN kb_attribute ab4 ON a.textNature          = ab4.id
    LEFT JOIN kb_attribute ab5 ON a.permission          = ab5.id
    """
    with pymysql.connect(**DB_CONFIG) as conn, conn.cursor() as cur:
        cur.execute(sql)
        return cur.fetchall()


# ------------------ 写 CSV（单文件） ----------
def write_csv_single(rows: List[dict]) -> Path:
    ts = dt.datetime.now().strftime("%Y%m%d_%H%M%S")
    path = CSV_DIR / f"立信会计审计知识库_{ts}.csv"

    fieldnames = ["标题", "发布部门", "发布日期", "生效日期", "失效日期",
                  "所属准则", "文本效力", "文本性质", "权限", "文号", "分类", "正文"]

    with path.open("w", newline="", encoding="utf-8-sig") as f:
        w = csv.DictWriter(f, fieldnames=fieldnames)
        w.writeheader()
        for r in rows:
            body = clean_html(r["content"])
            if not body:
                continue
            w.writerow({
                "标题": r.get("title", "未命名"),
                "发布部门": r.get("publishingDepartment", "无"),
                "发布日期": r.get("publishingDate", "无"),
                "生效日期": r.get("effectiveDate", "无"),
                "失效日期": r.get("expirationDate", "无"),
                "所属准则": r.get("criteria", "无"),
                "文本效力": r.get("textValidity", "无"),
                "文本性质": r.get("textNature", "无"),
                "权限": r.get("permission", "无"),
                "文号": r.get("documentNumber", "无"),
                "分类": r.get("catalogPath", "无"),
                "正文": body,
            })
    return path


# ------------------ 上传 (split → _bach) -----
def _wrap_for_bach(raw: list, filename: str) -> list:
    """
    把 split 返回的数据 wrap 成 _bach 需要的格式
    """
    docs = []
    for doc_i, item in enumerate(raw):
        contents = item.get("content", [])
        # 某些版本 split 直接返回 list[str]
        if isinstance(contents, list) and contents and isinstance(contents[0], str):
            contents = [{"content": c, "title": ""} for c in contents]

        paragraphs = [
            {
                "content": c.get("content", "").strip(),
                "index": idx,
                "title": c.get("title", "")
            }
            for idx, c in enumerate(contents) if c.get("content")
        ]
        if paragraphs:
            docs.append({
                "name": filename if len(raw) == 1 else f"{filename}#{doc_i}",
                "paragraphs": paragraphs,
                "meta": {}
            })
    return docs


def upload_csv(token: str, dataset_id: str, csv_path: Path) -> None:
    headers = {"Authorization": token}

    print(f"⬆️  上传文件（split）：{csv_path.name}")
    with csv_path.open("rb") as f:
        files = {"file": (csv_path.name, f, "text/csv")}
        split_r = requests.post(
            SPLIT_URL,
            headers=headers,
            files=files,
            data={"patterns": "", "limit": "0", "with_filter": "false"},
            timeout=60
        )
    raw = split_r.json().get("data") or split_r.json()
    docs = _wrap_for_bach(raw, csv_path.name)
    if not docs:
        raise RuntimeError("split 接口未解析出有效段落，上传终止")

    bach_url = f"{BASE_URL}/api/dataset/{dataset_id}/document/_bach"
    bach_r = requests.post(bach_url, headers=headers, json=docs, timeout=120)
    bach_r.raise_for_status()

    print(f"✅ {csv_path.name} 上传成功，共 {len(docs[0]['paragraphs'])} 段")
    # 成功后可按需删除本地文件
    # csv_path.unlink(missing_ok=True)


# ------------------ 删除旧文档 -----------------
def delete_old_documents(token: str,
                         dataset_id: str,
                         keep_names: List[str]) -> None:
    headers = {"Authorization": token}
    try:
        r = requests.get(LIST_URL, headers=headers, timeout=30)
        r.raise_for_status()
        j = r.json().get("data") or r.json()
        records = j.get("records", []) if isinstance(j, dict) else j
        if not isinstance(records, list):
            print("列表接口格式异常，放弃删除")
            return

        del_ids = [rec["id"] for rec in records
                   if rec.get("name") not in keep_names]

        if not del_ids:
            print("没有需要删除的旧文档")
            return

        d_url = f"{BASE_URL}/api/dataset/{dataset_id}/document/_bach"
        # **关键修改：包装成 {"id_list": [...]}**
        payload = {"id_list": del_ids}

        d = requests.delete(d_url, headers=headers, json=payload, timeout=30)
        d.raise_for_status()
        print(f"已清除 {len(del_ids)} 条旧文档")
    except Exception as e:
        print(f"删除文档失败：{e}")


# ------------------ 主流程 --------------------
def main() -> None:
    print("开始同步...")
    rows = fetch_all_kb()
    print(f"共获取 {len(rows)} 条知识库内容")

    csv_path = write_csv_single(rows)  # 1. 生成 CSV
    token = get_primary_token()  # 2. 登录
    upload_csv(token, FIXED_DATASET_ID, csv_path)  # 3. 上传

    print("等待 10 秒以便系统刷新")
    time.sleep(10)

    # 4. 删除旧文档，仅保留刚上传的这一个
    delete_old_documents(token,
                         FIXED_DATASET_ID,
                         keep_names=[csv_path.name])

    print("✅ 全部完成")


if __name__ == "__main__":
    main()
