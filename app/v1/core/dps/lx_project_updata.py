#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量同步 2021-2024 项目基础信息 → MaxKB
  · 每年 5 000 行 / part，文件名  项目基础信息_{year}_part{n}.csv
  · 若近 DELTA_DAYS 天有行修改 ⇒ 重传“受影响 part”（含未改行，保证完整）
  · 删除旧块时自动翻页，100% 命中
"""

from __future__ import annotations
import datetime as dt
import time  # ★ NEW
from pathlib import Path
from typing import Any, List, Dict, Set

import pandas as pd
import requests
from requests.exceptions import (
    ConnectionError, ReadTimeout  # ★ NEW
)
from urllib3.exceptions import ProtocolError  # ★ NEW
from sqlalchemy import create_engine, text

# ────────── 1. 参数区域 ──────────
DB_USER = "read"
DB_PASSWORD = "read123"
DB_HOST = "***********"
DB_PORT = 5188
DB_NAME = "asdb"
CHARSET = "utf8mb4"

BASE_URL = "http://************:8891"
USERNAME = "admin"
PASSWORD = "adminyzw@1"
DATASET_ID = "524f59d0-1f23-11f0-b4fa-86a404970cbc"

YEARS = (2023, 2024, 2025)  # 需要同步的年份
CHUNK_SIZE = 5_000  # 行/块
DELTA_DAYS = 1  # 近 N 天内修改才算增量
CSV_DIR = Path("/app/chatglm/maxkb_auto_updata/lx_kb_full")
CSV_DIR.mkdir(parents=True, exist_ok=True)
FILE_TPL = "项目基础信息_{year}_part{idx}.csv"

HTTP_TIMEOUT = 600
PAGE_SIZE = 1_000

# ★ NEW: 重试策略常量（只影响网络瞬断，不改变原有逻辑）
MAX_RETRY = 5  # 最多重试 5 次
BACKOFF = 2  # 指数回退因子：2,4,8,…
RETRY_ERRS = (ConnectionError, ReadTimeout, ProtocolError)
# ────────── 2. 连接 & 常量 ──────────
ENGINE = create_engine(
    f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    f"?charset={CHARSET}",
    pool_pre_ping=True
)

LOGIN_URL = f"{BASE_URL}/api/user/login"
TABLE_URL = f"{BASE_URL}/api/dataset/{DATASET_ID}/document/table"
LIST_TPL = f"{BASE_URL}/api/dataset/{DATASET_ID}/document?page={{page}}&size={PAGE_SIZE}"
DELETE_URL = f"{BASE_URL}/api/dataset/{DATASET_ID}/document/_bach"


# ────────── 3. HTTP / MaxKB 工具 ──────────
def get_token() -> str:
    r = requests.post(
        LOGIN_URL,
        json={"username": USERNAME, "password": PASSWORD},
        timeout=HTTP_TIMEOUT,
    )
    r.raise_for_status()
    return r.json()["data"]


def _build_urls(page: int) -> List[str]:
    """返回同一个页码的两种可能 URL。"""
    return [
        # ① query 参数写法
        f"{BASE_URL}/api/dataset/{DATASET_ID}/document?page={page}&size={PAGE_SIZE}",
        # ② RESTful 写法（UI 默认为 0-based，如果你的后端是 1-based 就把 page-1）
        f"{BASE_URL}/api/dataset/{DATASET_ID}/document/{page}/{PAGE_SIZE}?order_by=",
    ]


def _extract_records(j) -> List[Dict[str, Any]]:
    """兼容两种结构：直接 list 或 dict->data->records"""
    if isinstance(j, list):
        return j
    if isinstance(j, dict):
        # UI 风格：{"code":200,"data":{"records":[...]}}
        if "data" in j and isinstance(j["data"], dict):
            return j["data"].get("records", [])
        # 某些旧版本：{"records":[...]}
        return j.get("records", [])
    return []


def iter_dataset_files(token: str):
    page = 1  # 如遇 0-based 后端，可改成 0
    while True:
        recs = []
        for url in _build_urls(page):
            try:
                resp = requests.get(url,
                                    headers={"Authorization": token},
                                    timeout=HTTP_TIMEOUT)
                if resp.status_code == 200:
                    recs = _extract_records(resp.json())
                    if recs:  # 拿到数据即退出双 URL 尝试
                        break
            except requests.RequestException:
                pass  # 换下一种 URL

        if not recs:  # 两种 URL 都无数据 → 结束
            break

        print(f"[Page {page}] 取到 {len(recs)} 条")
        yield from recs

        if len(recs) < PAGE_SIZE:  # 最后一页
            break
        page += 1


def delete_block(token: str, fname: str) -> None:
    """删除数据集中与 fname 同名的文件，并打印匹配到的 name / id。"""
    matches = [
        {"id": r["id"], "name": r.get("name", "")}
        for r in iter_dataset_files(token)
        if r.get("name") == fname
    ]

    if not matches:
        print(f"[!] 没找到同名文件：{fname}（删除失败）")
        return

    # 打印匹配详情
    print(f"[√] 找到 {len(matches)} 条同名文件：")
    for i, m in enumerate(matches, 1):
        print(f"  {i:>2}. {m['name']}  ->  {m['id']}")

    # 继续执行删除
    ids = [m["id"] for m in matches]
    resp = requests.delete(
        DELETE_URL,
        headers={"Authorization": token},
        json={"id_list": ids},
        timeout=HTTP_TIMEOUT,
    )

    # 删除结果
    if resp.ok:
        print(f"[×] 已删除 {len(ids)} 条  ——  {fname}")
    else:
        print(f"[!] 删除请求失败：HTTP {resp.status_code}  {resp.text}")


def table_upload_ok(resp_json: Any) -> bool:
    """兼容不同版本返回，判断表格写入是否成功"""
    if isinstance(resp_json, list):
        return bool(resp_json)
    if not isinstance(resp_json, dict):
        return False
    data = resp_json.get("data") or resp_json
    if isinstance(data, dict) and str(data.get("total", "0")).isdigit():
        return int(data["total"]) > 0
    rows = data.get("rows") if isinstance(data, dict) else None
    if rows is None and isinstance(data, list):
        rows = data
    return bool(rows)


def upload_block(token: str, csv_path: Path) -> None:
    """
    上传一个块：
    1) 网络瞬断 ⇒ 指数回退重试 MAX_RETRY 次
    2) 解析失败 ⇒ 删除新块后抛异常（与原逻辑一致）
    """
    for attempt in range(1, MAX_RETRY + 1):
        try:
            with csv_path.open("rb") as f:
                resp = requests.post(
                    TABLE_URL,
                    headers={"Authorization": token},
                    files={"file": (csv_path.name, f, "text/csv")},
                    timeout=HTTP_TIMEOUT,
                )
            resp.raise_for_status()

            if not table_upload_ok(resp.json()):  # 解析失败
                delete_block(token, csv_path.name)
                raise RuntimeError("解析失败")

            print(f"[↑] {csv_path.name} 上传成功")
            return  # 成功即返回；不进入后续重试

        except RETRY_ERRS as e:
            # —— 可恢复的网络异常：指数回退重试 ——
            if attempt == MAX_RETRY:
                print(f"[×] {csv_path.name} 网络异常仍失败({e})")
                raise  # 最后一次仍失败 → 抛给调用方
            sleep = BACKOFF ** attempt
            print(f"[!] 网络异常({e}) 第 {attempt}/{MAX_RETRY} 次重试，{sleep}s 后重试…")
            time.sleep(sleep)

        except Exception:
            # 解析失败或其他错误：保持原有回滚 + 抛异常行为
            raise


# ────────── 4. SQL / DataFrame 工具 ──────────
SQL_TMPL = """
SELECT 
    CONCAT('审计事业部','-',IFNULL(f.firstItemName,''),'-',IFNULL(f.hrDeptName,'')) AS 所属部门,
    a.projectId        AS 项目编号,
    a.projectYear      AS 项目年份,
    a.projectName      AS 项目名称,
    a.auditpara        AS 业务类型,
    g.name             AS 项目负责人,
    g1.name            AS 签字合伙人,
    g2.name            AS 签字会计师2,
    c.customerName     AS 客户名称,
    c.firstItemName    AS 集团名称,
    CONCAT(a.AuditTimeBegin,'~',a.AuditTimeEnd) AS 审计期间,
    d.reportNumber     AS 报告文号,
    a.LAST_UPDATE_DATE
FROM asdb.z_project a
LEFT JOIN asdb.z_projectpeople   b ON a.projectId=b.projectId
LEFT JOIN bdo_cus.cus_customerinfo c ON a.customerId=c.customerId
LEFT JOIN asdb.z_reportlinknum   d ON a.projectId=d.projectId
LEFT JOIN asdb.z_projectext      e ON a.projectId=e.projectId
LEFT JOIN bdo_base.base_department f ON a.departmentid=f.autoid
LEFT JOIN bdo_base.base_user g     ON g.id  = b.managerId
LEFT JOIN bdo_base.base_user g1    ON g1.id = b.signUser
LEFT JOIN bdo_base.base_user g2    ON g2.id = b.signUser2
WHERE a.projectYear = :yr
ORDER BY a.projectId      -- ★保证与首轮分块顺序一致
"""


def load_year_df(year: int) -> pd.DataFrame:
    return pd.read_sql_query(text(SQL_TMPL), ENGINE, params={"yr": year})


def is_header_row(row: pd.Series) -> bool:
    """过滤意外的“列头行”"""
    return all(str(row[c]).strip() == c for c in row.index)


def find_changed_parts(df: pd.DataFrame, since_ts: str) -> Set[int]:
    mask = df["LAST_UPDATE_DATE"] >= since_ts
    if not mask.any():
        return set()
    return {idx // CHUNK_SIZE + 1 for idx in df[mask].index}


# ────────── 5. 主流程 ──────────
def main() -> None:
    token = get_token()
    since_ts = (dt.datetime.now() - dt.timedelta(days=DELTA_DAYS)) \
        .strftime("%Y-%m-%d %H:%M:%S")
    total_rows = 0

    for year in YEARS:
        df = load_year_df(year)
        if df.empty:
            continue
        df.reset_index(drop=True, inplace=True)  # 行号 0..n-1
        parts_to_update = find_changed_parts(df, since_ts)
        if not parts_to_update:
            print(f"[{year}] 无变更")
            continue

        print(f"[{year}] 需重传块：{sorted(parts_to_update)}")

        for idx, start in enumerate(range(0, len(df), CHUNK_SIZE), start=1):
            if idx not in parts_to_update:
                continue
            chunk = df.iloc[start:start + CHUNK_SIZE] \
                .loc[lambda x: ~x.apply(is_header_row, axis=1)]
            if chunk.empty:
                continue

            fname = FILE_TPL.format(year=year, idx=idx)
            fpath = CSV_DIR / fname
            chunk.to_csv(fpath, index=False, encoding="utf-8-sig")

            delete_block(token, fname)  # 先删旧块（自动翻页保证能删到）
            upload_block(token, fpath)  # 再传新块
            total_rows += len(chunk)

            # 如需上传后删除本地文件 → fpath.unlink(missing_ok=True)

    if total_rows == 0:
        print("本次无任何增量块需要同步。")
    else:
        print(f"增量块同步完成，共更新 {total_rows} 行。")


# ────────── 6. 入口 ──────────
if __name__ == "__main__":
    main()
