# net_server_async_optimized.py
# -*- coding: utf-8 -*-
"""
异步 NetServer
  • SearxNG + FireCrawl（默认）/ Bocha
  • FireCrawl：超长裁剪、空字段剔除
  • 自动检测 HTTP/2
  • Semaphore 控制并发
"""

import asyncio
import json
import logging
import random
import urllib.parse
from typing import Any, Dict, List

import httpx

# ---------- 日志 ----------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# ---------- 常量 ----------
TOTAL_TIMEOUT = 30.0           # HTTP 请求超时
CRAWL_TEXT_LIMIT = 5_000       # FireCrawl 文本最大长度
MAX_CONCURRENT_CRAWLS = 5      # 并发爬虫上限

# ---------- 工具 ----------
def _safe_json(text: str) -> Dict[str, Any]:
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        logger.error("JSON decode error: %s…", text[:120])
        return {}

def _trim_text(obj: Any) -> Any:
    """递归裁剪超长字符串并过滤空白"""
    if isinstance(obj, str):
        obj = obj.strip()
        if not obj:
            return None
        return obj[:CRAWL_TEXT_LIMIT] + ("…" if len(obj) > CRAWL_TEXT_LIMIT else "")
    if isinstance(obj, list):
        return [_trim_text(i) for i in obj if _trim_text(i) is not None]
    if isinstance(obj, dict):
        return {k: _trim_text(v) for k, v in obj.items() if _trim_text(v) is not None}
    return obj

def resolve_final_url(url: str) -> str:
    """处理百度/QQ 等跳转链接，粗暴提取真正目标"""
    try:
        up = urllib.parse.urlparse(url)
        if up.netloc.endswith("baidu.com") and "url=" in up.query:
            qs = urllib.parse.parse_qs(up.query)
            real = qs.get("url", [""])[0]
            return urllib.parse.unquote(real) or url
        return url
    except Exception:
        return url

# ---------- 主类 ----------
class NetServer:
    def __init__(self) -> None:
        # API 配置
        self.bocha_api_url = "https://api.bochaai.com/v1/web-search"
        self.bocha_api_key = "sk-5fd5cdcf800b428885d20a973fda892d"
        self.searxng_api_url = "http://*************:8096/search_and_crawl"
        self.CRAWL_URL = "http://***********:3002/v0/crawl"
        self.GET_RESULT_BASE_URL = "http://***********:3002/v1/crawl/"
        self.TOKEN = "<your_token>"

        # AsyncClient: 自动检测 h2
        try:
            import h2  # noqa
            use_http2 = True
        except ImportError:
            use_http2 = False
            logger.warning(
                "未安装 h2，已回退 HTTP/1.1；pip install 'httpx[http2]' 可开启 HTTP/2。"
            )

        self._client = httpx.AsyncClient(
            headers={"Content-Type": "application/json"},
            timeout=httpx.Timeout(TOTAL_TIMEOUT),
            http2=use_http2,
        )
        self._crawl_sem = asyncio.Semaphore(MAX_CONCURRENT_CRAWLS)
        logger.info("NetServer 初始化完成 (HTTP/2=%s)", use_http2)

    # ======= 对外入口 =======
    async def search(
        self, query: str, engine: str = "searxng", results: int = 5
    ) -> str:
        engine = engine.lower()
        if engine == "bocha":
            return await self._search_bocha(query, results)
        return await self._search_searxng(query, results)

    # ======= Bocha =======
    async def _search_bocha(self, query: str, results: int) -> str:
        body = {
            "query": query,
            "freshness": "noLimit",
            "summary": True,
            "count": results,
            "page": 1,
        }
        headers = {"Authorization": f"Bearer {self.bocha_api_key}"}
        try:
            resp = await self._client.post(self.bocha_api_url, json=body, headers=headers)
            resp.raise_for_status()
            pages = resp.json().get("data", {}).get("webPages", {}).get("value", [])
            if not pages:
                return f"No results found for: {query}"
            out = ["Bocha 搜索结果：\n"]
            for i, p in enumerate(pages[:results], 1):
                out.append(
                    f"{i}. {p.get('name','无标题')}\n"
                    f"   {p.get('summary','')}\n"
                    f"   URL: {p.get('url','')}\n"
                )
            return "\n".join(out)
        except httpx.HTTPError as e:
            return f"[Error] 调用 Bocha 失败: {e}"

    # ======= SearxNG + FireCrawl =======
    async def _search_searxng(self, query: str, results: int) -> str:
        payload = {"query": query, "num": results, "count": results, "limit": results}
        try:
            resp = await self._client.post(self.searxng_api_url, json=payload)
            resp.raise_for_status()
        except httpx.HTTPError as e:
            return f"[Error] 调用 SearxNG 失败: {e}"

        raw_json = _safe_json(resp.text)
        res_arr = (
            raw_json.get("results")
            or raw_json.get("searchResult", {}).get("results")
            or []
        )
        if not res_arr:
            # 连 URL 都没有，直接返回
            return json.dumps(
                {
                    "searchMeta": {"query": query, "engine": "searxng"},
                    "error": "No search results",
                },
                ensure_ascii=False,
                indent=2,
            )

        picks = random.sample(res_arr, min(results, len(res_arr)))
        tasks = [
            self._crawl_wrapper(resolve_final_url(i.get("url")))
            for i in picks
            if i.get("url")
        ]
        crawls = await asyncio.gather(*tasks)

        # -------- 成功 / 失败 统计 --------
        def _is_ok(c: Dict[str, Any]) -> bool:
            r = c.get("crawl_result", {})
            return (
                r.get("success") is True
                and r.get("status") == "completed"
                and r.get("data")
            )

        ok = sum(1 for c in crawls if _is_ok(c))
        failed = len(crawls) - ok
        status = (
            "ok"
            if ok and failed == 0
            else "partial"
            if ok and failed
            else "all_failed"
        )

        combined = {
            "searchMeta": {"query": query, "engine": "searxng"},
            "searchResult": raw_json,          # ← 把原始搜索 JSON 带回来
            "randomCrawls": crawls,
            "crawlSummary": {"ok": ok, "check": failed, "status": status},
        }

        return json.dumps(combined, ensure_ascii=False, indent=2)

    # ---------- 爬虫包装 ----------
    async def _crawl_wrapper(self, url: str) -> Dict[str, Any]:
        async with self._crawl_sem:
            logger.info("[FireCrawl] 准备启动爬虫任务, url=%s", url)
            result = await self._run_crawl(url)
        data = _trim_text(_safe_json(result) if isinstance(result, str) else result)
        return {"url": url, "crawl_result": data}

    async def _run_crawl(self, url: str) -> str:
        try:
            start = await self._client.post(self.CRAWL_URL, json={"url": url})
            start.raise_for_status()
            job_id = start.json().get("jobId")
            if not job_id:
                return "[FireCrawl] jobId 获取失败"
        except httpx.HTTPError as e:
            return f"[FireCrawl] 启动失败: {e}"
        return await self._poll_crawl(job_id)

    async def _poll_crawl(
        self, job_id: str, retries: int = 4, interval: int = 3
    ) -> str:
        url = f"{self.GET_RESULT_BASE_URL}{job_id}"
        headers = {"Authorization": f"Bearer {self.TOKEN}"}
        for attempt in range(1, retries + 1):
            try:
                resp = await self._client.get(url, headers=headers)
                resp.raise_for_status()
                data = resp.json()
                if data.get("status") == "completed":
                    return json.dumps(data, ensure_ascii=False)
                logger.info(
                    "[FireCrawl] 第 %d 次轮询 jobId=%s: %s",
                    attempt,
                    job_id,
                    data.get("status"),
                )
            except httpx.HTTPError as e:
                return f"[FireCrawl] 查询失败: {e}"
            await asyncio.sleep(interval)
        return "[FireCrawl] 超时未完成"

    # ======= 关闭 =======
    async def aclose(self):
        await self._client.aclose()

# ---------- Demo ----------
if __name__ == "__main__":

    async def _demo():
        srv = NetServer()
        try:
            res = await srv.search("关税战最新消息", results=8)
            print(res)
        finally:
            await srv.aclose()

    asyncio.run(_demo())
