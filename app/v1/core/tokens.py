# -*- encoding: utf-8 -*-
"""
@File    : tokens.py
@Time    : 2023/4/11 11:09
<AUTHOR> Qu ming
"""
import hashlib
import json
import os
from typing import Dict
import uuid
from urllib import parse
import sys  # 临时

from app.utils.monitor import send_monitor_event

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))  # 临时
import jwt
from datetime import datetime, timedelta

from requests_toolbelt import MultipartEncoder
import requests

from app.v1.core.semantic_recognition import judge_question
from app.v1.core.utils import create_token
from app.v1.settings import Config


def encode_token(user: str):
    """
    根据用户生成token
    """
    payload = {
        'exp': datetime.now() + timedelta(minutes=24 * 60 * 7),
        'username': user
    }
    token = jwt.encode(payload=payload, key="123", algorithm='HS256')
    return token


def decode_token(token):
    """
    根据用户生成token校验用户是否匹配
    """
    try:
        res = jwt.decode(token, key='123', algorithms='HS256')
    except Exception as e:
        print(e)
        return False
    if res.get('username', None):
        return res.get('username').lower()
    return False


def get_user_token():
    """
    返根据ID和SECRET回 access_token
    """
    access_token = None
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={Config.wechat_corpid}&corpsecret={Config.wechat_secret}"
    response = requests.get(url).json()
    if response.get('access_token', None):
        access_token = response.get('access_token')
    return access_token


def get_user_id(access_token: str, code: str):
    """
    根据access_token和code获取user_id
    """
    user_id = None
    if not access_token:
        return
    url = f"https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token={access_token}&code={code}"
    response = requests.get(url).json()
    if response.get('userid', None):
        user_id = response.get('userid')
    return user_id


def get_user_id_v2(code: str):
    """
    根据access_token和code获取user_id
    """
    user_id = None
    url = f"http://172.17.5.137:9998/work/getUserIdByCode?appId=AI&code={code}"
    response = requests.get(url).json()
    if response.get('data', None):
        user_id = response.get('data')
    return user_id


def get_private(user: str):
    """
    获取私人信息
    """
    # 获取token
    try:
        url = f"http://172.17.5.46:9999/base/getUserInfo?loginId={user}"
        data = {
            'key': Config.private_info_key,
            'secret': Config.private_info_secret
        }
        response = requests.post(Config.private_info_url, data).json()
        headers = {
            'content-type': 'application/json',
            'Authorization': response['data']['token'],
        }
        res = requests.get(url=url, headers=headers).json()
    except Exception as e:
        print(e)
        res = '无法查询到数据'
    return res


def login_maxkb(kind: bool = True, username:str= None):
    """
    登录maxkb
    """
    if kind:
        kind_url = Config.maxkb_private_library_url
    else:
        kind_url = Config.maxkb_project_library_url

    login_url = f"{kind_url}/api/user/login"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Content-Type": "application/json"
    }
    login_payload = {
        "username": "admin",
        "password": "adminyzw@1"
    }

    try:
        resp_login = requests.post(login_url, headers=headers, json=login_payload, timeout=5).json()
        if resp_login.get('code') == 200:
            return resp_login["data"]
    except Exception as e:
        send_monitor_event(
            title="📡 知识库内部调用异常",
            content=f"❌ 错误：{str(e)}\n📥 问题：登录maxkb失败 \n📌 登录链接：{login_url} \n 访问用户: {username}",
            tags=["maxkb", "exception"],
        )


def get_project_id(desc: str, name: str, type: str = '0',
                   embedding_mode_id: str = '663eba1e-21a6-11f0-8221-b224e39ee5f7', kind: bool = True,username:str= None):
    """
    获取知识库id
    """
    if kind:
        kind_url = Config.maxkb_private_library_url
        unique_name = f"{name}-{uuid.uuid4().hex[:8]}"
    else:
        kind_url = Config.maxkb_project_library_url
        unique_name = name
    token = login_maxkb(kind,username)
    if token:
        try:
            url = f"{kind_url}/api/dataset"
            headers = {
                "accept": "application/json, text/plain, */*",
                "authorization": token
            }
            data = {
                'desc': desc,
                'name': unique_name,
                'type': type,
                'embedding_mode_id': embedding_mode_id
            }
            result = requests.post(url, headers=headers, data=data, timeout=5).json()
            return result['data']['id']
        except Exception as e:
            send_monitor_event(
                title="📡 知识库内部调用异常",
                content=f"❌ 错误：{str(e)}\n📥 问题：创建知识库id失败 \n 访问用户: {username}",
                tags=["maxkb", "exception"],
            )


def generate_token(loginId):
    secretKey = 'Xam8EpCvBHv/tplh0YHHVqH+twNPaBf701MzpGJCpoaDAMQNWjIgdzlMx5I0+Vj4URHHpMHKqSCHa0ECYA6gHg=='
    current_date = datetime.now().strftime("%Y-%m-%d")
    data = loginId + current_date + secretKey
    token = hashlib.md5(data.encode()).hexdigest()
    return token


def project_permission(user: str, tenantId: str = 'E8378C2E8376BF0E89D5B58829ED6FC6'):
    """
    获取项目权限负责人，调用java接口
    """

    headers = {
        'content-type': 'application/x-www-form-urlencoded',
    }
    data = {
        "tenantId": tenantId,
        "token": generate_token(user),
        "userLoginId": user,
        "interfaceId": 309,
        "param": json.dumps({'param1': user})
    }

    try:
        res = requests.post(url=Config.sacp_project_url, headers=headers, data=data).json()
        return res['data']
    except Exception as e:
        send_monitor_event(
            title="📡 sacp项目管理系统调用异常",
            content=f"❌ 错误：{str(e)}\n📥 问题：项目负责人接口调用失败 \n📌  访问用户: {user}",
            tags=["sacp", "exception"],
        )
        return "无法获取项目权限"



def download_file(user: str, project_name: str, fileId: str, file_name: str, file_dir: str, file_type: str,
                  tenantId: str = 'E8378C2E8376BF0E89D5B58829ED6FC6'):
    """
    下载文件，java接口
    """
    token = generate_token(user)
    projectId = project_name.split('-')[0]
    data = {
        "tenantId": tenantId,
        "token": token,
        "userLoginId": user,
        "interfaceId": "314",
        "param": json.dumps({
            "param1": projectId,
            "param2": file_type,
            "param3": fileId
        })
    }

    response = requests.post(Config.sacp_project_url, data=data, stream=True, timeout=5)
    if response.status_code != 200:
        return {"error": "文件下载失败", "detail": response.text}
    file_path = os.path.join(file_dir, file_name)
    with open(file_path, 'wb') as f:
        for chunk in response.iter_content(chunk_size=8192):
            f.write(chunk)


def get_sacp_file(username: str, projectId: str):
    """
    获取sacp文件
    """
    token = generate_token(username)
    data = {
        "tenantId": 'E8378C2E8376BF0E89D5B58829ED6FC6',
        "token": token,
        "userLoginId": username,
        "interfaceId": "313",
        "param": json.dumps({"param1": projectId})
    }
    try:
        response = requests.post(Config.sacp_project_url, data=data).json()
        return response['data']
    except Exception as e:
        send_monitor_event(
            title="📡 sacp项目管理系统调用异常",
            content=f"❌ 错误：{str(e)}\n📥 问题：获取sacp文件失败 \n📌 访问用户: {username}",
            tags=["sacp", "exception"],
        )
        return []


def query_law_titles(
        apikey: str = Config.law_api_key,
        pageNum: str = "1",
        pageSize: str = "10",
        keywords: str = None,
        searchType: str = None,
        username:str = None
) -> list[dict]:
    """
    调用“查询法规标题”开放平台接口，返回标题列表（items）。
    """
    url = Config.law_api_query_law_title_url
    headers = {"Content-Type": "application/json"}
    params = {"apikey": apikey}
    body = {"pageNum": pageNum, "pageSize": pageSize}
    if keywords:
        body["keywords"] = keywords
    if searchType:
        body["searchType"] = searchType

    try:
        res = requests.post(
            url, headers=headers, params=params,
            data=json.dumps(body), timeout=10
        )
        res.raise_for_status()
        return res.json().get("result", {})
    except Exception as e:
        send_monitor_event(
            title="📡 法律法规库调用异常",
            content=f"❌ 错误：{str(e)}\n📥 问题：获取法律法规标题失败 \n📌 访问用户: {username}",
            tags=["law", "exception"],
        )
        return []


# ------------------ 2. 查询法规正文 ------------------
def query_law_content(
        mainId: str,
        username:str,
        apikey: str = Config.law_api_key
) -> dict:
    """
    调用“查询法规正文”开放平台接口，返回完整的 result 对象。
    """
    url = Config.law_api_query_law_content_url
    params = {"apikey": apikey, "mainId": mainId}
    try:
        res = requests.get(url, params=params, timeout=10).json()
        return res.get("result", {})
    except Exception as e:
        send_monitor_event(
            title="📡 法律法规库调用异常",
            content=f"❌ 错误：{str(e)}\n📥 问题：获取法律法规内容失败 \n📌 访问用户: {username}",
            tags=["law", "exception"],
        )
        return {}


# ------------------ 3. 下载为 CSV ------------------
def download_single_law_csv(
        mainId: str,
        file_path: str,
        username: str
):
    """
    查询某条法规正文并写入 CSV，返回生成的文件路径。

    CSV 列：
      version, paragraphId, content
    """
    # 1) 调用正文接口
    try:
        apikey = Config.law_api_key
        result: Dict = query_law_content(mainId, username, apikey)
        texts = result.get('items').get("texts", [])
        with open(file_path, "a+", encoding="utf-8-sig") as f:
            for sec in texts['textSubsectionDtoList']:
                content = sec.get("content", "").replace('&nbsp', '') + '\n'
                f.write(content)
        return file_path
    except:
        pass


def delete_dataset(dataset_id: str, kind: bool = True, username:str= None) -> dict:
    """
    登录后，使用获取的 token 调用 DELETE 接口，删除指定数据集。
    :param dataset_id: 待删除的数据集 ID
    :return: 操作结果，包含响应或异常信息
    """
    token = login_maxkb(kind,username)
    if kind:
        kind_url = Config.maxkb_private_library_url
    else:
        kind_url = Config.maxkb_project_library_url
    delete_url = f"{kind_url}/api/dataset/{dataset_id}"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Authorization": token,
    }
    try:
        resp_delete = requests.delete(delete_url, headers=headers, timeout=5)
    except Exception as e:
        send_monitor_event(
            title="📡 知识库内部调用异常",
            content=f"❌ 错误：{str(e)}\n📥 问题：删除知识库失败 \n📌 数据集：{dataset_id} \n 访问用户: {username}",
            tags=["maxkb", "exception"],
        )
        return {"error": f"DELETE 请求异常: {str(e)}"}

    # --- 3. 返回删除结果 ---
    result = {
        "status_code": resp_delete.status_code,
        "response_text": resp_delete.text
    }
    return result


def get_sacp_list(user: str, project_id: str, tenantId: str = 'E8378C2E8376BF0E89D5B58829ED6FC6'):
    headers = {
        'content-type': 'application/x-www-form-urlencoded',
    }
    data = {
        "tenantId": tenantId,
        "token": generate_token(user),
        "userLoginId": user,
        "interfaceId": 315,
        "param": json.dumps({'param1': project_id.split('-')[0]})
    }

    try:
        res = requests.post(url=Config.sacp_project_url, headers=headers, data=data).json()
        return res['data']
    except Exception:
        pass


def get_financial_analysis(question: str, analysis_type: str, username:str):
    """
    调用java接口，得到财务数据
    """
    data = judge_question(question)
    url = f'https://cwr.bdo.com.cn/cwr/newEdition/report/{analysis_type}'
    try:
        res = requests.get(url=url, params=data).json()
        return res['data']
    except Exception as e:
        send_monitor_event(
            title="📡 财务分析调用异常",
            content=f"❌ 错误：{str(e)}\n📥 问题：获取财务分析失败 \n📌 访问用户: {username}",
            tags=["financial", "exception"],
        )
        pass


def ragflow_api(username: str, content: bytes, file_name: str, project_id, kind: bool = True):
    """
    调用ragflow的api
    """
    if kind:
        kind_url = Config.maxkb_private_library_url
    else:
        kind_url = Config.maxkb_project_library_url
    try:
        encoded_filename = parse.quote(file_name)
        url = f'{Config.document_parsing_url}/api/v1/documents/upload'
        file_id = create_blank_documents(username, project_id, file_name, kind_url=kind_url)[0]['id']
        data = MultipartEncoder(
            fields={
                'files': (
                    file_name,
                    content,
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    {
                        'Content-Disposition': f'form-data; name="file"; filename="{file_name}"; filename*=UTF-8\'\'{encoded_filename}'
                    }
                ),
                'maxkb_id': project_id,
                'document_ids': file_id,
                "maxkb_url": kind_url
            },
        )
        headers = {'Content-Type': data.content_type,
                   'Authorization': f'Bearer {create_token(username)}'}
        if requests.post(url, data=data, headers=headers).json()['batch_id']:
            return file_id
    except Exception as e:
        send_monitor_event(
            title="📡 ragflow调用异常",
            content=f"❌ 错误：{str(e)}\n📥 问题：ragflow调用异常 \n 访问用户: {username}",
            tags=["ragflow", "exception"],
        )


def create_blank_documents(username: str, project_id: str, file_name: str, kind_url: str):
    """
    创建新的空白文档
    """
    url = f'{Config.document_parsing_url}/api/v1/documents/blank_documents'
    data = {
        "knowledge_base_id": project_id,
        'names': [file_name],
        "maxkb_url": kind_url
    }
    headers = {'Content-Type': 'application/json',
               'Authorization': f'Bearer {create_token(username)}'}
    return requests.post(url, data=json.dumps(data), headers=headers).json()['data']


def judge_is_login(login_id: str, systemName: str, token: str):
    """
    判断云办公是登录
    """
    current_date = datetime.now().strftime("%Y%m%d")
    data = login_id + current_date + systemName
    print(hashlib.md5(data.encode()).hexdigest())
    if hashlib.md5(data.encode()).hexdigest() == token:
        return True


if __name__ == '__main__':
    # print(get_sacp_file('jane.liu','2023104314-胡子健测试sacp对接apt2022仅内部控制审计'))
    # print(get_financial_analysis('2023贵州茅台', 'analyseRisk'))
    # print(get_sacp_list('hu.zijian','2023104314-胡子健测试sacp对接apt2022仅内部控制审计'))
    # print(judge_is_login('fan.guojing','BdoXXR','3cdfab68c3ff5c6e4b04f34819c34912'))
    # print(get_sacp_list('hu.zijian','2021062774'))
    print(encode_token('hu.zijian'))
    print(judge_is_login('hu.zijian', 'BdoXXR', '3cdfab68c3ff5c6e4b04f34819c34912'))
