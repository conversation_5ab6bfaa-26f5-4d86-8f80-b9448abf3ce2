#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频+转写 → 关键词 / 摘要 / 会议纪要
FastAPI POST /process_speech
启动：  uvicorn main:app --host 0.0.0.0 --port 8195
"""
import io
import re
import json
import datetime
import traceback
from typing import Dict, Union

import pymysql
from pydub import AudioSegment
from app.v1.core.qwq_api import qwq_chat_stream
from app.v1.settings import Config


# ============ 数据库配置 ============


# ============ 工具函数 ============
def remove_think_blocks(text: str) -> str:
    """去掉 <think>…</think> 片段"""
    while "<think>" in text and "</think>" in text:
        start = text.index("<think>")
        end = text.index("</think>") + len("</think>")
        text = text[:start] + text[end:]
    return text


def call_my_ai_agent(context: str, query: str) -> str:
    """
    用 QwQ 的流式接口代替 DeepSeek-R1，把所有 chunk 按顺序拼成一条字符串并去除 <think> …
    """
    # 构造消息
    user_messages = [
        {"role": "system",
         "content": f"请使用简体中文回答用户问题，且不要输出任何 <think> 标签。\n以下是转写内容：\n{context}"},
        {"role": "user", "content": query}
    ]

    full_chunks: list[str] = []
    for chunk in qwq_chat_stream(user_messages, backend="online"):
        # 如果是 bytes，就 decode，一般是 str 就直接用
        if isinstance(chunk, (bytes, bytearray)):
            text = chunk.decode("utf-8", errors="ignore")
        else:
            text = chunk
        full_chunks.append(text)

    raw = "".join(full_chunks)
    return remove_think_blocks(raw)


# ============ 数据库操作 ============
def save_to_db(audio_filename: str, created_at: datetime.datetime, data: Dict) -> None:
    try:
        conn = pymysql.connect(
            host=Config.mysql_host, port=Config.mysql_port, user=Config.mysql_user,
            password=Config.mysql_password, db=Config.mysql_database
        )
        cursor = conn.cursor()

        cursor.execute(
            "SELECT 1 FROM xxr_speech_data WHERE audio_filename=%s AND dialog_length=%s",
            (audio_filename, data["dialog_length"])
        )
        if not cursor.fetchone():  # 不存在才插入
            cursor.execute(
                """
                INSERT INTO xxr_speech_data
                (audio_filename, created_at, num_speakers, dialog_data,
                 dialog_time, dialog_length, keywords, summary, meeting_points)
                VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s)
                """,
                (
                    audio_filename, created_at, data["num_speakers"],
                    json.dumps(data["dialog_data"], ensure_ascii=False),
                    data["dialog_time"], data["dialog_length"],
                    json.dumps(data["keywords"], ensure_ascii=False),
                    data["summary"], data["meeting_points"]
                )
            )
            conn.commit()
    except Exception as e:
        print("Error in save_to_db:", e)
        traceback.print_exc()
    finally:
        cursor.close()
        conn.close()


def update_to_db(audio_filename: str, data: Dict) -> bool:
    try:
        with pymysql.connect(
                host=Config.mysql_host, port=Config.mysql_port, user=Config.mysql_user,
                password=Config.mysql_password, db=Config.mysql_database
        ) as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                        UPDATE xxr_speech_data
                        SET dialog_data = %s,
                        summary = %s,
                        meeting_points = %s
                        WHERE audio_filename = %s AND dialog_length = %s
                        """,
                    (json.dumps(data["dialog_data"], ensure_ascii=False),
                     data["summary"], data["meeting_points"], audio_filename, data['dialog_length']
                     )
                )
                conn.commit()
                return True
    except Exception as e:
        print("Error in save_to_db:", e)
        traceback.print_exc()


def get_role_ids(data):
    try:
        with pymysql.connect(
                host=Config.mysql_host, port=Config.mysql_port, user=Config.mysql_user,
                password=Config.mysql_password, db=Config.mysql_database
        ) as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                   f"""
                    SELECT * FROM `bdo_xxr2`.`system_role_depts` WHERE `dept_id` = {data}
                    """
                )
                result = cursor.fetchall()
                conn.commit()
                return [r[0] for r in result]
    except Exception as e:
        print(e)
        traceback.print_exc()
        return []

def get_assistant(assistant_id: str):
    if assistant_id:
        try:
            with pymysql.connect(
                    host=Config.mysql_host, port=Config.mysql_port, user=Config.mysql_user,
                    password=Config.mysql_password, db=Config.mysql_database
            ) as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        f"""
                          SELECT prompt_content FROM `bdo_xxr2`.`assistant` WHERE `id` = {assistant_id}
                            """)
                    result = cursor.fetchone()
                    conn.commit()
                    return result  # 返回包含所有字段的元组
        except Exception as e:
            print(e)
            traceback.print_exc()


def get_from_db(audio_filename: str, dialog_length: str) -> Union[Dict, None]:
    try:
        conn = pymysql.connect(
            host=Config.mysql_host, port=Config.mysql_port, user=Config.mysql_user,
            password=Config.mysql_password, db=Config.mysql_database
        )
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT num_speakers, dialog_data, dialog_time, dialog_length,
                   keywords, summary, meeting_points
            FROM xxr_speech_data
            WHERE audio_filename=%s AND dialog_length=%s
            """,
            (audio_filename, dialog_length)
        )
        row = cursor.fetchone()
        if row:
            return {
                "num_speakers": row[0],
                "dialog_data": json.loads(row[1]),
                "dialog_time": row[2],
                "dialog_length": row[3],
                "keywords": json.loads(row[4]),
                "summary": row[5],
                "meeting_points": row[6],
            }
    except Exception as e:
        print("Error in get_from_db:", e)
        traceback.print_exc()
    finally:
        cursor.close()
        conn.close()


# ============ 核心处理 ============
def process_speech_data(
        transcript_text: str,
        audio_bytes: bytes,
        audio_format: str,
        audio_filename: str,
        created_at: datetime.datetime,
        file_path: str
) -> Dict:
    # ---- 1. 解析逐行对话 ----
    conversation, current_speaker = [], None
    for raw in transcript_text.splitlines():
        line = raw.strip()
        if not line:
            continue
        if line.startswith("说话人"):
            speaker, content = line.split("：", 1)
            current_speaker = speaker
            conversation.append({"speaker": speaker, "content": content.strip()})
        elif current_speaker:
            conversation.append({"speaker": current_speaker, "content": line})

    speakers = {c["speaker"] for c in conversation}
    if len(speakers) == 1:  # 只有一个说话人 → 合并
        merged = " ".join(c["content"] for c in conversation)
        conversation = [{"speaker": current_speaker, "content": merged}]

    # ---- 2. 对话时间 & 时长 ----
    dialog_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    duration_ms = len(AudioSegment.from_file(file_path, format=audio_format))
    m, s = divmod(duration_ms // 1000, 60)
    dialog_length = f"{m}分{s}秒"

    # ---- 3. 交给 AI ----
    ai_context = "\n".join(f"{c['speaker']}：{c['content']}" for c in conversation)

    # 3.1 关键词
    kw_raw = call_my_ai_agent(ai_context, "请提取关键词，用逗号分隔输出，不要带其它说明。")
    kw_split = re.split(r"[，,;；\s]+", kw_raw)
    keywords = [
        k for k in (x.strip() for x in kw_split)
        if k and k.lower() in transcript_text.lower()
    ]

    # 3.2 摘要
    summary = call_my_ai_agent(
        ai_context, "请用不超过150字的中文简要总结上述对话。"
    )

    # 3.3 会议纪要
    meeting_points = call_my_ai_agent(
        ai_context, "请将上述对话整理为分点形式的会议纪要，每点简要说明要点。"
    )

    result = {
        "num_speakers": len(speakers),
        "dialog_data": conversation,
        "dialog_time": dialog_time,
        "dialog_length": dialog_length,
        "keywords": keywords,
        "summary": summary,
        "meeting_points": meeting_points,
    }

    save_to_db(audio_filename, created_at, result)
    return result


# ===========新增重新生成按钮==========================
def _update_fields(audio_filename: str, dialog_length: str, **fields) -> None:
    """一次性把任意字段写回数据库"""
    if not fields:
        return
    set_clause = ", ".join(f"{k}=%s" for k in fields.keys())
    values = list(fields.values()) + [audio_filename, dialog_length]
    sql = f"UPDATE xxr_speech_data SET {set_clause} WHERE audio_filename=%s AND dialog_length=%s"
    conn = cursor = None
    try:
        conn = pymysql.connect(
            host=Config.mysql_host, port=Config.mysql_port, user=Config.mysql_user,
            password=Config.mysql_password, db=Config.mysql_database
        )
        cursor = conn.cursor()
        cursor.execute(sql, values)
        conn.commit()
    finally:
        cursor and cursor.close()
        conn and conn.close()


def regenerate_summary(audio_filename: str, dialog_length: str) -> dict:
    """只重算摘要并覆盖"""
    record = get_from_db(audio_filename, dialog_length)
    if not record:
        return {}
    ai_context = "\n".join(f"{c['speaker']}：{c['content']}" for c in record["dialog_data"])
    summary = call_my_ai_agent(ai_context, "请用不超过150字的中文简要总结上述对话。")
    _update_fields(audio_filename, dialog_length, summary=summary)
    record["summary"] = summary
    return record


def regenerate_meeting_points(audio_filename: str, dialog_length: str) -> dict:
    """只重算会议纪要并覆盖"""
    record = get_from_db(audio_filename, dialog_length)
    if not record:
        return {}
    ai_context = "\n".join(f"{c['speaker']}：{c['content']}" for c in record["dialog_data"])
    meeting_points = call_my_ai_agent(
        ai_context, "请将上述对话整理为分点形式的会议纪要，每点简要说明要点。"
    )
    _update_fields(audio_filename, dialog_length, meeting_points=meeting_points)
    record["meeting_points"] = meeting_points
    return record


# ========================================================================

if __name__ == '__main__':
    result = get_from_db('t-rex-roar (3).mp3', '0分2秒')
    if result:
        dialog_data = result.get('dialog_data')
        print(dialog_data)
        speakers = [item["speaker"] for item in dialog_data]
        print(speakers)
        summary = result.get('summary')
        print(dialog_data)
        meeting_points = result.get('meeting_points')
        print(summary)
        print(meeting_points)
