# -*- coding: utf-8 -*-
"""
优化版MaxKB流式接口 - 直接使用原生流式API，避免SSEClient包装开销
"""
from app.configs import load_env


import json
import aiohttp
import asyncio
from typing import Dict, Any, AsyncGenerator
from loguru import logger

from app.v1.core.enterprise_knowledge import get_final_token_and_appid, extract_paragraph_list
import os
from loguru import logger
from dotenv import load_dotenv
from pathlib import Path



async def maxkb_stream_native(message: str, dataset_id: str) -> AsyncGenerator[bytes, None]:
    """
    直接使用MaxKB原生异步流式接口，避免SSEClient包装开销

    优化点：
    1. 使用aiohttp异步HTTP客户端
    2. 直接处理SSE流，避免SSEClient包装
    3. 减少错误检测开销
    4. 优化编码处理
    """
    try:
        token, application_id = get_final_token_and_appid(dataset_id)
    except Exception as e:
        logger.error(f"获取 token 失败: {e}")
        yield "系统繁忙，获取回复失败\n".encode('utf-8')
        yield b"<source>[]</source>\n"
        return

    payload = {
        "message": message, "re_chat": False,
        "stream": True,
        "image_list": [], "document_list": [],
        "audio_list": [], "video_list": [], "form_data": {}
    }
    headers = {
        "Authorization": token,
        "Content-Type": "application/json",
    }
    chat_url = f"http://172.17.10.70:8891/api/application/chat_message/{dataset_id}"

    chat_id = chat_record_id = None
    reasoning_started = False
    any_content_sent = False

    try:
        # 使用aiohttp异步客户端
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(chat_url, headers=headers, json=payload) as resp:
                if resp.status != 200:
                    logger.error(f"MaxKB 请求失败，状态码: {resp.status}")
                    yield "请求失败，获取回复失败\n".encode('utf-8')
                    yield b"<source>[]</source>\n"
                    return

                # 直接处理流式响应
                async for line in resp.content:
                    line_str = line.decode('utf-8', errors='ignore').strip()
                    if not line_str:
                        continue

                    # 处理SSE格式：data: {...}
                    if line_str.startswith("data: "):
                        data_str = line_str[6:]  # 去掉 "data: " 前缀
                        if data_str.strip() == "[DONE]":
                            break

                        try:
                            data: Dict[str, Any] = json.loads(data_str)
                        except json.JSONDecodeError:
                            continue

                        chat_id = data.get("chat_id", chat_id)
                        chat_record_id = data.get("chat_record_id", chat_record_id)
                        content = data.get("content", "")
                        reasoning = data.get("reasoning_content", "")

                        # ---- reasoning ----
                        if reasoning:
                            if not reasoning_started:
                                reasoning_started = True
                                yield b"<think>"
                            yield reasoning.encode('utf-8', errors='ignore')
                            any_content_sent = True
                        elif reasoning_started:
                            reasoning_started = False
                            yield b"</think>\n"

                        # ---- content ----
                        if content:
                            # 简化错误检测，只检测关键错误
                            if "exception" in content.lower() or "error" in content.lower():
                                yield "系统繁忙，获取回复失败\n".encode('utf-8')
                                yield b"<source>[]</source>\n"
                                return
                            yield content.encode('utf-8', errors='ignore')
                            any_content_sent = True

                        # ---- 结束判定 ----
                        if data.get("is_end") or data.get("node_is_end"):
                            if reasoning_started:
                                yield b"</think>\n"
                                reasoning_started = False
                            break

    except Exception as e:
        logger.error(f"异步处理MaxKB流式响应异常: {e}")
        yield "系统繁忙，处理响应失败\n".encode('utf-8')
        yield b"<source>[]</source>\n"
        return

    # 处理引用信息（简化版本，减少等待时间）
    # if chat_id and chat_record_id and any_content_sent:
    #     try:
    #         await asyncio.sleep(0.5)  # 异步等待，不阻塞
    #         # 这里可以添加异步获取引用信息的逻辑
    #         yield b"<source>[]</source>\n"  # 暂时返回空引用
    #     except Exception:
    #         yield b"<source>[]</source>\n"
    # else:
    #     if not any_content_sent:
    #         yield "抱歉，没有检索到相关答案。\n".encode('utf-8')
    #     yield b"<source>[]</source>\n"


# 性能对比函数
async def performance_comparison_demo():
    """
    性能对比演示
    """
    import time
    # from app.v1.core.enterprise_knowledge import sse_stream
    # from app.utils.sync2async import sync_to_async_generator

    message = "公派出境需要准备哪些资料"
    dataset_id = "a0880f08-2102-11f0-b213-926528472ef0"

    # 测试原版本（SSEClient + sync_to_async_generator）
    # start_time = time.time()
    # chunk_count_old = 0
    # try:
    #     sync_gen = sse_stream(message=message, dataset_id=dataset_id)
    #     stream_gen = sync_to_async_generator(sync_gen)
    #     async for chunk in stream_gen:
    #         chunk_count_old += 1
    # except Exception as e:
    #     logger.error(f"原版本测试失败: {e}")
    # old_time = time.time() - start_time

    # 测试优化版本（原生异步）
    start_time = time.time()
    chunk_count_new = 0
    try:
        async for chunk in maxkb_stream_native(message=message, dataset_id=dataset_id):
            print(chunk.decode('utf-8', errors='ignore'), end='', flush=True)
            chunk_count_new += 1
    except Exception as e:
        logger.error(f"优化版本测试失败: {e}")
    new_time = time.time() - start_time



if __name__ == "__main__":
    # 运行性能对比
    asyncio.run(performance_comparison_demo())
