import datetime
import os
import uuid

import requests
from fastapi.responses import JSONResponse
from wps_callback_kit import wps

from app.configs.config import settings
from app.core.parser_adapter.adapter import ParserAdapter
from app.utils.monitor import send_monitor_event
from app.v1.core.Ifasr_new import RequestApi
from app.v1.core.deep_parser.excel_parser import ExcelParser
from app.v1.core.deep_parser.make_data import split_doc
from app.v1.core.process_speech import process_speech_data
from app.v1.core.qwq_api import qwq_chat_stream
from app.v1.core.tokens import download_single_law_csv, login_maxkb, download_file, ragflow_api
from app.v1.core.utils import is_audio_file, is_special_file, get_uuid
from app.v1.db.file_info import update_file, delete_file
from app.v1.settings import Config

###############################################################################
# 2) 第一个功能：音频/文本上传接口
###############################################################################


DEFAULT_DATASET_ID = "88c41658-009d-11f0-a772-0242ac140003"


def trans_text(file_path, username):
    """
    录音转文字
    """

    # 调用讯飞转写
    api = RequestApi(
        appid="0a2bad6a",
        secret_key="889286b36599b45bf4c0f85c46d7ccac",
        upload_file_path=file_path,
        user_id=username)
    text_result = api.get_result()
    if not text_result:
        return {"error": "音频转写失败或未得到结果"}
    out_txt_path = file_path.rsplit('.', 1)[0] + '.txt'
    with open(out_txt_path, "w", encoding="utf-8") as f:
        f.write(text_result)
    return {'out_txt_path': out_txt_path, 'text_result': text_result}


def upload_and_process(files: list, file_dir: str, username: str, project_id: str, is_private: bool, project_name: str,
                       fileId: str = None, file_type: str = None, mainid: str = None, kind: bool = True):
    """
    支持一次上传多个文件，对每个文件：
      - 若是音频文件：先调用转写，再按拆分后文档的方式处理
      - 若是普通文档：直接按拆分后文档处理
    可使用 query 参数 dataset_id 指定目标知识库 ID；若不提供则用默认值。
    """
    token = login_maxkb(kind)
    if not token:
        return JSONResponse({"content": "maxkb登录失败", "status_code": 500})
    results = []
    for file in files:
        source = 'normal'
        file_path = os.path.join(file_dir, file)
        # 项目库下载文件
        if not is_private and not os.path.exists(file_path) and fileId and file_type:
            source = 'sacp'
            if download_file(username, project_name, fileId, file, file_dir, file_type):
                delete_file(username, file, project_id)
                return JSONResponse({"content": "下载文件失败", 'status_code': 500})
        elif mainid and not os.path.exists(file_path):
            source = 'law'
            if not download_single_law_csv(mainid, file_path,username):
                delete_file(username, file, project_id)
                return JSONResponse({"content": "下载文件失败", 'status_code': 500})
        if source in ['law', 'sacp']:
            try:
                wps_url = wps.run(get_uuid(), file_path, username)
            except:
                wps_url = ''
            kwarg = {'url': wps_url, 'size': os.path.getsize(file_path)}
            update_file(username, file, project_id, kwarg)
        # 根据是否为音频文件，执行不同逻辑
        if is_audio_file(file):
            # 如果需要转写，请确保已安装并正确引入 Ifasr_new.RequestApi
            out_txt_path = file_path.rsplit('.', 1)[0] + '.txt'
            if os.path.exists(out_txt_path):
                with open(out_txt_path, "r", encoding='utf-8') as f:
                    text_result = f.read()
                    result = {'out_txt_path': out_txt_path, 'text_result': text_result}
            else:
                result = trans_text(file_path, username)
                if not result.get('out_txt_path'):
                    return JSONResponse({"content": "音频转写失败或未得到结果", 'status_code': 500})
                extension = file.split(".")[-1].lower()
                content = result.get('text_result')
                try:
                    process_speech_data(
                        content, content.encode(), extension, file, datetime.datetime.utcnow(), file_path
                    )
                except Exception as e:
                    send_monitor_event(
                        title="📡 语音文件写失败",
                        content=f"❌ 错误：{str(e)}\n📥 问题：语音文件写失败 \n 访问用户: {username}",
                        tags=["speech", "exception"],
                    )
                    return {"error": f"解析响应异常: {str(e)}"}
            # 把后续的“文档拆分 + _bach”逻辑写成一个函数, 方便复用
            return process_document_split_bach(token, os.path.basename(result.get('out_txt_path')),
                                               result.get('text_result').encode(), username,
                                               project_id, file_path, file, kind)
        else:
            # 非音频文件直接当作文档处理,判断是是否为pdf或者excel
            with open(file_path, 'rb') as f:
                file_bytes = f.read()
            result = process_document_split_bach(token, file, file_bytes, username, project_id,
                                                 file_path, kind=kind)

        results.append({
            "filename": file,
            "result": result
        })
    return JSONResponse({"results": results, 'status_code': 200})


def process_document_split_bach(token: str, filename: str, file_bytes: bytes, username: str,
                                project_id: str, file_path, audio_file: str = None,
                                kind: bool = True) -> dict:
    """
    拆分文档并批量写入 dataset_id 对应的知识库
    """
    # 调用 /api/dataset/document/split 接口

    if kind:
        kind_url = Config.maxkb_private_library_url
    else:
        kind_url = Config.maxkb_project_library_url
    if is_special_file(filename):
        # id = ragflow_api(username, file_bytes, filename, project_id, kind=kind)
        # if not id:
        #     return {'status_code': 500, "content": '接口异常'}
        almond_parser_id = str(uuid.uuid4())
        adapter = ParserAdapter(
            parser_host=settings.parser_base_url, api_key=settings.parser_api_key
        )
        adapter.submit_parse_task(
            username=username,
            filename=filename,
            file_bytes=file_bytes,
            maxkb_url=kind_url,
            maxkb_id=project_id,
            almond_parser_id=almond_parser_id
        )
        kwarg = {"is_almond_parser": True, "almond_parser_id": almond_parser_id}
        update_file(username, filename, project_id, kwarg)
        return {}
    else:

        split_url = f"{kind_url}/api/dataset/document/split"
        split_headers = {
            "accept": "application/json, text/plain, */*",
            "authorization": token
        }
        try:
            files_payload = {
                "file": (
                    filename,
                    file_bytes,
                    "text/plain" if filename.lower().endswith(".txt") else
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                )
            }
            resp_split = requests.post(split_url, headers=split_headers, files=files_payload, timeout=30)
            resp_split.raise_for_status()
            split_json = resp_split.json()
            split_data_list = split_json["data"]
            first_file_data = split_data_list[0]
            file_name = first_file_data.get("name", filename)
            contents = first_file_data.get("content", [])
        except Exception as e:
            send_monitor_event(
                title="📡 知识库内部调用异常",
                content=f"❌ 错误：{str(e)}\n📥 问题：分段失败 \n📌 数据集：{project_id} \n 访问用户: {username}",
                tags=["maxkb", "exception"],
            )
            return {'status_code': 500, "content": '接口异常'}

        # 拼装要提交给 _bach 的数据
        paragraphs = []
        for c in contents:
            paragraph_item = {
                "title": c.get("title", ""),
                "content": c.get("content", ""),
                "status": ["nn2"],
                "is_active": True,
                "meta": "{}"
            }
            paragraphs.append(paragraph_item)

        payload = [
            {
                "name": file_name,
                "paragraphs": paragraphs
            }
        ]
        # 调用 _bach
        bach_url = f"{kind_url}/api/dataset/{project_id}/document/_bach"
        bach_headers = {
            "accept": "application/json, text/plain, */*",
            "authorization": token,
            "content-type": "application/json",
            "referer": f"{kind_url}/ui/dataset/upload?id={project_id}",
        }

        try:
            resp_bach = requests.post(bach_url, headers=bach_headers, json=payload, timeout=5)
            id = resp_bach.json()['data'][0]['id']
        except Exception as e:
            send_monitor_event(
                title="📡 知识库内部调用异常",
                content=f"❌ 错误：{str(e)}\n📥 问题：转向量失败 \n📌 数据集：{project_id} \n 访问用户: {username}",
                tags=["maxkb", "exception"],
            )
            return {"error": f"解析 bach 响应异常: {str(e)}"}
    abstract = ''
    if is_audio_file(file_path):
        abstract = ''
    else:
        if is_allowed_file(file_path):
            psr = ExcelParser()
            result = psr.to_text(file_path)
        else:
            result = split_doc(file_path)
        result = ','.join(result)
        messages_list = [
            {'role': 'user', 'content': result[:70000]},
            {'role': 'system', 'content': '请问题总结出为100字以内'}]
        for i in qwq_chat_stream(messages_list=messages_list, chat_type=True):
            abstract += i
    abstract = abstract.replace("<think>", "").replace("</think>", "").strip()
    # 更新文件信息
    if audio_file:
        filename = audio_file
    kwarg = {'file_id': id, 'abstract': abstract}
    update_file(username, filename, project_id, kwarg)


###############################################################################
# 3) 第二个功能：上传表格文件到 /upload/{dataset_id}
###############################################################################
ALLOWED_EXTENSIONS = {"csv", "xlsx", "xls"}


def is_allowed_file(filename: str) -> bool:
    """
    判断文件扩展名是否在允许范围内
    """
    if "." not in filename:
        return False
    extension = filename.rsplit(".", 1)[-1].lower()
    return extension in ALLOWED_EXTENSIONS


def upload_and_process_table(file: str, file_bytes, dataset_id: str, kind: bool = True):
    """
    在一次请求中上传多个 (csv / xlsx / xls)，
    每个文件依次调用 /document/table 接口写入 dataset_id 对应的知识库。
    """
    if kind:
        kind_url = Config.maxkb_private_library_url
    else:
        kind_url = Config.maxkb_project_library_url
    token = login_maxkb()
    if not token:
        return JSONResponse({"content": "maxkb登录失败", "status_code": 500})

    upload_results = []

    # 根据扩展名确定 MIME
    extension = file.rsplit(".", 1)[-1].lower()
    if extension == "csv":
        mime_type = "text/csv"
    elif extension == "xlsx":
        mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    else:
        mime_type = "application/vnd.ms-excel"

    upload_url = f"{kind_url}/api/dataset/{dataset_id}/document/table"
    upload_headers = {
        "accept": "application/json, text/plain, */*",
        "authorization": token
    }
    files_param = {
        "file": (file, file_bytes, mime_type)
    }

    try:
        resp_upload = requests.post(upload_url, headers=upload_headers, files=files_param)
        resp_upload.raise_for_status()
        upload_json = resp_upload.json()
        return upload_results.append({
            "filename": file,
            "status": "success",
            "upload_response": upload_json
        })
    except Exception as e:
        pass


###############################################################################
# 4) 第三个功能：查询文档信息 /document_info/{dataset_id}
###############################################################################

def document_info(dataset_id: str, kind: bool = True) -> dict:
    """
    调用 /dataset/{dataset_id}/document/1/10 接口，获取文档列表信息
    """
    token = login_maxkb(kind)
    if not token:
        return {"content": "maxkb登录失败", "status_code": 500}
    if kind:
        kind_url = Config.maxkb_private_library_url
    else:
        kind_url = Config.maxkb_project_library_url
    url = f"{kind_url}/api/dataset/{dataset_id}/document"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Authorization": token
    }
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
    except Exception as e:
        return {"error": f"GET 请求异常: {str(e)}"}

    try:
        data = response.json()
    except Exception as e:
        return {"error": f"解析 GET 响应异常: {str(e)}", "raw_response": response.text}

    return data


###############################################################################
# 6) 第五个功能：删除文档 /delete_document/{dataset_id}/{document_id}
###############################################################################
def delete_doc(dataset_id: str, document_id: str, kind: bool = True, username: str = None) -> dict:
    """
    调用 DELETE /api/dataset/{dataset_id}/document/{document_id} 删除文档
    """
    token = login_maxkb(kind,username)
    if not token:
        return {"content": "maxkb登录失败", "status_code": 500}
    if kind:
        kind_url = Config.maxkb_private_library_url
    else:
        kind_url = Config.maxkb_project_library_url
    url = f"{kind_url}/api/dataset/{dataset_id}/document/{document_id}"
    headers = {
        "Authorization": token
    }
    # 如果后端严格要求 params，则可在此定义；若不需要，可删除
    params = {
        "dep": "[object Object]",
        "__v_isRef": "true",
        "__v_isShallow": "false",
        "_rawValue": "false",
        "_value": "false"
    }
    try:
        resp = requests.delete(url, headers=headers, params=params, timeout=5)
        return resp.json()
    except Exception as e:
        send_monitor_event(
            title="📡 知识库内部调用异常",
            content=f"❌ 错误：{str(e)}\n📥 问题：删除文件失败 \n📌 数据集：{dataset_id} \n 访问用户: {username}",
            tags=["maxkb", "exception"],
        )
        return {"error": f"解析响应异常: {str(e)}"}


if __name__ == "__main__":
    # print(login_maxkb())
    # print(delete_doc('3931c40a-2023-11f0-93fa-0242ac120002','46efcf88-2023-11f0-8049-0242ac120002'))
    print(document_info('b8551e3a-20d2-11f0-8a44-0242ac120002'))
    upload_and_process(['教材4.保险学 第4版 魏华林 第1-4章+第10章(1).docx'],
                       ''r'D:\WXWork\1688857608540968\Cache\File\2025-05', 'qu.mingming',
                       'e976fb7c-323f-11f0-a1d7-62af8668a4ba', False, '测试')
