# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/15 19:20
@Auth ： qu ming
@File ：new_trans.py
@IDE ：PyCharm
"""
import os

from langchain.chains.retrieval_qa.base import RetrievalQA
from langchain_core.callbacks import CallbackManager
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate
from langchain_community.embeddings import OllamaEmbeddings
from langchain_community.vectorstores import Milvus
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler

from app.v1.core.deep_parser.excel_parser import ExcelParser
from app.v1.core.get_content import split_document
from app.v1.core.qwq_api import qwq_chat_stream
from app.v1.core.upload import is_allowed_file
from app.v1.core.utils import get_uuid
from app.v1.core.deep_parser.make_data import split_doc
from app.v1.settings import Config


class Qa:
    def __init__(self):
        self.ollama_bge_base = Config.ollama_base_url
        self.chat_api_base = Config.vllm_openai_base_url
        self.connection_args = {"host": Config.milvus_host, "port": Config.milvus_port}
        self.embeddings = OllamaEmbeddings(model="bge-m3",
                                           base_url=self.ollama_bge_base)

    def chat_deepseek(self, query: str, collection_name: str):
        # LLM
        llm = ChatOpenAI(
            model='QwQ-32B',
            openai_api_key=Config.vllm_openai_api_key,
            openai_api_base=self.chat_api_base,
            streaming=True,
            callback_manager=CallbackManager([StreamingStdOutCallbackHandler()]))

        vector_db = Milvus(embedding_function=self.embeddings, connection_args=self.connection_args,
                           collection_name=str(collection_name))
        tech_template = """Please answer the questions based on the provided content. Please do not make up an answer. By
                        default, please reply in Chinese. If you ask questions in English or request a response in English,
                        please reply in English.

                        {context}

                        Q: {question}

                        A:   """

        PROMPT = PromptTemplate(template=tech_template, input_variables=["context", "question"])
        chat_chain = RetrievalQA.from_chain_type(llm=llm,
                                                 chain_type="stuff",
                                                 retriever=vector_db.as_retriever(k=2),
                                                 # chain_type_kwargs={"prompt": PROMPT},
                                                 return_source_documents=False)
        return chat_chain(query)['result']

    def vector_data(self, file_path):
        # # LLM

        collection_name = f"a{(get_uuid().replace('-', ''))}"
        texts = split_document(file_path)
        Milvus.from_documents(
            documents=texts,  # 设置保存的文档
            embedding=self.embeddings,
            collection_name=collection_name,  # 设置集合名称
            drop_old=True,
            connection_args=self.connection_args)
        return collection_name

    def chat_content(self, question: str,  collection_name: str,  username:str):
        """
        根据文章内容来回答问题
        """
        file_path = os.path.join(Config.txt_storage_path, f'user_files/{username}', collection_name)
        if is_allowed_file(file_path):
            psr = ExcelParser()
            result = psr.to_text(file_path)
        else:
            result = split_doc(file_path)
        if result:
            if isinstance(result,list):
                result = ','.join(result)
            if len(result) > 70000:
                result = result[:65000]
            if len(result) > 65000:
                percent = int(65000 / len(result) * 100)
                # 返回特定json或自定义格式，方便前端识别
                warn_msg = f"<progress>超出字数限制，小杏仁 只阅读了前 {percent}%</progress>"
                yield warn_msg
            messages = [
                {'role': 'user', 'content': question},
                {'role': 'system', 'content': result}]
            yield from qwq_chat_stream(messages, chat_type=True)
        else:
            return (i for i in '文件内容获取失败')


if __name__ == '__main__':
    qa = Qa()
    # print(qa.vector_data(r'D:\WXWork\1688857608540968\Cache\File\2023-12\进项-获取验证码接口.pdf'))
    # print(qa.chat_deepseek('关键点是什么', 'aa6b65d3b24ba11f0bdf8c8f750f3d9e4'))
    for i in qa.chat_content('解读文档', 'htmltoword001.docx','qu.mingming'):
        print(i, end='', flush=True)
