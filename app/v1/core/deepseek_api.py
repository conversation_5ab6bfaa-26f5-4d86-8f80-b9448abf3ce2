# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/10 18:03
@Auth ： qu ming
@File ：ollama-api.py
@IDE ：PyCharm
"""
import json
import requests


def deepseek_chat(messages: list):
    """
    使用 DeepSeek HTTP 接口发送消息并解析流式多行 JSON。
    messages 结构如：
        [
            {'role': 'system', 'content': '你将使用搜索结果来回答问题。'},
            {'role': 'user', 'content': '哪吒2现在多少亿了？'},
            {'role': 'system', 'content': '以下是搜索结果...'}
        ]
    """
    deepseek_api_url = "http://20.171.88.164:11434/api/chat"
    payload = {
        "model": "t85:latest",
        "messages": messages,
        "frequency_penalty": 0,
        "max_token": 4096,
        "presence_penalty": 0,
        "response_format": {
            "type": "text"
        }
    }
    headers = {"Content-Type": "application/json"}

    try:
        with requests.post(deepseek_api_url, headers=headers, json=payload, stream=True) as r:
            r.raise_for_status()
            for line in r.iter_lines(decode_unicode=True):
                line = line.strip()
                if not line:
                    continue
                chunk = json.loads(line)
                content_chunk = chunk.get("message", {}).get("content", "")
                if chunk.get("done"):
                    break
                yield content_chunk
    except requests.exceptions.RequestException as e:
        print(f"\n[Error] DeepSeek 请求出错: {e}")


if __name__ == '__main__':
    messages = [{"content": '如何快速挣到100w', "role": "user"}]
    deepseek_chat(messages)

