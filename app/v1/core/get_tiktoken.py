# -*- coding: utf-8 -*-
"""
@Time ： 2023/7/28 17:08
@Auth ： qu ming
@File ：get_tiktoken.py
@IDE ：PyCharm
"""
from typing import List
import tiktoken


def tokens_from_gpt(prompt: str,
                    completion: str,
                    unit_price: float = 0.002,
                    encoding_name: str = 'cl100k_base') -> dict:
    """
    model: gpt-3.5-turbo, 每 1,000 个标记($0.002)
    Returns the number of tokens in a text string and fee.
    """
    encoding = tiktoken.get_encoding(encoding_name)
    prompt_tokens = len(encoding.encode(prompt)) + 8
    completion_tokens = len(encoding.encode(completion))
    fee = format((prompt_tokens + completion_tokens) * unit_price / 1000, '.7f')
    return {'prompt_tokens': prompt_tokens,
            'completion_tokens': completion_tokens,
            'fee': fee}


def fee_from_pic(num: int = 1) -> float:
    """
    model: Dall-E
    Returns fee of  a picture.
    """
    return 0.02 * num


def tokens_from_embedding(contents: List,
                          unit_price: float = 0.0001,
                          encoding_name: str = 'cl100k_base') -> dict:
    """
    model: text-embedding-ada-002,每 1,000 个标记($0.0001)
    Returns the number of tokens in a text string and fee.
    """
    encoding = tiktoken.get_encoding(encoding_name)
    tokens = 0
    for content in contents:
        if isinstance(content, str):
            tokens += len(encoding.encode(content))
        else:
            tokens += len(encoding.encode(content.page_content))
    fee = format(tokens * unit_price / 1000, ".8f")
    return {'tokens': tokens, 'fee': fee}


def tokens_from_model(prompt: str,
                      completion: str,
                      encoding_name: str = 'cl100k_base') -> dict:
    """
    Returns the number of tokens in a text string.
    """
    encoding = tiktoken.get_encoding(encoding_name)
    prompt_tokens = len(encoding.encode(prompt)) + 8
    completion_tokens = len(encoding.encode(completion))
    return {'prompt_tokens': prompt_tokens,
            'completion_tokens': completion_tokens}


if __name__ == '__main__':
    print(tokens_from_model('你好', '我可以帮助你么'))
