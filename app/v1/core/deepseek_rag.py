import requests
import json


class Rag:
    def send_chat_message(self, query: str,isCenter: bool = False):
        """
        发送聊天消息并解析响应内容

        :param query: 用户输入的查询消息
        :return: 解析后的内容
        """
        if isCenter:
            # 测试应用
            project_id = '88cd72d4-ffd0-11ef-b356-0242ac140003'
            key = 'application-7e3992463075758b8d856a42f2ad7662'
        else:
            # 正式应用
            project_id = '3099850c-0a21-11f0-b446-0242ac140003'
            key = 'application-564e808918159fa43268228f798afb21'

        # 设置请求的 URL
        url = f"http://*************:8088/api/application/chat_message/{project_id}"  # 修改内容！！！！！！！！！

        # 设置请求头
        headers = {
            "accept": "application/json",
            "Authorization": key  # 使用有效的密钥 #修改内容！！！！！
        }

        # 设置请求体内容
        data = {
            "message": query,  # 使用传入的查询作为消息
            "re_chat": False,
            "stream": True
        }

        try:
            # 发送 POST 请求
            response = requests.post(url, headers=headers, json=data)

            # 检查返回的数据是否为空或者仅包含空格
            if response.text.strip():
                try:
                    # 清理响应文本，并将每个部分的内容提取出来
                    cleaned_response = response.text.strip().split("\n")
                    json_objects = []
                    complete_content = ""

                    # 遍历每一行内容，找到以 'data:' 开头的部分
                    for item in cleaned_response:
                        if item.startswith("data:"):
                            item = item[len("data:"):].strip()
                            try:
                                json_obj = json.loads(item)
                                json_objects.append(json_obj)

                                # 拼接所有 content 部分
                                if 'content' in json_obj:
                                    complete_content += json_obj['content']

                            except json.JSONDecodeError as e:
                                print(f"Failed to decode JSON: {e}")
                                continue

                    # 在返回之前，将 /api/image/... 替换为 http://*************:8088/api/image/...
                    # 将 ![](/api/image/xxx) 转为 ![](http://*************:8088/api/image/xxx)
                    complete_content = complete_content.replace(
                        "](/api/image/",
                        "](http://*************:8088/api/image/"
                    )

                    print(f"Complete Content: {complete_content}")

                    return complete_content  # 返回解析的完整内容

                except json.JSONDecodeError as e:
                    print(f"Failed to decode JSON response: {e}")
                    return None
            else:
                print("Empty response or response only contains whitespace")
                return None

        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            return None


if __name__ == '__main__':
    query = "恒大集团相关信息"

    # 创建 rag 类的实例
    chat_instance = Rag()
    # 调用 send_chat_message 方法
    response = chat_instance.send_chat_message(query,True)
    if response:
        print(f"响应内容: {response}")
    else:
        print("没有获取到有效的响应内容。")
