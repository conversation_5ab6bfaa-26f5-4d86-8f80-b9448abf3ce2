# -*- coding: utf-8 -*-
"""
FastAPI 封装的伭流式 SSE 转发服务
启动: uvicorn main:app --host 0.0.0.0 --port 8190
"""

import json
import re
from typing import List, Dict, Any, Generator, Optional
import requests
from loguru import logger
from pydantic import BaseModel
from sseclient import SSEClient
from pymongo import MongoClient
from app.v1.settings import Config
from app.utils.monitor import send_monitor_event

# ----------------- MongoDB 配置 -----------------
MONGO_URI = Config.mongo_url
MONGO_DB = Config.mongo_database
MONGO_COLLECTION = "dataset_application_map"


def detect_error_content(content: str, context: str = "") -> bool:
    """
    检测内容是否包含错误信息

    Args:
        content: 要检测的内容
        context: 上下文信息，用于监控报警

    Returns:
        bool: True 表示检测到错误内容，False 表示正常
    """
    if not content:
        return False

    lower_content = content.lower()
    error_keywords = ["exception", "traceback", "error:", "cannot connect", "stack",
                      "connection refused", "timeout", "network error", "http error"]

    if any(err in lower_content for err in error_keywords):
        send_monitor_event(
            title="📡 MaxKB异常内容检测",
            content=f"⚠️ 检测到错误内容：\n{context}\n内容：{content[:1000]}...",
            tags=["maxkb", "exception", "sse"],
        )
        return True
    return False


def get_dataset_config(dataset_id: str) -> Dict[str, str]:
    client = MongoClient(MONGO_URI)
    db = client[MONGO_DB]
    collection = db[MONGO_COLLECTION]
    config = collection.find_one({"dataset_id": dataset_id})
    if not config:
        raise ValueError(f"dataset_id={dataset_id} 未在 MongoDB 中找到配置")
    return {
        "access_token": config["access_token"],
        "application_id": config["application_id"]
    }


# ----------------- 常量 -----------------
LOGIN_URL = "http://************:8891/api/user/login"
AUTH_URL = "http://************:8891/api/application/authentication"
DETAIL_URL_TMPL = (
    "http://************:8891/api/application/"
    "{application_id}/chat/{chat_id}/chat_record/{chat_record_id}"
)
USERNAME = "admin"
PASSWORD = "adminyzw@1"


# ----------------- FastAPI 输入模型 -----------------
class ChatRequest(BaseModel):
    question: str
    chat_style: Optional[bool] = False
    robot_name: Optional[str] = None
    search_type: Optional[str] = None


# ----------------- 文件名处理工具 -----------------
STRIP_SUFFIX_ENABLED = False
_EXT_PATTERN = re.compile(r"\\.(txt|md|markdown|pdf|docx?|html?|xls|xlsx|csv|zip)$", re.I)


def _strip_suffix(text: str) -> str:
    return _EXT_PATTERN.sub("", text)


def extract_paragraph_list(detail_json: Dict[str, Any], question: str) -> List[Dict[str, Any]]:
    paragraphs = []
    for exec_item in detail_json.get("data", {}).get("execution_details", []):
        for p in exec_item.get("paragraph_list", []):
            p["query"] = question
            for key in ("title", "name", "file_name", "doc_name", "content"):
                if key in p and isinstance(p[key], str) and STRIP_SUFFIX_ENABLED:
                    p[key] = _strip_suffix(p[key])
            paragraphs.append(p)
    return paragraphs


# ----------------- 获取 token -----------------
def get_final_token_and_appid(dataset_id: str) -> (str, str):
    config = get_dataset_config(dataset_id)
    access_token = config["access_token"]
    application_id = config["application_id"]

    login_resp = requests.post(
        LOGIN_URL,
        json={"username": USERNAME, "password": PASSWORD},
        headers={"Content-Type": "application/json"},
        timeout=10,
    )
    login_resp.raise_for_status()
    token1 = login_resp.json().get("data")
    if not token1:
        raise RuntimeError("/login 未返回 data")

    auth_resp = requests.post(
        AUTH_URL,
        json={"access_token": access_token},
        headers={"Authorization": token1, "Content-Type": "application/json"},
        timeout=10,
    )
    auth_resp.raise_for_status()
    token2 = auth_resp.json().get("data")
    if not token2:
        raise RuntimeError("/authentication 未返回最终 token")
    return token2, application_id


# ----------------- SSE 主函数 -----------------
def sse_stream(message: str, dataset_id: str) -> Generator[bytes, None, None]:
    """
    企业知识库 SSE：
    1) 正常流 → 正文 / <think> / <source>
    2) 若流阶段无正文，则回退 detail 接口给固定答复
    """
    try:
        token, application_id = get_final_token_and_appid(dataset_id)
    except Exception as e:
        logger.error(f"获取 token 失败: {e}")
        yield "系统繁忙，获取回复失败\n"
        yield b"<source>[]</source>\n"
        return

    payload = {
        "message": message, "re_chat": False,
        "image_list": [], "document_list": [],
        "audio_list": [], "video_list": [], "form_data": {}
    }
    headers = {
        "Authorization": token,
        "Content-Type": "application/json",
        "Accept": "*/*",
    }
    chat_url = f"http://************:8891/api/application/chat_message/{dataset_id}"
    resp = requests.post(chat_url, headers=headers, json=payload, stream=True)

    if resp.status_code != 200:
        yield "SSE 请求失败，获取回复失败\n"
        yield b"<source>[]</source>\n"
        return

    sse = SSEClient(resp)
    chat_id = chat_record_id = None
    reasoning_started = False
    any_content_sent = False  # ★ 是否已经给前端发过正文

    for event in sse.events():
        if not event.data.strip():
            continue
        try:
            data: Dict[str, Any] = json.loads(event.data)
        except json.JSONDecodeError:
            continue

        chat_id = data.get("chat_id", chat_id)
        chat_record_id = data.get("chat_record_id", chat_record_id)
        content = data.get("content", "")
        reasoning = data.get("reasoning_content", "")

        # ---- reasoning ----
        if reasoning:
            if not reasoning_started:
                reasoning_started = True
                yield b"<think>"
            yield reasoning.encode()
            any_content_sent = True
        elif reasoning_started:
            reasoning_started = False
            yield b"</think>\n"

        # ---- content ----
        if content:
            yield content.encode()
            any_content_sent = True

        # ---- 结束判定 ----
        if data.get("is_end") or data.get("node_is_end"):
            if reasoning_started:
                yield b"</think>\n"
                reasoning_started = False
            break

    # ---------- 获取引用 & 兜底正文 ----------
    if chat_id and chat_record_id:
        detail_url = DETAIL_URL_TMPL.format(
            application_id=application_id,
            chat_id=chat_id,
            chat_record_id=chat_record_id,
        )
        try:
            import time
            time.sleep(2)  # 等待来源生成
            detail_json = requests.get(detail_url,
                                       headers={"Authorization": token},
                                       timeout=10).json()

            # ★ 若流阶段没发送正文，则从 detail 里兜底
            if not any_content_sent:
                answer_text = detail_json["data"].get("answer_text", "") or ""
                if not answer_text:
                    # 部分版本把答案放在 execution_details 最后一个 reply-node
                    exec_list = detail_json["data"].get("execution_details", [])
                    if exec_list:
                        answer_text = exec_list[-1].get("answer", "")
                if answer_text:
                    yield answer_text.encode()  # 兜底正文
                    any_content_sent = True
                else:
                    yield "抱歉，没有检索到相关答案。\n"

            # 构造引用
            paragraphs = extract_paragraph_list(detail_json, message)
            yield f"<source>{json.dumps(paragraphs, ensure_ascii=False)}</source>\n".encode()

        except Exception as e:
            yield f"<source>{{\"error\":\"获取引用内容失败: {e}\"}}</source>\n".encode()
    else:
        if not any_content_sent:
            yield "抱歉，没有检索到相关答案。\n"
        yield b"<source>[]</source>\n"
