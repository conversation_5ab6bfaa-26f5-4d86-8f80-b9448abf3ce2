# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/22 10:39
@Auth ： qu ming
@File ：semantic_recognition.py
@IDE ：PyCharm
"""

import re
import jieba
from app.v1.settings import Config

def get_company_list():
    """
    获取上市公司列表
    """
    with open(Config.listed_company_txt_path, 'r', encoding='utf-8') as f:
        return f.read().split('\n')


def get_company(text: str):
    """
    从输入的文字中提取上市公司
    """
    jieba.load_userdict(Config.listed_company_txt_path)
    seg_list = jieba.cut(text, cut_all=False)
    new_list = list(seg_list)
    company_list = get_company_list()
    listed_company = []
    for i in new_list:
        if i in company_list:
            listed_company.append(i)
    return listed_company


def judge_question(text: str):
    """
    分离公司和年份
    """
    num_list = re.findall("\d+", text)
    if num_list:
        # 若提问中，财报的年份有多个，则取第一个
        num_list = [f"20{num}" if len(num) == 2 else num for num in num_list]
        # 判断是否为年份区间
    company = get_company(text)
    if len(company) == 1 and len(num_list) == 1:
        return {'companyName': company[0], 'year': num_list[0]}
    elif len(company) == 1 and not num_list:
        return {'companyName': company[0]}
    else:
        return '请输入单个公司和年份'


if __name__ == '__main__':
    print(judge_question("2024平安银行"))
