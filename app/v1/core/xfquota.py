# core/xfquota.py
# -*- coding: utf-8 -*-
"""
讯飞云转写配额检查（实时聚合明细表，无需预扣回滚）
  • 直接从 audio_transcriptions 聚合当月已用分钟
  • 优先使用 users.monthly_audio_quota，否则使用 config 默认值
  • 如果超配额，抛出 RuntimeError
"""

from datetime import datetime, timezone
from pydub import AudioSegment
from pymongo import MongoClient
from app.v1.settings import Config

# ———— Mongo 配置 ————
MONGO_URI = Config.mongo_url
MONGO_DB = Config.mongo_database
TRANS_COLLECTION = "audio_transcriptions"
USERS_COLLECTION = "users"
CONFIG_COLLECTION = "config"

DEFAULT_MONTHLY_QUOTA_KEY = "monthly_audio_quota_default"
DEFAULT_MONTHLY_QUOTA_VAL = 180


def _get_quota_for_user(user_id: str) -> int:
    client = MongoClient(MONGO_URI)
    db = client[MONGO_DB]
    users = db[USERS_COLLECTION]
    cfg = db[CONFIG_COLLECTION]

    user_doc = users.find_one({"_id": user_id}) or {}
    if isinstance(user_doc.get("monthly_audio_quota"), (int, float)):
        quota = int(user_doc["monthly_audio_quota"])
    else:
        cfg_doc = cfg.find_one({"_id": DEFAULT_MONTHLY_QUOTA_KEY}) or {}
        quota = int(cfg_doc.get("value", DEFAULT_MONTHLY_QUOTA_VAL))

    client.close()
    return quota


def _get_used_this_month(user_id: str) -> int:
    client = MongoClient(MONGO_URI)
    db = client[MONGO_DB]
    trans = db[TRANS_COLLECTION]

    now = datetime.now(timezone.utc)
    month_start = datetime(now.year, now.month, 1, tzinfo=timezone.utc)

    pipeline = [
        {"$match": {
            "user_id": user_id,
            "transcribe_date": {"$gte": month_start, "$lt": now}
        }},
        {"$group": {"_id": None, "total": {"$sum": "$used_minutes"}}}
    ]
    agg = list(trans.aggregate(pipeline))
    already = int(agg[0]["total"]) if agg else 0

    client.close()
    return already


def check_and_reserve_quota(user_id: str, file_path: str) -> int:
    """
    调度后台任务前调用：
      1) 计算 file_path 时长（分钟，向上取整）
      2) 读取本月已用 + 本次 need，如果超配额则抛 RuntimeError
      3) 返回 need（分钟）
    """
    audio = AudioSegment.from_file(file_path)
    duration_ms = len(audio)
    need = max(1, (duration_ms + 59_999) // 60_000)

    used = _get_used_this_month(user_id)
    quota = _get_quota_for_user(user_id)

    if used + need > quota:
        raise RuntimeError(
            f"本月已用 {used} 分钟,超额"
        )
    return need
