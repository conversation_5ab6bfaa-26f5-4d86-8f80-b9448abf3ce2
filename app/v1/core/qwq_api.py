import json
import requests
from typing import Generator, List, Dict, Optional

from app.utils.monitor import send_monitor_event

# ================================== Backend 配置 ==================================
BACKEND_MAP = {
    # 本地 QwQ Wrapper
    "normal": {
        "url":  "http://idc-vllm.bdo.com.cn/v1/chat/completions",
        "key":  "q5A53chTPgb4wkMHZhZUH2c1wd4ph6Aw",
        "model": "QwQ-32B"
    },
    # 线上 vLLM 服务
    "online": {
        "url":  "http://idc-vllm.bdo.com.cn/v1/chat/completions",
        "key":  "q5A53chTPgb4wkMHZhZUH2c1wd4ph6Aw",
        "model": "QwQ-32B"
    }
}
# 默认 backend
DEFAULT_BACKEND = "normal"

# ================================== 通用 headers 构造 ==================================
def _headers(api_key: str) -> Dict[str, str]:
    return {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

# ================================== 默认系统提示 ==================================
DEFAULT_MESSAGES: List[Dict[str, str]] = [
    {"role": "system", "content": "你是立信小杏仁助手"}
]

# =================================================================================
# 流式接口
# =================================================================================
def qwq_chat_stream(
    messages_list: List[Dict[str, str]],
    backend: str = DEFAULT_BACKEND,
    chat_type: bool = False
) -> Generator[str, None, None]:
    """
    流式调用：将“思考过程”整体包裹在 <think>…</think> 中。
    * messages_list: 不含默认 system 提示，仅需传本轮对话内容
    * backend: 'normal'（本地 wrapper）或 'online'（vLLM 服务）
    """
    if backend not in BACKEND_MAP:
        raise ValueError(f"backend must be one of {list(BACKEND_MAP)}, got {backend}")

    cfg = BACKEND_MAP[backend]
    if chat_type:
        messages = messages_list
    else:
        messages = DEFAULT_MESSAGES + messages_list

    payload = {
        "model": cfg["model"],
        "messages": messages,
        "stream": True
    }
    try:
        with requests.post(cfg["url"], headers=_headers(cfg["key"]),
                           json=payload, stream=True) as resp:
            resp.raise_for_status()

            thinking_opened = False
            answering = False

            for raw in resp.iter_lines(decode_unicode=True):
                if not raw:
                    continue
                line = raw.lstrip("data: ").strip()
                try:
                    chunk = json.loads(line)
                except json.JSONDecodeError:
                    continue

                delta = chunk.get("choices", [{}])[0].get("delta", {})

                # 思考过程
                thought = delta.get("reasoning_content")
                if thought:
                    if not thinking_opened:
                        yield "<think>"
                        thinking_opened = True
                    yield thought
                    continue

                # 正式回答
                content = delta.get("content")
                if content:
                    if thinking_opened:
                        yield "</think>\n"
                        thinking_opened = False
                    if not answering:
                        answering = True
                    yield content
    except Exception as e:
        send_monitor_event(
            title="📡 语言模型QwQ-32B调用失败",
            content=f"❌ 错误：{str(e)}\n📥 问题：语言模型调用失败 \n 负责人: 严紫炜",
            tags=["llm", "exception"],
        )
        content = '亲，小杏仁暂时无法访问'
        if '400 Client Error: Bad Request for url' in str(e):
            content = '已超过当前模型最大Token数，请删减内容后再问答'
        yield content

# =================================================================================
# 非流式一次性接口
# =================================================================================
def qwq_chat_once(
    user_question: str,
    extra_messages: Optional[List[Dict[str, str]]] = None,
    backend: str = DEFAULT_BACKEND
) -> str:
    """
    请求完整回答（非流式）。
    * extra_messages: 额外上下文，插入在默认 system 和用户问题之间
    * backend: 同上
    """
    if backend not in BACKEND_MAP:
        raise ValueError(f"backend must be one of {list(BACKEND_MAP)}, got {backend}")

    cfg = BACKEND_MAP[backend]

    messages = DEFAULT_MESSAGES.copy()
    if extra_messages:
        messages.extend(extra_messages)
    messages.append({"role": "user", "content": user_question})

    payload = {
        "model": cfg["model"],
        "messages": messages,
        "stream": False
    }

    resp = requests.post(cfg["url"], headers=_headers(cfg["key"]), json=payload)
    resp.raise_for_status()
    data = resp.json()

    return data.get("choices", [{}])[0].get("message", {}).get("content", "")

# =================================================================================
# Demo
# =================================================================================
if __name__ == "__main__":
    question = "以markdown的形式帮我做一份5月日本旅行表"
    with open(r'C:\Users\<USER>\Desktop\新建文本文档 (7).txt',encoding='utf-8')as f:
        data = f.read()
        print(len(data))
    print("\n=== normal (本地) 流式输出 ===")
    for chunk in qwq_chat_stream([{"role": "user", "content": data}], backend="normal"):
        print(chunk, end="", flush=True)

    print("\n\n=== online (远程) 非流式输出 ===")
    # print(qwq_chat_once(question, backend="online"))
