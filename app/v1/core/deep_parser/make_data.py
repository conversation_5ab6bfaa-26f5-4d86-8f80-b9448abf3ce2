# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/6 14:54
@Auth ： qu ming
@File ：make_data.py
@IDE ：PyCharm
"""

from PyPDF2 import PdfReader
from langchain_community.document_loaders import UnstructuredFileLoader, UnstructuredWordDocumentLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter


def split_doc(file_path: str):
    """
    根据输入的文件路径去生成向量文件
    """

    # 初始化文本分割器
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=30
    )
    if '.txt' in file_path:
        loader = UnstructuredFileLoader(file_path)
        texts = text_splitter.split_documents(loader.load())
        texts = [doc.page_content for doc in texts]
        return texts
    elif '.pdf' in file_path or '.PDF' in file_path:
        raw_list = []
        reader = PdfReader(file_path)
        for i, page in enumerate(reader.pages):
            text = page.extract_text()
            raw_list.append(text)
        return raw_list
    elif 'doc' in file_path or 'docx' in file_path:
        loader = UnstructuredWordDocumentLoader(file_path)
        texts = text_splitter.split_documents(loader.load())
        texts = [doc.page_content for doc in texts]
        return texts


if __name__ == '__main__':
    # print(split_doc(r'C:\Users\<USER>\Desktop\瞿明明-Python-本科-简历.pdf'))
    # print(split_doc(r'C:\Users\<USER>\Documents\1. 手机银行-转账-转账汇款.docx'))
    print(split_doc(r"C:\Users\<USER>\Documents\1. 手机银行-转账-转账汇款.docx"))
