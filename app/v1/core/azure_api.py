# -*- encoding: utf-8 -*-
"""
@File    : azure_api.py
@Time    : 2023/6/27 10:34
<AUTHOR> ming
"""

from typing import List

from openai import AzureOpenAI

from app.v1.settings import Config


def azure_api(messages: List, chatgpt_version: str = '4o'):
    """
    调用azure_openai接口，返回问答内容,默认chatgpt_version为4.0
    """
    client = AzureOpenAI(
        azure_endpoint=Config.us_azure_openai_base_url,
        api_key=Config.us_azure_east_api_key,
        api_version=Config.us_azure_openai_api_version,
    )
    completion = client.chat.completions.create(
        model=Config.us_azure_openai_deployment_id,
        messages=messages,
        max_tokens=5000,
        temperature=0,
        top_p=0.95,
        frequency_penalty=0,
        presence_penalty=0,
        stop=None,
        stream=False
    )

    return completion.to_dict()['choices'][0]['message']['content']


if __name__ == '__main__':
    print(azure_api([{'role': 'user', 'content': '你是gpt几'}]))
