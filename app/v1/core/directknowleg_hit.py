#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目 / 个人 / 企业知识库统一问答（带 <source> 引用）
"""

from __future__ import annotations
import json, re, requests
from typing import List, Dict, Any, Generator
import os
import sys  # 临时

from app.v1.core.process_speech import get_assistant
from app.v1.models.UserProject import Project

# sys.path.append(os.path.abspath(os.path.join(__file__, "..", "..")))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))  # 临时
from app.v1.core.qwq_api import qwq_chat_stream
from app.v1.core.enterprise_knowledge import sse_stream
from app.v1.core.tokens import login_maxkb
from app.v1.settings import Config

# ---------- A. 去除常见扩展名 ---------------------------
_EXT_PATTERN = re.compile(
    r"\.(txt|md|markdown|pdf|docx?|xls|xlsx|csv|html?|zip)$", re.I
)


def _strip_suffix(text: str) -> str:
    return _EXT_PATTERN.sub("", text)


def _clean_paragraph_list(plist: List[Dict[str, Any]]) -> None:
    """原地修改：剥掉 title/name/file_name/doc_name/content 的扩展名"""
    for p in plist:
        for k in ("title", "name", "file_name", "doc_name", "content"):
            if k in p and isinstance(p[k], str):
                p[k] = _strip_suffix(p[k])


# -------------------------------------------------------


def hit_question(query_text: str, dataset_id: str,
                 similarity: float = 0.55, top_number: int = 100,
                 search_mode: str = "blend", kind: bool = True) -> Dict[str, Any]:
    if kind:
        kind_url = Config.maxkb_private_library_url
    else:
        kind_url = Config.maxkb_project_library_url
    url = f"{kind_url}/api/dataset/{dataset_id}/hit_test"
    token = login_maxkb(kind)
    params = {
        "query_text": query_text, "similarity": similarity,
        "top_number": top_number, "search_mode": search_mode
    }
    headers = {
        "Authorization": token,
        "Accept": "application/json, text/plain, */*",
        "User-Agent": "Mozilla/5.0",
        "Referer": f"{kind_url}/ui/dataset/{dataset_id}/hit-test"
    }
    resp = requests.get(url, headers=headers, params=params, timeout=30)
    resp.raise_for_status()
    return resp.json()


def call_my_ai_agent(context_data: str, query_text: str, dataset_id: str, is_assistant: bool) -> Generator[bytes, None, None]:
    """
    用新的 qwq_chat_stream 流式调用，返回 bytes
    """
    content = f"你是立信杏仁助手。以下是背景知识：\n{context_data}\n请结合以上回答问题。"
    if is_assistant:
        res = Project.objects.filter(project_id=dataset_id)
        if res:
            assistant_id = res[0].assistant_id
            prompt = get_assistant(assistant_id)
            if prompt:
                content = f'{prompt[0]}: + \n{context_data}\n请结合以上回答问题。'

    user_messages = [
        {"role": "system", "content": content},
        {"role": "user", "content": query_text}
    ]

    for chunk in qwq_chat_stream(user_messages, backend="online"):  # ★ 如果想用线上vLLM，backend="online"
        yield chunk.encode("utf-8")


def _merge_context(plist: List[Dict[str, Any]]) -> str:
    return "\n".join(
        "【{}】{}".format(i + 1, p.get("content", "").replace("\n", " "))
        for i, p in enumerate(plist)
    )


def get_info(dataset_id: str, query_text: str, kind: bool = True, is_assistant: bool = False) -> Generator[bytes, None, None]:
    """统一入口：yield bytes（便于直接作为 SSE/WebSocket 数据）"""
    # ---- 企业知识库 --------------------------------------------------
    if not dataset_id or dataset_id == "d5fb110a-1f4d-11f0-b0a5-86a404970cbc":
        if not dataset_id:
            dataset_id = "a0880f08-2102-11f0-b213-926528472ef0"
        # 旧逻辑就是把 sse_stream 的产出直接交给前端，
        # 不做任何二次 encode/解码。
        yield from sse_stream(query_text, dataset_id)
        return

    # ---- 项目 / 个人知识库 -------------------------------------------
    try:
        hit_json = hit_question(query_text, dataset_id, kind=kind)
        paragraph_list = hit_json.get("data", [])
    except Exception as e:
        yield f"系统繁忙，获取回复失败".encode()
        return

    if not paragraph_list:
        yield "没有找到和您提问相关的问题，请重新描述您的问题。".encode()
        return

    # —— 在这里给每条段落加上 query 字段 ——
    for p in paragraph_list:
        # 如果想保留已有 query，就判断一下：
        if "query" not in p:
            p["query"] = query_text
        # 如果想强制覆盖也可以直接：
        # p["query"] = query_text

    # 1) 上下文
    context_str = _merge_context(paragraph_list)
    # 2) LLM 流
    if len(context_str) > 60000:
        context_str = context_str[:60000]
    for chunk in call_my_ai_agent(context_str, query_text, dataset_id, is_assistant):
        yield chunk

    # 3) <source> 结尾（字节流 + 换行）
    src_json = json.dumps(paragraph_list, ensure_ascii=False)
    yield f"<source>{src_json}</source>\n".encode("utf-8")


# --------------------------- CLI 测试 -----------------------------
if __name__ == "__main__":
    for b in get_info(
            dataset_id="c4a6e750-2f13-11f0-aab5-b693dda450bf",
            query_text="数据安全管理第二十一条是什么",
            kind=True
    ):
        print(b.decode("utf-8", errors="ignore"), end="")
