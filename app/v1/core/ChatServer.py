# -*- encoding: utf-8 -*-
"""
@File    : ChatServer.py
@Time    : 2023/4/3 17:11
<AUTHOR> Qu ming
"""
import time
from datetime import datetime, date
from app.v1.db import user_info
from app.v1.db.user_info import record_data
from app.v1.core.azure_api import azure_api
from app.v1.core.get_tiktoken import tokens_from_gpt


class ChatServer:
    def __init__(self, user, question):
        self.user = user.lower()
        self.question = question

    def chat_robot(self, chat_style=False):
        """
        提供作者的信息和问题，返回相应的答案
        return: { status_code': 200,
                'content': content
                }
        """
        try:
            content = None
            messages = []
            if not content:
                history_user_data = {"role": "user"}
                history_assistant_data = {"role": "assistant"}
                history_data = self.estimated_time()
                if chat_style and history_data:
                    for data in history_data:
                        history_user_data["content"] = data["question"]
                        history_assistant_data["content"] = data["reply"]
                        messages.append(history_user_data)
                        messages.append(history_assistant_data)
                messages.append({"role": "user", "content": f"{self.question}"})
                content = azure_api(messages)  # azure_api
                result = tokens_from_gpt(self.question, content)
                record_data(
                    self.user,
                    self.question,
                    content,
                    result["prompt_tokens"],
                    result["completion_tokens"],
                    str(result["fee"]),
                )
        except Exception as e:
            content = "gpt暂时无法回答，请稍后尝试."
        return content

    def estimated_time(self):
        """
        当前时间加上5分钟与数据库的时间对比，并得到符合相应的数据
        return: [<User: User object>, ...]
        """
        columns = []
        pause_time = time.time() - 5 * 60
        user_list = user_info.query_user(self.user)["data_list"]
        for obj in user_list:
            if obj["create_time"].timestamp() > pause_time:
                columns.append(obj)
        return columns

    @staticmethod
    def get_date_interval():
        """
        获取当月时间的月初和下个月的第一天
        return: first_day_of_month, first_day_of_next_month
        """
        today_date = datetime.today().date()
        first_day_of_month = datetime(today_date.year, today_date.month, 1)
        year = today_date.year
        month = today_date.month + 1
        if month > 12:
            year += 1
            month = 1
        # 构造下个月的第一天
        first_day_of_next_month = date(year, month, 1)
        return first_day_of_month, first_day_of_next_month


if __name__ == "__main__":
    chat = ChatServer("qu.mingming", "哪吒2现在票房多少")
    print(chat.chat_robot())
