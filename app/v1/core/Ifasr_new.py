#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
讯飞云转写 + MongoD<PERSON> 原子限额控制
------------------------------------------------
依赖: requests pymongo pydub
pydub 需要 ffmpeg 可自行安装
"""
import os, time, hmac, json, base64, hashlib, urllib.parse, requests
from datetime import datetime
from typing import Optional, List, Dict

from pydub import AudioSegment
from pymongo import MongoClient, ASCENDING

from app.v1.settings import Config
import sys  # 临时

# sys.path.append(os.path.abspath(os.path.join(__file__, "..", "..")))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))  # 临时
# ——— 1. 站点 & Mongo 配置 ——————————————————————————
MONGO_URI = Config.mongo_url
MONGO_DB = Config.mongo_database
LFASR_HOST = "https://raasr.xfyun.cn/v2/api"
API_UPLOAD = "/upload"
API_GET_RESULT = "/getResult"

TRANS_COLLECTION = "audio_transcriptions"
USERS_COLLECTION = "users"
CONFIG_COLLECTION = "config"
USAGE_COLLECTION = "audio_usage"  # 月度汇总

DEFAULT_MONTHLY_QUOTA_KEY = "monthly_audio_quota_default"
DEFAULT_MONTHLY_QUOTA_VAL = 180  # 若 config 无设置则用此

# ——— 2. 初始化 MongoClient（全局一个即可） —————————
client_global = MongoClient(MONGO_URI)
db_global = client_global[MONGO_DB]

# 建唯一索引（只建一次）
db_global[USAGE_COLLECTION].create_index(
    [("user_id", ASCENDING), ("month", ASCENDING)],
    unique=True, background=True, name="user_month_unique"
)


# ——— 3. 主类 ————————————————————————————————————————
class RequestApi:
    def __init__(self,
                 appid: str,
                 secret_key: str,
                 upload_file_path: str,
                 user_id: str):
        self.appid = appid
        self.secret_key = secret_key
        self.file_path = upload_file_path
        self.user_id = user_id
        self.ts = str(int(time.time()))
        self.signa = self._make_signa()

    # ---------- 小工具 ----------
    def _make_signa(self) -> str:
        md5 = hashlib.md5((self.appid + self.ts).encode()).hexdigest().encode()
        raw = hmac.new(self.secret_key.encode(), md5, hashlib.sha1).digest()
        return base64.b64encode(raw).decode()

    # ---------- 原子扣额 ----------
    def _reserve_minutes(self, want: int) -> None:
        """并发安全扣额（兼容 Mongo <4.4 无 $expr+upsert）"""
        usage = client_global[MONGO_DB][USAGE_COLLECTION]
        users = client_global[MONGO_DB][USERS_COLLECTION]
        cfgcol = client_global[MONGO_DB][CONFIG_COLLECTION]

        now = datetime.utcnow()
        mflag = datetime(now.year, now.month, 1)

        from pymongo.errors import DuplicateKeyError

        # 1) 取配额
        default_q = int((cfgcol.find_one({"_id": DEFAULT_MONTHLY_QUOTA_KEY}) or {})
                        .get("value", DEFAULT_MONTHLY_QUOTA_VAL))
        quota = int((users.find_one({"_id": self.user_id}) or {})
                    .get("monthly_audio_quota", default_q))

        # 2) 至多重试 3 次
        for _ in range(3):
            # 2-1) 先尝试条件扣减
            res = usage.update_one(
                {"user_id": self.user_id, "month": mflag,
                 "used": {"$lte": quota - want}},
                {"$inc": {"used": want}}
            )
            if res.modified_count == 1:
                return  # 成功扣额

            # 2-2) 若没有文档 → 插入占坑
            try:
                usage.insert_one({
                    "user_id": self.user_id,
                    "month": mflag,
                    "used": want
                })
                return  # 首次插入成功（额度必够）
            except DuplicateKeyError:
                # 刚被别的并发插入，回到 while 再试一次
                continue

        # 3) 三次仍失败 ⇒ 额度不足
        raise RuntimeError("本月语音转写额度不足。")

    # ---------- 回滚配额 ----------
    def _rollback_minutes(self, mins: int):
        db = client_global[MONGO_DB]
        usage = db[USAGE_COLLECTION]
        month_flag = datetime.utcnow().replace(day=1)
        usage.update_one(
            {"user_id": self.user_id, "month": month_flag},
            {"$inc": {"used": -mins}},
            upsert=False
        )

    # ---------- 上传 ----------
    def _upload_audio(self, duration_ms: int) -> dict:
        file_size = os.path.getsize(self.file_path)
        params = {
            "appId": self.appid,
            "signa": self.signa,
            "ts": self.ts,
            "fileSize": file_size,
            "fileName": os.path.basename(self.file_path),
            "duration": str(duration_ms // 1000),
            "roleType": "1",
            "roleNum": "0",
        }
        url = LFASR_HOST + API_UPLOAD + "?" + urllib.parse.urlencode(params)
        with open(self.file_path, "rb") as f:
            resp = requests.post(
                url, headers={"Content-Type": "application/json"},
                data=f.read(), timeout=60
            )
        res = resp.json()
        if res.get("code") != "000000":
            raise RuntimeError(f"上传失败: {res.get('descInfo')}")
        return res

    # ---------- 主入口 ----------
    def get_result(self) -> Optional[str]:
        audio = AudioSegment.from_file(self.file_path)
        duration_ms = len(audio)
        used_minutes = max(1, (duration_ms + 59_999) // 60_000)

        # 扣额
        self._reserve_minutes(used_minutes)

        try:
            order_id = self._upload_audio(duration_ms)["content"]["orderId"]
            text = self._poll_and_parse(order_id)
            self._record_to_db(used_minutes)
            return text
        except Exception:
            # 任意异常回滚额度
            self._rollback_minutes(used_minutes)
            raise

    # ---------- 轮询 ----------
    def _poll_and_parse(self, order_id: str) -> str:
        params = {
            "appId": self.appid, "signa": self.signa, "ts": self.ts,
            "orderId": order_id, "resultType": "transfer,predict",
        }
        url = LFASR_HOST + API_GET_RESULT + "?" + urllib.parse.urlencode(params)

        while True:
            result = requests.post(url, headers={"Content-Type": "application/json"}, timeout=30).json()
            if result.get("code") != "000000":
                raise RuntimeError(result.get("descInfo", "查询失败"))

            order_result = result["content"].get("orderResult")
            if order_result:  # 转写完成
                segs = self._parse_result(result)
                return self._format_segments(segs)

            if result["content"]["orderInfo"]["status"] != 3:
                raise RuntimeError("订单已结束但无结果")
            time.sleep(5)

    # ---------- 解析 ----------
    @staticmethod
    def _parse_result(res_json: dict) -> List[Dict]:
        raw = json.loads(res_json["content"]["orderResult"])
        segs = []
        for lat in raw.get("lattice", []):
            j = lat.get("json_1best")
            if isinstance(j, str):
                j = json.loads(j)
            st = j.get("st", {})
            text = "".join(
                w["w"]
                for rt in st.get("rt", [])
                for ws in rt.get("ws", [])
                for w in ws.get("cw", [])
            )
            if text:
                segs.append({"role": st.get("rl", "未知"), "text": text})
        return segs

    @staticmethod
    def _format_segments(segs: List[Dict]) -> str:
        return "\n".join(f"说话人{seg['role']}：{seg['text']}" for seg in segs)

    # ---------- 写明细 ----------
    def _record_to_db(self, used_minutes: int):
        db = client_global[MONGO_DB]
        db[TRANS_COLLECTION].insert_one({
            "user_id": self.user_id,
            "file_name": os.path.basename(self.file_path),
            "used_minutes": used_minutes,
            "transcribe_date": datetime.utcnow()
        })
        print(f"已记录转写时长：{used_minutes} 分钟")


# ———— CLI 测试 ——————————————————————————————————
if __name__ == "__main__":
    api = RequestApi(
        appid="51988241",
        secret_key="d0942785f647f5917c2041dacd15ef54",
        upload_file_path=r"C:\Users\<USER>\Desktop\output.wav",
        user_id="hu.zijian"
    )
    try:
        txt = api.get_result()
        if txt:
            out = r"C:\Users\<USER>\Desktop\final_output.txt"
            with open(out, "w", encoding="utf-8") as f:
                f.write(txt)
            print("转写完成，已保存：", out)
    except RuntimeError as e:
        print("错误：", e)
