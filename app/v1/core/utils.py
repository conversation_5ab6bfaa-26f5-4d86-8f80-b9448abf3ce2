# -*- encoding: utf-8 -*-
"""
@File    : utils.py
@Time    : 2023/4/17 16:12
<AUTHOR>  ming
"""

import os
from datetime import datetime, timedelta
import uuid
import socket
from typing import Union

import jwt
import redis

from app.v1.settings import Config


def limit_data(data_list: list,
               page: int = None,
               page_size: int = None):
    """
    对数据进行切片
    """
    if page and page_size:
        start = (page - 1) * page_size
        end = start + page_size
        data_list = data_list[start:end]
    return data_list


def sava_content(path, data):
    """
    将数据存储到某个目录下
    """
    with open(path, 'wb') as f:
        f.write(data)


def check_file(path):
    """
    检验文件是否存在
    """
    if os.path.exists(path):
        return True


def get_uuid():
    """
    根据根据当前的时间戳和MAC地址生成的生成的uuid
    """
    return str(uuid.uuid1())


def get_redis_conn():
    """
    获取redis连接
    """
    pool = redis.ConnectionPool(host=Config.redis_host, password=Config.redis_password, decode_responses=True,
                                db=Config.redis_db)
    rd = redis.Redis(connection_pool=pool)
    return rd


def delete_topic_redis(user_id):
    get_redis_conn().delete(user_id)


def get_dir(is_private: bool, username: str, project_name: str):
    if is_private:
        file_dir = os.path.join(Config.txt_storage_path, username, project_name)
    else:
        file_dir = os.path.join(Config.txt_storage_path, 'project', project_name)
    return file_dir


def judge_product():
    """
    判断系统是测试还是生产环境
    """
    hostname = socket.gethostname()
    ip = socket.gethostbyname(hostname)
    if '172.17' in ip:
        return True


def get_directory_size(path):
    """
    计算单个目录占用磁盘空间
    """
    total_size = 0
    for dirpath, dir_names, filenames in os.walk(path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            total_size += os.path.getsize(fp)
    return total_size / (1024 * 1024)


def remove_audio_txt(file_list: list):
    """
    移除txt，保留录音文件
    """
    audio_files = {file.split('.')[0] for file in file_list if is_audio_file(file)}
    return [file for file in file_list if not (file.endswith('.txt') and file.split('.')[0] in audio_files)]


def is_audio_file(filename: str) -> bool:
    """
    判断文件是否为音频文件
    """
    extension = filename.split('.')[-1].lower()
    return extension in ['amr', 'wav', 'mp3', 'mp4', 'm4a', 'aac', 'ogg']  # 可根据需要再加


def is_special_file(filename: str) -> bool:
    """
    判断文件是否为pdf，xlsx，xls，CSV
    """
    extension = filename.split('.')[-1].lower()
    return extension in ['pdf', 'xlsx', 'xls', 'csv']  # 可根据需要再加


def get_extension(file_path: str):
    """
    获取文件后缀
    """
    EXT_CATEGORY_MAP = {
        **{e: "word文档" for e in (".doc", ".docx", ".dot", ".rtf")},
        **{e: "PPT文档" for e in (".ppt", ".pptx", ".pps", ".pot")},
        **{e: "Excel文档" for e in (".xls", ".xlsx", ".xlsm", ".csv")},
        **{e: "PDF文档" for e in (".pdf", ".ofd")},
        **{e: "图片" for e in (".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp")},
        **{e: "音视频" for e in (".mp3", ".wav", ".aac", ".flac", ".mp4", ".mov", ".avi", ".mkv", ".amr")},
        **{e: "压缩包" for e in (".zip", ".rar", ".7z", ".tar", ".gz")},
        **{e: "TXT文档" for e in (".txt",)},
    }

    ex = os.path.splitext(file_path)[1].lower()
    return EXT_CATEGORY_MAP.get(ex, '其他')


def create_token(user_id: str) -> str:
    """
        根据外部认证信息创建JWT token
        :param user_id: 用户ID
        :param user_info: 用户信息字典
        :return: JWT token
        """
    JWT_SECRET_KEY = 'your-secret-key-here'
    JWT_ALGORITHM = 'HS256'
    try:
        payload = {
            "user_id": user_id,
            "user_info": {},
            "exp": datetime.now() + timedelta(days=1)
        }

        token = jwt.encode(
            payload,
            JWT_SECRET_KEY,
            algorithm=JWT_ALGORITHM
        )
        return token
    except:
        pass


def str_replace(data: Union[list, str], speakers: list):
    if data:
        name_dict = {item['username']: item['newName'] for item in speakers}
        if isinstance(data, list):
            for dialog in data:
                speaker = dialog.get('speaker', '')
                if speaker in name_dict:
                    dialog['speaker'] = name_dict[speaker]
        else:
            for old_name, new_name in name_dict.items():
                data = data.replace(old_name, new_name)
        return data


def format_size(size_bytes):
    if size_bytes < 1048576:
        return f"{size_bytes / 1024:.1f}KB"
    else:
        return f"{size_bytes / 1048576:.1f}MB"


def is_supported_document(filename: str) -> bool:
    """
    判断文件名是否为 PDF、Word 或 Excel 类型

    支持的扩展名：
    - PDF: .pdf
    - Word: .doc, .docx
    - Excel: .xls, .xlsx

    Args:
        filename (str): 文件名（可以包含路径）

    Returns:
        bool: 是否为支持的文档类型
    """
    supported_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt']
    filename = filename.lower()
    return any(filename.endswith(ext) for ext in supported_extensions)


if __name__ == '__main__':
    # print(get_functions('qu.mingming'))
    # print(get_directory_size(r'D:\chatgpt\trunk\xxr-chatgpt-app'))
    print(is_special_file('1.PDF'))
