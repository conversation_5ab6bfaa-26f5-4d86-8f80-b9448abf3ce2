# -*- encoding: utf-8 -*-
"""
@File    : chat_server.py
@Time    : 2023/4/19 13:53
<AUTHOR> qu ming
"""

from fastapi import APIRouter, Header
from fastapi import Body
from fastapi.responses import JSONResponse
from app.v1.core.embeddings_api import get_embeddings
from app.v1.core.tokens import decode_token
from app.v1.core.ChatServer import ChatServer

router = APIRouter()


@router.post("/api/ChatServer", tags=['聊天服务'])
async def chat_server(Authorization: str = Header(None),
                      question: str = Body(None),
                      robot_name: str = Body(None),
                      chat_style: bool = Body(False),
                      ):
    """
    提供聊天服务： 保存访问次数及问题，
    生成图片优先级最高，默认为 false

    return: {
        "status_code": 200,
        'msgType'： text
        'content':{
                "source":[土地法,土地改革法]，
                "reply": '土地属于国家所有'
        }
    }
    """
    username = decode_token(Authorization)
    if not username:
        return JSONResponse({'status_code': 403, 'content': '无权限访问'})
    msgType = 'text'
    status_code = 200
    if robot_name == 'financial_data':
        content = get_embeddings(Authorization, robot_name, question)
    else:
        chat = ChatServer(username, question)
        res = chat.chat_robot(chat_style)
        content = {"reply": res, 'sources': ['立信AI中心']}
    data = {'status_code': status_code, 'msgType': msgType, 'content': content}
    return JSONResponse(data)
