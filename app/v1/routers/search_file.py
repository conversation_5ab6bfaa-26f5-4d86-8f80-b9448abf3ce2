# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/17 15:26
@Auth ： qu ming
@File ：search_file.py
@IDE ：PyCharm
"""

import os

from fastapi import APIRouter, Header, Body
from fastapi.responses import JSONResponse

from typing import Optional
from app.v1.core.tokens import decode_token
from app.v1.core.utils import get_dir

router = APIRouter()


@router.post("/api/search_file", tags=['ai中心'])
async def search_file(Authorization: Optional[str] = Header(None),
                      project_name: str = Body(None),
                      file_name: str = Body(None),
                      is_private: bool = Body(None)):
    """
    选定文件，上传相关文件内容
    """
    username = decode_token(Authorization)
    if not username:
        result = {'status': 403, 'content': '无权限访问'}
        return JSONResponse(result)
    file_dir = get_dir(is_private, username, project_name)
    os.makedirs(file_dir, exist_ok=True)
    file_names = [i for i in os.listdir(file_dir) if file_name in i]
    result = {'status_code': 200, 'content': file_names}
    return JSONResponse(result)
