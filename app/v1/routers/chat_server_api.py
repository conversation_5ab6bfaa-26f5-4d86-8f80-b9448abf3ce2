# -*- encoding: utf-8 -*-
"""
@File    : chat_server_api.py
@Time    : 2023/4/19 13:53
<AUTHOR> qu ming yan.ziwei
"""

from fastapi import APIRouter, Body, Header, Request, Depends
from fastapi.responses import StreamingResponse
from typing import Union
from ai_assistant.services.assistant_service import AssistantService
from app.core.llm.session.general_chat.chat_session import GeneralChatSessionManager
from app.v1.models.UserProject import Project
from auth_permission.core.dependencies import AuthPermission
from auth_permission.schemas.system import Auth
from app.v1.core.directknowleg_hit import get_info
from app.v1.core.net_api import NetServer
from app.v1.core.process_speech import get_assistant
from app.v1.core.tokens import decode_token

# 引入你改造好的流式函数
from app.v1.core.qwq_api import qwq_chat_stream

from app.v1.core.trans_embeddings import Qa

router = APIRouter()


@router.post("/api/chat")
async def chat_server(
        assistant_id: Union[str, int] = Body(''),
        Authorization: str = Header(None),
        question: str = Body(None),
        search_type: str = Body(None),
        collection_name: str = Body(None),
        is_private: bool = Body(True),
        project_id: str = Body(None),
        session_id: str = Body(None)
        #     auth: Auth = Depends(AuthPermission(permissions=["system:user:query"]))
):
    """
    提供聊天服务：支持联网搜索 / QA 文件 / 项目知识库 / 模型对话
    """
    username = decode_token(Authorization)
    if not username:
        # 返回 SSE 格式的“无权访问”
        return StreamingResponse((i for i in "无权访问"), media_type='text/event-stream')

    # 初始化会话管理器
    session_manager = GeneralChatSessionManager()

    # 检查会话是否存在
    if not session_id or not session_manager.session_exists(session_id):
        return StreamingResponse((i for i in "🧠 会话无效，建议刷新或重试"), media_type="text/event-stream")

    # 处理助手相关逻辑
    assistant_prompt = None
    if assistant_id:
        prompt = get_assistant(str(assistant_id))
        if prompt:
            assistant_prompt = prompt[0]

        # 检查是否有关联的项目
        res = Project.objects.filter(assistant_id=assistant_id)
        if res and assistant_id != 1:
            project_id = res[0].project_id
            search_type = 'inner'

    # QA 功能保持独立处理（因为有特殊的 collection_name 逻辑）
    if search_type == 'qa' and collection_name:
        qa = Qa()
        return StreamingResponse(qa.chat_content(question, collection_name, username), media_type='text/event-stream')

    # 其他所有功能统一使用 GeneralChatSessionManager 处理
    # 判断功能类型
    use_web = search_type == "online"
    use_enterprise_qa = search_type == "inner"

    # 使用会话管理器进行流式对话
    async def event_generator():
        async for chunk in session_manager.chat_stream_ask(
            session_id=session_id,
            question=question,
            dataset_id=project_id,
            use_web=use_web,
            use_enterprise_qa=use_enterprise_qa,
            is_private=is_private,
            assistant_prompt=assistant_prompt,
        ):
            yield chunk

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
    )
