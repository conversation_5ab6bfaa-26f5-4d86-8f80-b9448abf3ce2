# -*- encoding: utf-8 -*-
"""
@File    : __init__.py.py
@Time    : 2023/4/19 13:51
<AUTHOR> qu ming
"""


from fastapi import FastAPI
from wps_callback_kit.routing.fastapi_routes import register_fastapi_routes
from auth_permission.api.system import dept, log, menu, position, role, user
from ai_assistant.api import prompt, assistant

from app.v1.routers import (
    chat_server,
    check_user,
    check_identity,
    chat_server_api,
    building_knowledge,
    get_knowledge,
    save_file,
    select_file,
    delete_knowledge,
    get_permission,
    delete_file,
    search_file,
    file_details,
    save_record,
    process_speech,
    upload_qa,
    judge_sacp,
    project_file,
    history,
    private_Info,
    check_space,
    query_private,
    check_speech,
    permission,
    financial_analysis,
    update_speaker,
)


router_list = [
    chat_server.router,
    check_user.router,
    check_identity.router,
    chat_server_api.router,
    building_knowledge.router,
    get_knowledge.router,
    save_file.router,
    select_file.router,
    delete_knowledge.router,
    get_permission.router,
    delete_file.router,
    search_file.router,
    file_details.router,
    save_record.router,
    process_speech.router,
    upload_qa.router,
    private_Info.router,
    query_private.router,
    financial_analysis.router,
    judge_sacp.router,
    project_file.router,
    history.router,
    check_speech.router,
    permission.router,
    check_space.router,
    dept.router,
    log.router,
    menu.router,
    role.router,
    position.router,
    user.router,
    update_speaker.router,
    prompt.router,
    assistant.router,
]


def register_v1_routes(app: FastAPI):
    """
    注册v1版本的路由
    Args:
        app:

    Returns:

    """
    register_fastapi_routes(app)
    for router in router_list:
        app.include_router(router)


__all__ = ["register_v1_routes"]
