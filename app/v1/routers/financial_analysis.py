# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/22 11:29
@Auth ： qu ming
@File ：financial_analysis.py
@IDE ：PyCharm
"""

from fastapi import APIRouter, Header
from fastapi import Body
from fastapi.responses import JSONResponse
from app.v1.core.tokens import decode_token, get_financial_analysis

router = APIRouter()


@router.post("/api/financial_analysis", tags=['聊天服务'])
async def chat_server(Authorization: str = Header(None),
                      question: str = Body(None),
                      analysis_type: str = Body(None)
                      ):
    """
    财务分析问答
    return: {
        "status_code": 200,
        'content': xxxx
    """
    username = decode_token(Authorization)
    if not username:
        return JSONResponse({'status_code': 403, 'content': '无权限访问'})
    content = get_financial_analysis(question, analysis_type, username)
    if analysis_type == 'analyseComprehensive':
        new_dict = {}
        if content and content.get('data'):
            new_dict['data'] = content['data']
            data = {"sentence": content.get('comprehensive', ''), 'data': new_dict}
            res = {'status': True, "data_type": 'financial_report', "reply": data, "source": []}

        else:
            res = '公司数据不存在'
    elif analysis_type == 'analyseRisk':
        new_dict = {}
        if content:
            new_dict['data'] = content
            data = {"sentence": '', 'data': new_dict}
            res = {'status': True, "data_type": 'financial_report', "reply": data, "source": []}
        else:
            res = '公司数据不存在'
    else:
        if not content:
            content = '公司数据不存在'
        res = content
    data = {'status_code': 200, 'msgType': 'text', 'content': res}
    return JSONResponse(data)
