# -*- encoding: utf-8 -*-
"""
@File    : private_Info.py
@Time    : 2023/4/19 15:42
<AUTHOR> qu ming
"""

from fastapi import APIRouter, Header, Query
from fastapi.responses import JSONResponse
from app.v1.db import user_info
from app.v1.core.tokens import decode_token

router = APIRouter()


@router.get("/api/GetUserData", tags=['数据查询'])
def get_user_data(Authorization: str = Header(None),
                  robot_name: str = Query(None),
                  content_type: str = Query(None),
                  assistant_id: str = Query(None),
                  project_id: str = Query(None)):
    """
    获取用户的访问记录

    return: {
        "status_code": 200
        'content': [{
      "_id": "642e64084225640a0c1aa06d",
      "name": "cyl",
      "prompt": "怎样变的有钱",
      "reply": "学习和积累财务知识"，
      "create_time": "2023-04-21 10:26:08“
    }]
    }
    """
    username = decode_token(Authorization)
    if not username:
        result = {'status': 403, 'content': '无权限访问'}
        return JSONResponse(result)
    query_params = {
        'user': username,
        'robot_name': robot_name,
        'content_type': content_type,
        'is_sort': False,
    }
    query_params.update({k: v for k, v in [('project_id', project_id), ('assistant_id', assistant_id)] if v})
    data_list = user_info.query_user(**query_params)['data_list']
    for data in data_list:
        if '_id' in data.keys():
            data['_id'] = str(data['_id'])
            data['create_time'] = data['create_time'].strftime("%Y-%m-%d %H:%M:%S")
    result = {'status_code': 200, 'content': data_list[-30:]}
    return JSONResponse(result)
