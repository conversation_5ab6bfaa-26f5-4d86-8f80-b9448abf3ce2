# -*- encoding: utf-8 -*-
"""
@File    : query_private.py
@Time    : 2023/4/19 15:42
<AUTHOR> qu ming
"""

from fastapi import APIRouter, Header, Query
from fastapi.responses import JSONResponse
from app.v1.core.tokens import get_private
from app.v1.core.tokens import decode_token

router = APIRouter()


@router.get("/api/GetPrivate", tags=['数据查询'])
def get_user_data(Authorization: str = Header(None),
                  username: str = Query(None)):
    """
    获取用户的访问记录

    return: {
        "status_code": 200
        'content': [{
      "bornDate": "1995-01-10 00:00:00",
    "deptId": 2186,
    "diploma": "武汉商学院",
    "educational": "本科",
    "email": "<EMAIL>",
    "hrDepart": "信息技术部",
    "hrDeptCode018": "信息科技发展部",
    "hrDeptCode019": "序伦上海",
    "hrId": 840729,
    "hrLoginId": "qu.mingming",
    "isActive": "在职",
    "jobName": "",
    "mobilePhone": "13476253402",
    "rank": "高级程序员1级",
    "sex": "男",
    "telephone": "",
    "userId": 153041,
    "userName": "瞿明明",
    "userPhoto": "840729.jpg",
    "userType": 0
    }]
    }
    """
    if not decode_token(Authorization):
        result = {'status': 403, 'content': '无权限访问'}
        return JSONResponse(result)
    content = get_private(username)
    result = {'status_code': 200, 'content': content}
    return JSONResponse(result)
