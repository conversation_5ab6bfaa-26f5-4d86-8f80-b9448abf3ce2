# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/31 9:28
@Auth ： qu ming
@File ：upload_.py
@IDE ：PyCharm
"""
import os

from fastapi import APIRouter, Header, UploadFile, File
from fastapi.responses import JSONResponse
from app.v1.core.trans_embeddings import Qa
from app.v1.core.utils import sava_content, get_uuid
from typing import Optional
from fastapi.requests import Request
from app.v1.core.tokens import decode_token
from wps_callback_kit import wps
from app.v1.settings import Config

router = APIRouter()


@router.post("/api/qa", tags=['数据问答'])
async def upload_file(request: Request,
                      Authorization: Optional[str] = Header(None),
                      file: UploadFile = File(...)):
    """
    根据上传文件，支持doc,docx,txt,pdf，excel
    """
    username = decode_token(Authorization)
    if not username:
        result = {'status': 403, 'content': '无权限访问'}
        return JSONResponse(result)
    try:
        file_size = request.headers.get("content-length")
        file_size_mb = int(file_size) / (1024 * 1024)
        # 检查文件大小是否超过100MB
        if file_size_mb > 100:
            result = {'status_code': 200, 'content': '文件限制为100MB以內'}
            return JSONResponse(result)
        file_dir = os.path.join(Config.txt_storage_path, f'user_files/{username}')
        os.makedirs(file_dir, exist_ok=True)
        file_path = os.path.join(file_dir, file.filename)
        data = await file.read()
        sava_content(file_path, data)
        # collection_name = Qa().vector_data(file_path)
        try:
            wps_url = wps.run(get_uuid(), file_path, username)
        except:
            wps_url = ''
        result = {'status_code': 200, 'content': {'collection_name': file.filename, 'url': wps_url}}
    except:
        result = {'status_code': 500, 'content': '无法解析'}
    return JSONResponse(result)
