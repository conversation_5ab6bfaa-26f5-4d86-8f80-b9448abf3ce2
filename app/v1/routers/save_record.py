# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/1 11:15
@Auth ： qu ming
@File ：save_record.py
@IDE ：PyCharm
"""

from fastapi import APIRouter, Header, Body
from fastapi.responses import JSONResponse
from typing import Optional, Union

from app.v1.core.get_tiktoken import tokens_from_model
from app.v1.core.tokens import decode_token
from app.v1.db.user_info import record_data

router = APIRouter()


@router.post("/api/records", tags=['ai中心'])
async def records(assistant_id: Union[str, int] = Body(''),
                  Authorization: Optional[str] = Header(None),
                  type: str = Body(None),
                  reply: str = Body(None),
                  question: str = Body(None),
                  source: str = Body(None),
                  project_id: str = Body(None)
                  ):
    """
    保存历史记录
    """
    username = decode_token(Authorization)
    if not username:
        result = {'status': 403, 'content': '无权限访问'}
        return JSONResponse(result)
    res = tokens_from_model(question, reply)
    kwarg = {'user':username,'question':question,'reply':reply,'prompt_tokens':res['prompt_tokens'],'completion_tokens':res['completion_tokens'],'fee':'','type':type,
             'model':'deepseek','source':source
    }
    if assistant_id:
        kwarg['assistant_id'] = str(assistant_id)
    if project_id:
        kwarg['project_id'] = project_id
    record_data(**kwarg)
    result = {'status_code': 200, 'content': '保存成功'}
    return JSONResponse(result)
