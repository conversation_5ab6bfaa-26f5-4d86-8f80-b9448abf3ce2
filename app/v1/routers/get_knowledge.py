# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/11 15:30
@Auth ： qu ming
@File ：get_knowledge.py
@IDE ：PyCharm
"""
import re

from fastapi import APIRouter, Header, Body
from pypinyin import lazy_pinyin
from starlette.responses import JSONResponse

from app.core.db.manager import DBManager
from app.core.db.models import ParseTask
from app.v1.core.tokens import decode_token
from app.v1.core.upload import document_info
from app.v1.core.utils import get_directory_size, is_special_file, format_size, is_supported_document
from app.v1.db.permission_info import get_project
from app.v1.db.user_project import query_project
from app.v1.models.FileInfo import File
from app.v1.models.UserProject import Project

router = APIRouter()


def extract_extension_letters(extension: str) -> str:
    if not extension:
        return ''
    return ''.join(re.findall(r'[a-zA-Z]+', extension)).upper()


@router.get("/api/get_knowledge_list", tags=['ai中心'])
async def get_knowledge_list(Authorization: str = Header(None),
                             is_private: bool = True,
                             is_doc: bool = False
):
    """
    获取知识库

    return: {
        "status_code": 200
        content": [xxx,xxx,xx,xx]
    """

    username = decode_token(Authorization)
    if not username:
        return JSONResponse({'status_code': 403, 'content': '无权限访问'})
    result = [i for i in query_project(username, is_private)]
    if not is_private:
        result.extend(get_project(username))
    res = []
    for i in result:
        try:
            res_dict = {'project_name': i.to_dict()['project_name'], 'project_id': i.to_dict()['project_id'],
                        'use_free': get_directory_size(i.to_dict()['file_dir'])}
            if i.user != username:
                res_dict['project_type'] = '3'
            else:
                res_dict['project_type'] = '2'
            files = File.objects.filter(name=username, project_id=i.project_id)
            if is_doc:
                files = [i for i in files if is_supported_document(i.file_name)]
            res_dict['file_count'] = len(files)
            res_dict["create_time"] = i.to_dict()['create_time']
            res.append(res_dict)
        except:
            pass
    return JSONResponse({'status_code': 200, 'content': res})


@router.post("/api/get_knowledge", tags=['ai中心'])
async def get_knowledge(
        Authorization: str = Header(None),
        is_private: bool = Body(True),
        project_id: str = Body(None),
        size_sort: bool = Body(False),
        time_sort: bool = Body(False),
        name_key_word: str = Body(None),
        extension_key_word: str = Body(None),
        name_sort: bool = Body(False),
        is_doc: bool = Body(False)
):
    """
    获取知识库

    return: {
        "status_code": 200
        content": [
            {
              "username": "qu.mingming",
              "project_name": "1234511226",
              "project_id": "292bc49c-fee6-11ef-a7f0-065600bcd0b2",
              "project_introduce": "123457899000",
              "file_dir": "D:\\traing_law\\files\\qu.mingming/1234511226",
              "file_names": [
                "醒悟.txt"
              ],
              "create_time": "2025-03-12 10:03:16"
            }
        ]
    }
    """
    username = decode_token(Authorization)
    if not username:
        return {'status_code': 403, 'content': '无权限访问'}

    sort_mapping = {
        'time': '-create_time',
    }
    sort_field = sort_mapping.get('time' if time_sort else None)
    # 构建查询
    query = File.objects.filter(name=username, project_id=project_id)
    if name_key_word:
        query = query.filter(file_name__icontains=name_key_word)

    if extension_key_word:
        query = query.filter(filename_extension__iregex=f"{extension_key_word}$")
    obj = query.order_by(sort_field) if sort_field else query
    if size_sort:
        obj = sorted(obj, key=lambda x: 0 if not x.size else x.size, reverse=True)
    pro = Project.objects.filter(project_id=project_id)
    data_list = []
    data_ict = {}
    with DBManager.get_mysql().session() as db:
        for i in obj:
            if is_doc and not is_supported_document(i.file_name):
                continue
            flag = 0

            # 优先判断解析状态
            if i.file_id:
                project_info = document_info(project_id, is_private)
                project_info_status = project_info.get('data')
                if project_info_status:
                    for p in project_info_status:
                        if i.file_id == p['id']:
                            if is_special_file(i.file_name):
                                if p.get('status_meta') and p['status_meta'].get('aggs') and \
                                        p['status_meta']['aggs'][0]['status'] == 'nn2':
                                    flag = 1
                            else:
                                if p.get('status') == 'nn2':
                                    flag = 1
            else:
                # 兼容旧逻辑：没有 file_id 的判断失败任务
                failed_tasks = db.query(ParseTask).filter(
                    ParseTask.almond_parser_id == i.almond_parser_id,
                    ParseTask.maxkb_id == project_id,
                    ParseTask.status == "error"
                ).first()
                if failed_tasks:
                    flag = 2

            data_list.append({
                'id': str(i.id),
                'is_study': i.is_study,
                'file_name': i.file_name,
                'wps_url': i.url,
                'file_id': i.file_id,
                'create_name': i.name,
                'source': i.source,
                'flag': flag,
                'size': format_size(int(i.size)) if i.size else 0,
                'abstract': i.abstract,
                'create_time': i.create_time.strftime("%Y-%m-%d %H:%M:%S"),
                'file_format': f"{extract_extension_letters(i.filename_extension)}"
            })

    if name_sort:
        data_list = sorted(data_list, key=lambda x: lazy_pinyin(x['file_name'])[0][0])
    data_ict['status'] = data_list

    if len(pro) == 1:
        res = pro[0].to_dict()
        data_ict.update({
            'project_name': res['project_name'],
            'project_introduce': res['project_introduce'],
            'create_time': res['create_time'],
            'is_private': res['is_private'],
            'username': res['username'],
            'use_free': get_directory_size(res['file_dir'])
        })

    result = {'status_code': 200, 'content': data_ict}
    return JSONResponse(result)
