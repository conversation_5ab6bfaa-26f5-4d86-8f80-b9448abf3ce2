# -*- encoding: utf-8 -*-
"""
@File   :history.py
@Time   :2025/4/8 19:41
<AUTHOR>
"""
from fastapi import APIRouter, Query, Header, Body

from app.v1.core.tokens import decode_token
from app.v1.db.user_info import get_user_history, delete_user_history

router = APIRouter(prefix="/api/history", tags=["历史记录"])


@router.get("/list")
async def history(
        Authorization: str = Header(None),
        page: int = Query(1, ge=1),
        page_size: int = Query(10, ge=1, le=100)
):
    """
    获取用户历史记录（分页 + 模糊搜索 + 时间过滤）
    """
    username = decode_token(Authorization)
    if not username:
        return {'status_code': 403, 'content': '无权限访问'}
    history_list = get_user_history(username, page, page_size)
    return {"content": history_list, "status_code": 200}


@router.post("/delete")
def delete_history(Authorization: str = Header(None),
                   file_type: str = Body(None),
                   username: str = Body(None)):
    """
    一键删除用户的所有历史记录
    """
    username = decode_token(Authorization)
    if not username:
        return {'status_code': 403, 'content': '无权限访问'}

    result = delete_user_history(username,file_type)
    if not result:
        return {'status_code': 200, 'content': '删除0个记录'}
    return {'status_code': 200, 'content': f'删除 {result}个记录'}
