# -*- coding: utf-8 -*-
"""
语音转写用量查询接口
"""
from datetime import datetime, date
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, Query, HTTPException
from pydantic import BaseModel
from pymongo import MongoClient

from app.v1.settings import Config
# -*- coding: utf-8 -*-
"""
语音转写用量查询接口
"""
from datetime import datetime, date
from typing import List

from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel
from pymongo import MongoClient
from app.v1.settings import Config

# ----------------- Mongo 配置 -----------------
MONGO_URI = Config.mongo_url
MONGO_DB = Config.mongo_database
TRANS_COLLECTION = "audio_transcriptions"
USERS_COLLECTION = "users"
CONFIG_COLLECTION = "config"  # 全局默认限额存放处


# ----------------- Pydantic 模型 -----------------
class Record(BaseModel):
    file_name: str
    used_minutes: int
    transcribe_date: date


class UsageResponse(BaseModel):
    user_id: str
    used_minutes: int
    monthly_quota: int
    remaining_minutes: int
    records: List[Record]


class UsageWrapper(BaseModel):
    status_code: int = 200
    content: UsageResponse


# ----------------- FastAPI 路由 -----------------
router = APIRouter(
    prefix="",  # 如需统一前缀可在此设置
    tags=["语音用量接口"],
)


# ----------------- Mongo 依赖 -----------------
def get_db():
    client = MongoClient(MONGO_URI)
    db = client[MONGO_DB]
    try:
        yield db
    finally:
        client.close()


# ----------------- 主接口 -----------------
@router.get(
    "/api/usage",
    response_model=UsageWrapper,
    summary="获取用户当月语音转写用量及明细"
)
def get_usage(
        user_id: str = Query(..., title="用户ID", description="要查询的用户 ID"),
        db=Depends(get_db)
):
    """
    示例：`GET /api/usage?user_id=alice`
    返回结构：
    ```json
    {
      "status_code": 200,
      "content": {
        "user_id": "alice",
        "used_minutes": 53,
        "monthly_quota": 180,
        "remaining_minutes": 127,
        "records": [...]
      }
    }
    ```
    """
    # 1. 计算当月范围
    today = datetime.today()
    month_start = datetime(today.year, today.month, 1)

    coll = db[TRANS_COLLECTION]

    # 2. 聚合统计已用分钟
    pipeline = [
        {"$match": {
            "user_id": user_id,
            "transcribe_date": {"$gte": month_start, "$lt": today}
        }},
        {"$group": {"_id": None, "total": {"$sum": "$used_minutes"}}}
    ]
    agg = list(coll.aggregate(pipeline))
    used = agg[0]["total"] if agg else 0

    # 3. 获取配额：优先用户自定义 → 全局默认
    users_coll = db[USERS_COLLECTION]
    config_coll = db[CONFIG_COLLECTION]

    cfg_doc = config_coll.find_one({"_id": "monthly_audio_quota_default"})
    default_quota = int(cfg_doc.get("value")) \
        if cfg_doc and isinstance(cfg_doc.get("value"), (int, float)) else 180

    user_doc = users_coll.find_one({"_id": user_id})
    quota = user_doc.get("monthly_audio_quota", default_quota) if user_doc else default_quota

    # 4. 查询当月明细
    raw_recs = coll.find(
        {"user_id": user_id, "transcribe_date": {"$gte": month_start, "$lt": today}},
        {"_id": 0, "file_name": 1, "used_minutes": 1, "transcribe_date": 1},
    ).sort("transcribe_date", -1)

    records: List[Record] = []
    for r in raw_recs:
        ts = r.get("transcribe_date")
        if not ts:
            continue
        records.append(
            Record(
                file_name=r["file_name"],
                used_minutes=r["used_minutes"],
                transcribe_date=(ts.date() if isinstance(ts, datetime) else ts)
            )
        )

    remaining = max(0, quota - used)

    return UsageWrapper(
        status_code=200,
        content=UsageResponse(
            user_id=user_id,
            used_minutes=used,
            monthly_quota=quota,
            remaining_minutes=remaining,
            records=records
        )
    )

# ----------------- Mongo 配置 -----------------
MONGO_URI = Config.mongo_url
MONGO_DB = Config.mongo_database
TRANS_COLLECTION = "audio_transcriptions"
USERS_COLLECTION = "users"
CONFIG_COLLECTION = "config"  # 全局默认限额存放处


# ----------------- Pydantic 模型 -----------------
class Record(BaseModel):
    file_name: str
    used_minutes: int
    transcribe_date: date


class UsageResponse(BaseModel):
    user_id: str
    used_minutes: int
    monthly_quota: int
    remaining_minutes: int
    records: List[Record]


class UsageWrapper(BaseModel):
    status_code: int = 200
    content: UsageResponse


# ----------------- FastAPI 路由 -----------------
router = APIRouter(
    prefix="",  # 如需统一前缀可在此设置
    tags=["语音用量接口"],
)


# ----------------- Mongo 依赖 -----------------
def get_db():
    client = MongoClient(MONGO_URI)
    db = client[MONGO_DB]
    try:
        yield db
    finally:
        client.close()


# ----------------- 主接口 -----------------
@router.get(
    "/api/usage",
    response_model=UsageWrapper,
    summary="获取用户当月语音转写用量及明细"
)
def get_usage(
        user_id: str = Query(..., title="用户ID", description="要查询的用户 ID"),
        db=Depends(get_db)
):
    """
    示例：`GET /api/usage?user_id=alice`
    返回结构：
    ```json
    {
      "status_code": 200,
      "content": {
        "user_id": "alice",
        "used_minutes": 53,
        "monthly_quota": 180,
        "remaining_minutes": 127,
        "records": [...]
      }
    }
    ```
    """
    # 1. 计算当月范围
    today = datetime.today()
    month_start = datetime(today.year, today.month, 1)

    coll = db[TRANS_COLLECTION]

    # 2. 聚合统计已用分钟
    pipeline = [
        {"$match": {
            "user_id": user_id,
            "transcribe_date": {"$gte": month_start, "$lt": today}
        }},
        {"$group": {"_id": None, "total": {"$sum": "$used_minutes"}}}
    ]
    agg = list(coll.aggregate(pipeline))
    used = agg[0]["total"] if agg else 0

    # 3. 获取配额：优先用户自定义 → 全局默认
    users_coll = db[USERS_COLLECTION]
    config_coll = db[CONFIG_COLLECTION]

    cfg_doc = config_coll.find_one({"_id": "monthly_audio_quota_default"})
    default_quota = int(cfg_doc.get("value")) \
        if cfg_doc and isinstance(cfg_doc.get("value"), (int, float)) else 180

    user_doc = users_coll.find_one({"_id": user_id})
    quota = user_doc.get("monthly_audio_quota", default_quota) if user_doc else default_quota

    # 4. 查询当月明细
    raw_recs = coll.find(
        {"user_id": user_id, "transcribe_date": {"$gte": month_start, "$lt": today}},
        {"_id": 0, "file_name": 1, "used_minutes": 1, "transcribe_date": 1},
    ).sort("transcribe_date", -1)

    records: List[Record] = []
    for r in raw_recs:
        ts = r.get("transcribe_date")
        if not ts:
            continue
        records.append(
            Record(
                file_name=r["file_name"],
                used_minutes=r["used_minutes"],
                transcribe_date=(ts.date() if isinstance(ts, datetime) else ts)
            )
        )

    remaining = max(0, quota - used)

    return UsageWrapper(
        status_code=200,
        content=UsageResponse(
            user_id=user_id,
            used_minutes=used,
            monthly_quota=quota,
            remaining_minutes=remaining,
            records=records
        )
    )
