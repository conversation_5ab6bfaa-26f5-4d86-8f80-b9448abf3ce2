# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/11 15:23
@Auth ： qu ming
@File ：building_knowledge.py
@IDE ：PyCharm
"""
import os

from fastapi import APIRouter, Header
from fastapi import Body
from starlette.responses import JSONResponse
from app.v1.core.tokens import decode_token, get_project_id, get_sacp_list
from app.v1.core.utils import get_dir
from app.v1.db.permission_info import save_permission
from app.v1.db.user_project import save_project
from app.v1.models.UserProject import Project

router = APIRouter()


@router.post("/api/building_knowledge", tags=['ai中心'])
async def building_knowledge(Authorization: str = Header(None),
                             project_name: str = Body(None),
                             project_introduce: str = Body(None),
                             is_private: bool = Body(True)):
    """
    提供聊天服务： 保存访问次数及问题


    return: {
        "status_code": 200
        'content': "创建成功"
        'project_id': 23456
    }
    """

    username = decode_token(Authorization)
    if not username:
        return {'status_code': 403, 'content': '无权限访问'}
    file_dir = get_dir(is_private, username, project_name)
    os.makedirs(file_dir, exist_ok=True)
    # 校验项目库和私人库
    kwarg = {'project_name': project_name}
    if is_private:
        kwarg['user'] = username
    else:
        kwarg['is_private'] = is_private
    if Project.objects.filter(**kwarg):
        return JSONResponse({"status_code": 200, 'content': '创建失败，请更换库名'})
    maxkb_desc = username
    project_id = get_project_id(maxkb_desc, project_name, kind=is_private,username=username)
    content = {'msg': '创建失败，请更换库名', 'project_id': None}
    if not is_private:
        project_permission = get_sacp_list(username, project_name)
        project_permission = project_permission if project_permission else {}
    else:
        project_permission = {}
    if isinstance(project_permission,dict):
        project_permission = [project_permission]
    kwarg = {'user': username, 'project_name': project_name, 'project_id': project_id,'project_permission': project_permission,
             'project_introduce': project_introduce, 'file_dir': file_dir, 'is_private': is_private}
    if not is_private:
        save_permission(project_name, username, '3')
    if project_id:
        save_project(**kwarg)
        content = {'msg': '创建成功', 'project_id': project_id}
    data = {"status_code": 200, 'content': content}
    return JSONResponse(data)
