# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/19 16:31
@Auth ： qu ming
@File ：delete_file.py
@IDE ：PyCharm
"""
import os

from fastapi import APIRouter, Header
from fastapi import Body
from starlette.responses import JSONResponse
from app.v1.core.tokens import decode_token
from app.v1.core.upload import delete_doc
from app.v1.core.utils import get_dir
from app.v1.db.user_project import delete_files

router = APIRouter()


@router.post("/api/delete_file")
async def delete_file(Authorization: str = Header(None),
                      file_list: list = Body(None),
                      project_name: str = Body(None),
                      project_id: str = Body(None),
                      is_all: bool = Body(False),
                      is_private: bool = Body(None),
                      ):
    """
    提供聊天服务： 保存访问次数及问题

    return: {
        "status_code": 200
        'content': "删除成功"
        'project_id': 23456
    }
    """

    username = decode_token(Authorization)
    if not username:
        return {'status_code': 403, 'content': '无权限访问'}
    file_dir = get_dir(is_private, username, project_name)
    document_id_list = delete_files(username, file_list, file_dir, project_id, is_all)
    if document_id_list:
        for document_id in document_id_list:
            delete_doc(project_id, document_id, kind = is_private, username=username)
    data = {"status_code": 200, 'content': '删除成功'}
    return JSONResponse(data)
