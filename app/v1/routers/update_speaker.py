# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/6 13:20
@Auth ： qu ming
@File ：update_speaker.py
@IDE ：PyCharm
"""

from fastapi import APIRouter, Header, Body
from fastapi.encoders import jsonable_encoder
from starlette.responses import JSONResponse

from app.v1.core.process_speech import get_from_db, update_to_db
from app.v1.core.tokens import decode_token
from app.v1.core.utils import str_replace

router = APIRouter(prefix="/api/speaker", tags=["ai中心"])


@router.post("/list")
async def user_search(
        Authorization: str = Header(None),
        audio_filename: str = Body(None),
        dialog_length: str = Body(None),
):
    """
    查询说话人
    """
    username = decode_token(Authorization)
    if not username:
        return JSONResponse({'status_code': 403, 'content': '无权限访问'})
    speakers = []
    result = get_from_db(audio_filename, dialog_length)
    if result:
        dialog_data = result.get('dialog_data')
        speakers = list(set([item["speaker"] for item in dialog_data]))
    return JSONResponse(jsonable_encoder({"status_code": 200, "content": speakers}))


@router.post("/update")
def update_speaker(Authorization: str = Header(None),
                   speakers: list = Body(None),
                   audio_filename:str = Body(None),
                   dialog_length: str = Body(None)
                   ):
    """
    一键删除用户的所有历史记录
    """
    username = decode_token(Authorization)
    if not username:
        return {'status_code': 403, 'content': '无权限访问'}
    result = get_from_db(audio_filename, dialog_length)
    if result and speakers:
        summary = str_replace(result.get('summary'), speakers)
        dialog_data = str_replace(result.get('dialog_data'), speakers)
        meeting_points = str_replace(result.get('meeting_points'), speakers)
        data = {'summary': summary, 'dialog_data':dialog_data,'meeting_points': meeting_points,'dialog_length': dialog_length}
        flag = update_to_db(audio_filename, data)
        if flag:
            return JSONResponse({'status_code': 200, 'content': '修改完成'})
    return JSONResponse({'status_code': 500, 'content': '修改失败'})


