# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/18 13:09
@Auth ： qu ming
@File ：permission.py
@IDE ：PyCharm
"""
from fastapi import APIRouter, Header, Body

from app.v1.core.tokens import decode_token, get_sacp_list, get_private
from fastapi.responses import JSONResponse

from app.v1.db.permission_info import save_permission, query_permission

router = APIRouter(prefix="/api/permission", tags=["权限控制"])


@router.get("/list")
async def get_list(Authorization: str = Header(None),
                   project_name: str = None):
    """
    获取项目成员
    return: {
        "status_code": 200
        'content': [xxxx,xxx,xxx,xxx]
    }
    """

    username = decode_token(Authorization)
    if not username:
        return {'status_code': 403, 'content': '无权限访问'}
    if project_name:
        result = get_sacp_list(username, project_name)
        if result:
            new_list = [w['loginId'] for w in result]
            if username not in new_list:
                result.append({'loginId':username,'userName': get_private(username)['userName']})
            res = query_permission(project_name)
            permission_dict = {}
            if res:
                permission_dict = res[0].permission
            for i in result:
                i['permission'] = '2'
                for k, y in permission_dict.items():
                    if k == i['loginId']:
                        i['permission'] = y
        else:
            result = '无法获取项目数据'
        return JSONResponse({"status_code": 200, 'content': result})
    return JSONResponse({"status_code": 200, 'content': '请上传项目id'})


@router.post("/update")
async def update_data(Authorization: str = Header(None),
                      project_name: str = Body(None),
                      user: str = Body(None),
                      permission: str = Body('2')):
    """
    获取项目成员
    return: {
        "status_code": 200
        'content': [xxxx,xxx,xxx,xxx]
    }
    """

    username = decode_token(Authorization)
    if not username:
        return {'status_code': 403, 'content': '无权限访问'}
    save_permission(project_name, user, permission)
    return JSONResponse({"status_code": 200, 'content': '修改成功'})
