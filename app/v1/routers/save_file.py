# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/11 15:34
@Auth ： qu ming
@File ：save_file.py
@IDE ：PyCharm
"""

import os
from fastapi import APIRouter, Header, UploadFile, File, Body
from fastapi.responses import JSONResponse
from wps_callback_kit import wps
from app.v1.core.utils import sava_content, is_audio_file, get_uuid
from typing import Optional, List
from fastapi.requests import Request
from app.v1.core.tokens import decode_token
from app.v1.db.file_info import save_file, query_file
from app.v1.settings import Config

router = APIRouter()


@router.post("/api/UploadFiles", tags=['数据训练'])
async def upload_file(request: Request,
                      Authorization: Optional[str] = Header(None),
                      project_name: str = Body(None),
                      files: List[UploadFile] = File(...),
                      is_private: str = Body(None),
                      project_id: str = Body(None)):
    """
    根据上传文件，支持doc,docx,txt,pdf，excel
    """
    username = decode_token(Authorization)
    if not username:
        result = {'status': 403, 'content': '无权限访问'}
        return JSONResponse(result)
    if is_private == 'true':
        file_dir = os.path.join(Config.txt_storage_path, f"{username}/{project_name}")
    else:
        file_dir = os.path.join(Config.txt_storage_path, f"project/{project_name}")
    os.makedirs(file_dir, exist_ok=True)
    for file in files:
        file_size = request.headers.get("content-length")
        file_size_mb = int(file_size) / (1024 * 1024)
        # 检查文件大小是否超过50MB
        if file_size_mb > 50:
            result = {'status_code': 200, 'content': '文件限制为100MB以內'}
            return JSONResponse(result)
        file_path = os.path.join(file_dir, file.filename)
        data = await file.read()
        sava_content(file_path, data)
        if query_file(username, file.filename, project_id):
            return JSONResponse({'status_code': 500, 'content': '上传同名文件，请更换文件'})
        wps_url = ''
        # 上传文件的都加载url
        if not is_audio_file(file.filename):
            try:
                wps_url = wps.run(get_uuid(), file_path, username)
            except:
                wps_url = ''
        save_file(username, file.filename, 'normal', False, wps_url, '', int(file_size), '', project_id)
    result = {'status_code': 200, 'content': '上传成功'}
    return JSONResponse(result)
