# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/11 15:38
@Auth ： qu ming
@File ：select_file.py
@IDE ：PyCharm
"""
import os

from fastapi import APIRouter, Header, BackgroundTasks
from fastapi import Body
from app.v1.core.tokens import decode_token
from app.v1.core.upload import upload_and_process
from app.v1.core.utils import get_dir, is_audio_file
from fastapi.responses import JSONResponse
from app.v1.db.file_info import update_file, save_file, query_file
from app.v1.core.xfquota import check_and_reserve_quota


router = APIRouter()


@router.post("/api/select_file")
async def select_file(
    background_tasks: BackgroundTasks,
    Authorization: str = Header(None),
    file_list: list = Body(None),
    project_name: str = Body(None),
    project_id: str = Body(None),
    is_private: bool = Body(None),
    file_type: str = Body(None),
    fileId: str = Body(None),
    mainid: str = Body(None)
):
    username = decode_token(Authorization)
    if not username:
        return {'status_code': 403, 'content': '无权限访问'}
    source = 'normal'
    first_file = file_list[0]
    if mainid:
        source = 'law'
        first_file = first_file + '.txt'
    elif not is_private and fileId and file_type:
        source = 'sacp'
    file_dir = get_dir(is_private, username, project_name)
    os.makedirs(file_dir, exist_ok=True)
    file_path = os.path.join(file_dir, first_file)

    if source in ['law', 'sacp']:
        if query_file(username, first_file, project_id):
            return JSONResponse({'status_code': 500, 'content': '上传同名文件，请更换文件'})
        save_file(username, first_file, source, True, '', '', size=None, project_id=project_id)
    if is_audio_file(first_file):
        try:
            check_and_reserve_quota(username, file_path)
        except RuntimeError as e:
            return JSONResponse(status_code=403, content={"status_code": 403, "content": str(e)})
    update_file(username, first_file, project_id, {'source': source, 'is_study': True})
    background_tasks.add_task(
        upload_and_process,
        [first_file],
        file_dir,
        username,
        project_id,
        is_private,
        project_name,
        fileId,
        file_type,
        mainid,
        kind=is_private
    )

    return JSONResponse({'status_code': 200, 'content': '选择成功'})
