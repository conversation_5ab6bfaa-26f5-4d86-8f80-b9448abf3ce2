# routers/storage_summary.py
# -*- coding: utf-8 -*-
"""
客户文件分类 + 空间统计 API（汇总 customer_id 下所有项目库）
"""
from __future__ import annotations
from pathlib import Path
from collections import defaultdict
from typing import Dict, Tuple

from fastapi import API<PERSON><PERSON><PERSON>, Header, Body
from fastapi.encoders import jsonable_encoder
from starlette.responses import JSONResponse
import humanize

from app.v1.core.tokens import decode_token
from app.v1.core.utils import get_dir

# ---------------- 配置 ----------------
CUSTOMER_QUOTA = 10 * 1024 ** 3  # 10 GB

EXT_CATEGORY_MAP = {
    **{e: "word文档"  for e in (".doc", ".docx", ".dot", ".rtf")},
    **{e: "PPT文档"   for e in (".ppt", ".pptx", ".pps", ".pot")},
    **{e: "Excel文档" for e in (".xls", ".xlsx", ".xlsm", ".csv")},
    **{e: "PDF文档"   for e in (".pdf", ".ofd")},
    **{e: "图片"      for e in (".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp")},
    **{e: "音视频"    for e in (".mp3", ".wav", ".aac", ".flac",
                                ".mp4", ".mov", ".avi", ".mkv", ".amr")},
    **{e: "压缩包"    for e in (".zip", ".rar", ".7z", ".tar", ".gz")},
}
DEFAULT_CATEGORY = "其他"

router = APIRouter(tags=["数据查询"])


# ---------------- 工具函数 ----------------
def _categorize(path: Path) -> str:
    return EXT_CATEGORY_MAP.get(path.suffix.lower(), DEFAULT_CATEGORY)


def _scan_dir(root: Path) -> Tuple[Dict[str, int], Dict[str, int], int, int]:
    """
    扫描一个目录（项目库）并返回：计数字典、大小字典、文件数、目录数。
    - 忽略含『说话人1：』的转写 txt
    - 忽略与同名非 txt 修改时间差 ≤1h 的 txt
    """
    cat_cnt = defaultdict(int)
    cat_size = defaultdict(int)
    files = dirs = 0

    non_txt_mtime: Dict[str, float] = {}
    for p in root.rglob("*"):
        if p.is_file() and p.suffix.lower() != ".txt":
            non_txt_mtime[p.stem] = p.stat().st_mtime

    for p in root.rglob("*"):
        if p.is_dir():
            dirs += 1
            continue

        suffix = p.suffix.lower()
        stem = p.stem

        if suffix == ".txt":
            try:
                txt_time = p.stat().st_mtime
                content = p.read_text("utf-8", errors="ignore")
                if "说话人1：" in content:
                    continue
                if abs(txt_time - non_txt_mtime.get(stem, 0)) <= 3600:
                    continue
            except Exception:
                continue

        files += 1
        cat = _categorize(p)
        sz = p.stat().st_size
        cat_cnt[cat]  += 1
        cat_size[cat] += sz

    return cat_cnt, cat_size, files, dirs


def _aggregate_customer(customer_root: Path) -> Tuple[Dict[str, int], Dict[str, int], int, int]:
    """
    汇总 customer_root 下所有一级子目录（项目库）+ 根目录直放文件
    （不再重复扫描根目录递归，避免双重统计）
    """
    agg_cnt = defaultdict(int)
    agg_size = defaultdict(int)
    agg_files = agg_dirs = 0

    # 根目录直放文件
    for p in customer_root.iterdir():
        if p.is_file():
            cat = _categorize(p)
            agg_cnt[cat]  += 1
            agg_size[cat] += p.stat().st_size
            agg_files += 1

    # 每个项目库
    for sub in customer_root.iterdir():
        if sub.is_dir():
            cnt, sz, files, dirs = _scan_dir(sub)
            for k in cnt:
                agg_cnt[k]  += cnt[k]
                agg_size[k] += sz[k]
            agg_files += files
            agg_dirs  += dirs + 1     # 计入项目库本身

    return agg_cnt, agg_size, agg_files, agg_dirs


def _build_summary(customer_id: str, root: Path) -> Dict:
    cnt, sz, total_files, total_dirs = _aggregate_customer(root)
    used = sum(sz.values())
    return {
        "customer_id": customer_id,
        "total_files": total_files,
        "total_dirs": total_dirs,
        "quota": humanize.naturalsize(CUSTOMER_QUOTA, binary=True),
        "used": humanize.naturalsize(used, binary=True),
        "used_pct": round(used / CUSTOMER_QUOTA * 100, 1) if CUSTOMER_QUOTA else 0,
        "categories": [
            {
                "name":  cat,
                "count": cnt[cat],
                "size":  humanize.naturalsize(sz[cat], binary=True),
                "size_bytes": sz[cat],
                "pct":   round(sz[cat] / used * 100, 1) if used else 0
            }
            for cat in sorted(cnt)
        ]
    }


# ---------------- API ----------------
@router.post("/api/storage_summary")
def storage_summary_api(
    Authorization: str = Header(None),
    is_private: bool = Body(True),
    project_name: str | None = Body(None),# 已忽略
    customer_id: str = Body(..., description="客户/用户名，对应根目录名")
):
    username = decode_token(Authorization)
    if not username:
        return JSONResponse({"status": 403, "content": "无权限访问"})

    # 不再拼 project_name；直接定位客户根目录
    customer_root = Path(get_dir(is_private, customer_id, ""))

    if not customer_root.exists():
        return JSONResponse({"status": 404, "content": "客户目录不存在"})

    summary = _build_summary(customer_id, customer_root)
    return JSONResponse(jsonable_encoder({"status": 200, "content": summary}))
