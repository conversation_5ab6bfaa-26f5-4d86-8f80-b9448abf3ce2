# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/17 14:34
@Auth ： qu ming
@File ：file_details.py
@IDE ：PyCharm
"""
import io
import os

from fastapi import APIRouter, Header
from fastapi.responses import JSONResponse
from starlette.responses import StreamingResponse

from typing import Optional
from app.v1.core.tokens import decode_token
from app.v1.core.utils import get_dir, is_audio_file

router = APIRouter()


@router.get("/api/details", tags=['ai中心'])
async def get_details(Authorization: Optional[str] = Header(None),
                      project_name: str = None,
                      file_name: str = None,
                      is_play: bool = True,
                      is_private: bool = True):
    """
    选定文件，上传相关文件内容
    """
    username = decode_token(Authorization)
    if not username:
        result = {'status': 403, 'content': '无权限访问'}
        return JSONResponse(result)
    file_dir = get_dir(is_private, username, project_name)
    os.makedirs(file_dir, exist_ok=True)
    file_path = os.path.join(file_dir, file_name)
    # 仅仅只有这种情况传内容（音频）
    if is_audio_file(file_name) and not is_play:
        file_path = file_path.rsplit('.', 1)[0] + '.txt'
        if not os.path.exists(file_path):
            return JSONResponse({'status_code': 200, 'content': '录音正在转文字。。。'})
    with open(file_path,'rb')as f:
        content = f.read()
    file_stream = io.BytesIO(content)
    return StreamingResponse(file_stream, media_type="application/octet-stream")
