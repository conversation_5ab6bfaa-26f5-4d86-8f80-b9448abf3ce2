# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/8 11:20
@Auth ： qu ming
@File ：judge_.py
@IDE ：PyCharm
"""
import os

from fastapi import APIRouter, Header
from fastapi.responses import JSONResponse
from typing import Optional

from app.v1.settings import Config
from app.v1.core.tokens import decode_token

router = APIRouter()


@router.get("/api/judge_qa", tags=['ai中心'])
async def records(Authorization: Optional[str] = Header(None),
                  project_id: str = None):
    """
    判断sacp是否可以问答

     return: {
        "status_code": 200
        'content': [xxxx,xxxx]
        }
    """
    username = decode_token(Authorization)
    if not username:
        result = {'status': 403, 'content': '无权限访问'}
        return JSONResponse(result)
    file_dir = os.path.join(Config.txt_storage_path, "project")
    for file in os.listdir(file_dir):
        if project_id in file:
            return JSONResponse({'status_code': 200, 'content': os.listdir(os.path.join(file_dir, file))})
    result = {'status_code': 200, 'files': []}
    return JSONResponse(result)
