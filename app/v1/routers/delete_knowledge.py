# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/17 16:21
@Auth ： qu ming
@File ：delete_knowledge.py
@IDE ：PyCharm
"""

import os

from fastapi import APIRouter, Header
from fastapi import Body
from starlette.responses import JSONResponse

from app.v1.core.tokens import decode_token, delete_dataset
from app.v1.core.utils import get_dir
from app.v1.db.permission_info import delete_permission
from app.v1.db.user_project import delete_data

router = APIRouter()


@router.post("/api/delete_knowledge", tags=['ai中心'])
async def delete_knowledge(Authorization: str = Header(None),
                           project_name: str = Body(None),
                           project_id: str = Body(None),
                           is_private: bool = Body(True)):
    """
    提供聊天服务： 保存访问次数及问题

    return: {
        "status_code": 200
        'content': "删除成功"
        'project_id': 23456
    }
    """

    username = decode_token(Authorization)
    if not username:
        return {'status_code': 403, 'content': '无权限访问'}
    content = '删除失败'
    if delete_dataset(project_id,kind=is_private,username=username).get('status_code') == 200:
        file_dir = get_dir(is_private, username, project_name)
        delete_data(file_dir, project_id, is_private, username)
        if not is_private:
            delete_permission(project_name)
        content = '删除成功'
    data = {"status_code": 200, 'content': content}
    return JSONResponse(data)
