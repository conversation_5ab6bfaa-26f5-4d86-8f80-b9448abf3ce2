# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/18 16:57
@Auth ： qu ming
@File ：get_permission.py
@IDE ：PyCharm
"""

from fastapi import APIRouter, Header
from starlette.responses import JSONResponse
from app.v1.core.tokens import decode_token, project_permission

router = APIRouter()


@router.get("/api/get_permission", tags=['ai中心'])
async def get_permission(Authorization: str = Header(None)):
    """
    获取项目权限

    return: {
        "status_code": 200
        'content': []
    }
    """

    username = decode_token(Authorization)
    if not username:
        return {'status_code': 403, 'content': '无权限访问'}
    content = project_permission(username)
    data = {"status_code": 200, 'content': content}
    return JSONResponse(data)
