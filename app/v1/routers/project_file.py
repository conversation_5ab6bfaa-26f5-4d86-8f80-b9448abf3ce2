# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/8 15:43
@Auth ： qu ming
@File ：project_file.py
@IDE ：PyCharm
"""

from typing import Optional
from fastapi import APIRouter, Header, Query
from fastapi.responses import JSONResponse

from app.v1.core.tokens import decode_token, get_sacp_file, query_law_titles

router = APIRouter()


@router.get("/api/project_files", tags=['ai中心'])
def get_files(
        Authorization: str = Header(None),
        project_name: str = None,
        # 以下三个都是法规库查询要用的
        keywords: Optional[str] = Query(None, description="法规检索关键字"),
        pageNum: Optional[str] = Query(None, description="页码；不传 → 项目库分支"),
        pageSize: Optional[str] = Query(None, description="每页条数；只在法规分支生效"),
        searchType: Optional[str] = Query(None, description="搜索类型：title/content/tag；只在法规分支生效")
):
    """
    获取项目相关文件 或者 法规库标题列表
    """
    username = decode_token(Authorization)
    if not username:
        return JSONResponse({'status_code': 403, 'content': '无权限访问'})
    if not pageNum:
        projectId = project_name.split('-')[0]
        res = get_sacp_file(username, projectId)
        return JSONResponse({'status_code': 200, 'content': res})
    else:
        items = query_law_titles(
            pageNum=pageNum,
            pageSize=pageSize,
            keywords=keywords,
            searchType=searchType,
            username=username
        )
        return JSONResponse({'status_code': 200, 'content': items})
