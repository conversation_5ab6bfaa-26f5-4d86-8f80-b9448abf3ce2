# routers/process_speech.py
# -*- coding: utf-8 -*-
"""
音频转写 → 关键词 / 摘要 / 会议纪要
"""

import io
import os
import traceback
from fastapi import APIRouter, Body, Header
from fastapi.encoders import jsonable_encoder
from pydub import AudioSegment
from starlette.responses import JSONResponse

from app.v1.core.process_speech import (
    process_speech_data, get_from_db,
    regenerate_summary, regenerate_meeting_points
)
from app.v1.core.tokens import decode_token
from app.v1.core.utils import get_dir

router = APIRouter()


# ------------------------------------------------------------
# 1. 首次处理
# ------------------------------------------------------------
import time
from datetime import datetime as dt

@router.post("/api/process_speech", tags=["数据查询"])
def process_speech_api(
    Authorization: str = Header(None),
    is_private: bool = Body(True),
    audio_file: str = Body(...),
    project_name: str = Body(None),
    type_speech: str = Body(None),
):
    username = decode_token(Authorization)
    if not username:
        # 无权限统一格式
        return JSONResponse(
            jsonable_encoder({"status_code": 403, "content": "无权限访问"})
        )
    # 定位文件
    file_dir = get_dir(is_private, username, project_name)
    file_path = os.path.join(file_dir, audio_file)
    trans_path = file_path.rsplit(".", 1)[0] + ".txt"

    # 计算时长
    try:
        raw = open(file_path, "rb").read()
    except FileNotFoundError:
        return JSONResponse(
            jsonable_encoder({"status_code": 404, "content": "音频文件不存在"})
        )
    ext = audio_file.rsplit(".", 1)[-1].lower()
    duration_ms = len(AudioSegment.from_file(io.BytesIO(raw), format=ext))
    m, s = divmod(duration_ms // 1000, 60)
    dialog_length = f"{m}分{s}秒"

    # 只查缓存分支
    if type_speech is None:
        cached = get_from_db(audio_file, dialog_length)
        if cached:
            return JSONResponse(
                jsonable_encoder({"status_code": 200, "content": cached})
            )
        else:
            return JSONResponse({"status_code": 404, "content": "尚未转写完毕或无可用结果"})

    # 全流程（读转写 + AI 处理）
    try:
        transcript = open(trans_path, "r", encoding="utf-8", errors="ignore").read()
    except FileNotFoundError:
        return JSONResponse(
            jsonable_encoder({"status_code": 404, "content": "转写文件不存在"})
        )
    try:
        result = process_speech_data(
            transcript_text=transcript,
            audio_bytes=raw,
            audio_format=ext,
            audio_filename=audio_file,
            created_at=dt.utcnow(),
            file_path=file_path,
        )
        return JSONResponse(
            jsonable_encoder({"status_code": 200, "content": result})
        )

    except Exception as e:
        traceback.print_exc()
        return JSONResponse(
            jsonable_encoder({"status_code": 500, "content": str(e)})
        )


# ------------------------------------------------------------
# 2. 重新生成摘要
# ------------------------------------------------------------
@router.post("/api/regenerate_summary", tags=["数据查询"])
def regenerate_summary_api(
        Authorization: str = Header(None),
        is_private: bool = Body(True),
        audio_file: str = Body(...),
        project_name: str = Body(...)
):
    username = decode_token(Authorization)
    if not username:
        return JSONResponse({"status_code": 403, "content": "无权限访问"})

    file_dir = get_dir(is_private, username, project_name)
    file_path = os.path.join(file_dir, audio_file)
    if not os.path.exists(file_path):
        return JSONResponse({"status_code": 404, "content": "音频文件不存在"})

    extension = audio_file.split(".")[-1].lower()
    duration_ms = len(AudioSegment.from_file(io.BytesIO(open(file_path, "rb").read()),
                                             format=extension))
    m, s = divmod(duration_ms // 1000, 60)
    dialog_length = f"{m}分{s}秒"

    record = regenerate_summary(audio_file, dialog_length)
    if not record:
        return JSONResponse({"status_code": 404, "content": "记录不存在"})

    # 仅返回更新的字段
    return JSONResponse(jsonable_encoder({
        "status_code": 200,
        "content": {"summary": record["summary"]}
    }))


# ------------------------------------------------------------
# 3. 重新生成会议纪要
# ------------------------------------------------------------
@router.post("/api/regenerate_meeting_points", tags=["数据查询"])
def regenerate_meeting_points_api(
        Authorization: str = Header(None),
        is_private: bool = Body(True),
        audio_file: str = Body(...),
        project_name: str = Body(...)
):
    username = decode_token(Authorization)
    if not username:
        return JSONResponse({"status_code": 403, "content": "无权限访问"})

    file_dir = get_dir(is_private, username, project_name)
    file_path = os.path.join(file_dir, audio_file)
    if not os.path.exists(file_path):
        return JSONResponse({"status_code": 404, "content": "音频文件不存在"})

    extension = audio_file.split(".")[-1].lower()
    duration_ms = len(AudioSegment.from_file(io.BytesIO(open(file_path, "rb").read()),
                                             format=extension))
    m, s = divmod(duration_ms // 1000, 60)
    dialog_length = f"{m}分{s}秒"

    record = regenerate_meeting_points(audio_file, dialog_length)
    if not record:
        return JSONResponse({"status_code": 404, "content": "记录不存在"})

    # 仅返回更新的字段
    return JSONResponse(jsonable_encoder({
        "status_code": 200,
        "content": {"meeting_points": record["meeting_points"]}
    }))
