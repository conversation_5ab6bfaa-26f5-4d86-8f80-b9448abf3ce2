# -*- encoding: utf-8 -*-
"""
@File    : check_user.py
@Time    : 2023/4/19 15:48
<AUTHOR> qu ming
"""

from fastapi import Query, Depends
from fastapi.responses import JSONResponse

from auth_permission.core.dependencies import get_user
from app.v1.core.tokens import encode_token, judge_is_login
from auth_permission.schemas.system import Auth
from fastapi import APIRouter

router = APIRouter()


@router.get("/api/User", tags=['云办公校验'])
async def user_info(username: str = Query(None),
              token: str = Query(None),
              auth: Auth = Depends(get_user)):
    """
    根据用户生成token

    return {
      "status_code": 200
      'content': {
        'username': user,
        'token': token
    }}
    """

    if not username or not token:
        result = {'status_CODE': 403, 'content': '无权限访问'}
        return JSONResponse(result)
    if judge_is_login(login_id=username, systemName='BdoXXR', token=token):
        token = encode_token(username)
        data = {
            'username': username,
            'token': token
        }
        result = {'status_code': 200, 'content': data}
        return JSONResponse(result)
    else:
        result = {'status_CODE': 403, 'content': '无权限访问'}
    return JSONResponse(result)


