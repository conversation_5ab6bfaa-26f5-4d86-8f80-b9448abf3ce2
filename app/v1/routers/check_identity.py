# -*- encoding: utf-8 -*-
"""
@File    : check_identity.py
@Time    : 2023/4/19 15:58
<AUTHOR> qu ming
"""
from fastapi import Query, APIRouter
from fastapi.responses import JSONResponse, RedirectResponse
from app.v1.core.tokens import encode_token,get_user_id_v2
from app.v1.core.utils import judge_product

router = APIRouter()


@router.get("/api/UserInfo", tags=['验证信息'])
def get_user_info(code: str = Query(None)):
    """
    根据用户生成token,返回url

    return https://ai-uat.bdo.com.cn/?code={code}&token={token}&loginid={user_id}
    """
    user_id = get_user_id_v2(code)
    if not user_id:
        result = {'status_CODE': 403, 'content': '无权限访问'}
        return JSONResponse(result)
    token = encode_token(user_id)
    if judge_product():
        url = f'https://ai.bdo.com.cn/?code={code}&token={token}&loginid={user_id}'
    else:
        url = f'https://ai-uat.bdo.com.cn?code={code}&token={token}&loginid={user_id}'
    return RedirectResponse(url)
