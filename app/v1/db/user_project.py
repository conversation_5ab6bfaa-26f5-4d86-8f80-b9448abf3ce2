# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/11 15:53
@Auth ： qu ming
@File ：user_project.py
@IDE ：PyCharm
"""
import os
import shutil
from copy import copy
from typing import Union
from app.v1.core.utils import is_audio_file
from app.v1.db.file_info import delete_file, query_file
from app.v1.models.UserProject import Project
from app.v1.settings import Config


def save_project(user: str,
                 project_permission: dict,
                 project_name: str = None,
                 project_id: str = None,
                 project_introduce: str = None,
                 file_dir: str = None,
                 is_private: bool = True,
                 ):
    """
    记录用户创建的知识库
    """
    obj = Project(user=user, project_permission=project_permission, project_name=project_name, project_id=project_id,
                  project_introduce=project_introduce,
                  file_dir=file_dir, embedding_files='', qa=False, is_private=is_private)
    obj.save()


def query_project(name: str = None, is_private: bool = True):
    """
    查询用户可使用知识库(私人库或者项目库)
    """

    return Project.objects.filter(user=name, is_private=is_private)


def update_project(project_id: Union[list, str], assistant_id: str):
    """
    更新用户创建的知识库和私人库
    """
    # 更新之前先删除之前的记录
    obj_list = Project.objects.filter(assistant_id=assistant_id)
    if obj_list:
        for i in obj_list:
            i.update(assistant_id=None)
    if isinstance(project_id, str):
        project_id = [project_id]
    for pro in project_id:
        obj = Project.objects.filter(project_id=pro)
        if len(obj) == 1:
            obj[0].update(assistant_id=assistant_id)


def delete_data(file_dir: str, project_id: str = None, is_private: bool = True, name: str = None):
    """
    删除知识库
    """
    filter_param = {'project_id': project_id, 'is_private': is_private}
    file_dir = os.path.join(Config.txt_storage_path, file_dir)
    Project.objects.filter(**filter_param).delete()
    if os.path.exists(file_dir):
        for file in os.listdir(file_dir):
            delete_file(name, file, project_id)
        shutil.rmtree(file_dir)
        # 删除文件


def delete_files(name: str, file_list: list, file_dir: str, project_id: str, is_all: bool = False):
    """
    删除文件
    """
    if os.path.exists(file_dir):
        result = []
        if is_all:
            file_list = os.listdir(file_dir)
        new_file_list = copy(file_list)
        for f in new_file_list:
            if is_audio_file(f):
                file_list.append(f"{f.split('.')[0]}.txt")
        for file in file_list:
            file_path = os.path.join(file_dir, file)
            if os.path.isfile(file_path):
                os.remove(file_path)
            obj = query_file(name, file, project_id)
            if len(obj) == 1:
                result.append(obj[0].file_id)
                delete_file(name, file, project_id)
        return result


if __name__ == '__main__':
    print((Project.objects.all()).delete())
