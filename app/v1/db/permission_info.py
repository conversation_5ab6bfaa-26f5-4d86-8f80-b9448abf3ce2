# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/17 16:46
@Auth ： qu ming
@File ：permission_info.py
@IDE ：PyCharm
"""
import os
from copy import deepcopy
from app.v1.models.PermissionInfo import Permission
from app.v1.models.UserProject import Project
from app.v1.settings import Config


def save_permission(project: str, user: str, permissions: str):
    """
    保存权限信息,update_permission为3，read_permission为2，no_permission为1
    """
    obj = query_permission(project)
    if obj:
        new_dict = deepcopy(obj[0].permission)
        new_dict[user] = permissions
        obj[0].update(permission=new_dict)
    else:
        Permission(project=project, permission={user: permissions}).save()


def query_permission(project: str = None):
    """
    查询权限
    """
    return Permission.objects.filter(project=project)


def delete_permission(project: str):
    """
    删除权限
    """
    query_permission(project).delete()


def get_project(username: str):
    """
    获取项目表
    """

    file_dir = os.path.join(Config.txt_storage_path, "project")
    has_permission = []
    for pro in os.listdir(file_dir):
        try:
            obj = Project.objects.filter(project_name=pro, is_private=False)
            if obj:
                res = obj[0].project_permission
                for i in res:
                    if i['loginId'] == username:
                        # 判断是否本人所建
                        if obj[0].user != username:
                            # 判断是否可读权限
                            per = query_permission(pro)
                            # 确保为可读权限
                            if per and per[0].permission.get(username) != '0':
                                has_permission.append(obj[0])
        except:
            pass
    return has_permission


if __name__ == '__main__':
    print(get_project('zhu.junfang'))
