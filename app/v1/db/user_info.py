# -*- coding: utf-8 -*-
"""
@Time ： 2023/9/21 17:48
@Auth ： qu ming
@File ：user_info.py
@IDE ：PyCharm
"""
from typing import Optional, Union
from datetime import datetime
from mongoengine.queryset.visitor import Q

from app.v1.core.utils import limit_data
from app.v1.models.UserInfo import User


def query_user(user: Optional[str] = None,
               page: int = None,
               page_size: int = None,
               content_type: str = None,
               is_sort: bool = True,
               robot_name: str = None,
               project_id:str = None,
               assistant_id: str = None):
    """
    分页查询数据 page和page_size必须同时有值
    return: {
            total_number: 2000
            'data_list':[{
            '_id': ObjectId('642e3a6c5e1ef3ea443b7d01'),
             'name': 'qm',
             'question': '测试',
             'reply: '测试',
             'create_time’: '2022-04-07 15:51:01'
    }...]
        }
    """
    if user:
        order_column = '-create_time'
        if not is_sort:
            order_column = 'create_time'
        kwargs = {
            'name': user,
            'robot_name': robot_name,
            **({'type': content_type} if content_type else {}),
            **({'assistant_id': assistant_id} if assistant_id else {}),
            **({'project_id': project_id} if project_id else {})
        }
        data_list = User.objects.filter(**kwargs).order_by(order_column)
        total_number = len(data_list)
    else:
        if page and page_size:
            offset = (page - 1) * page_size
            data_list = User.objects.skip(offset).limit(page_size).order_by('-create_time')
            # 默认最新2800（以防内存溢出）
            total_number = 2800
        else:
            if content_type:
                data_list = User.objects.filter(type=content_type)
                total_number = len(data_list)
            else:
                data_list = User.objects.all()
                total_number = User.objects.count()
    # data_list = limit_data(data_list, page, page_size)
    data_dict = {
        'total_number': total_number,
        'data_list': list(map(lambda x: x.to_mongo().to_dict(), data_list))
    }
    return data_dict


def query_history_data(start: Union[datetime, str],
                       end: Union[datetime, str],
                       user: Optional[str] = None,
                       page: int = None,
                       page_size: int = None
                       ):
    """
    根据指定时间查询用户数据

    return:  return: [{
            '_id': ObjectId('642e3a6c5e1ef3ea443b7d01'),
             'name': 'qm',
             'question': '测试',
             'reply: '测试',
             'create_time’: '2022-04-07 15:51:01'
    }...]

    """
    if user:
        data_obj = User.objects(Q(create_time__gte=start) & Q(create_time__lte=end)
                                & Q(name=user)).order_by('-create_time')
    else:
        data_obj = User.objects(Q(create_time__gte=start) & Q(create_time__lte=end)).order_by('-create_time')
    total_number = len(data_obj)
    data_list = limit_data(data_obj, page, page_size)
    data_dict = {
        'total_number': total_number,
        'data_list': list(map(lambda x: x.to_mongo().to_dict(), data_list))
    }
    return data_dict


def record_data(user: str,
                question: str,
                reply: str,
                prompt_tokens,
                completion_tokens,
                fee,
                type='normal',
                robot_name=None,
                model='gpt',
                source='',
                assistant_id='',
                project_id=''):
    """
        记录访问次数及存储提交的问题和回答
        """
    data = User(name=user,
                question=question,
                reply=reply,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                robot_name=robot_name,
                fee=fee,
                type=type,
                model=model,
                source=source,
                assistant_id=assistant_id,
                project_id=project_id)
    data.save()


def delete_data(user):
    """
    删除用户数据
    """
    for i in User.objects.filter(name=user):
        i.delete()


def update_info():
    """
    更新所有用户名为小写
    """
    data_obj = User.objects.all()
    for i in data_obj:
        i.update(name=i.name.lower())


def get_last_one(name: str):
    """
    获取最新的已一条数据
    """
    order_column = '-create_time'
    return User.objects.filter(name=name).order_by(order_column).first()


def get_user_history(name: Optional[str],
                     page: int = 1,
                     page_size: int = 10,
                     ):
    """
    获取用户历史记录，支持分页、模糊搜索、按时间倒序排序
    :param name: 用户名
    :param page: 页码，从 1 开始
    :param page_size: 每页条数
    :return: {
        "total_number": int,
        "data_list": [dict, dict, ...]
    }
    """

    def serialize_user(doc: User) -> dict:
        create_time = doc.create_time

        return {
            "id": str(doc.id),
            "title": doc.question,
            "type": doc.type,
            "date": create_time.strftime("%Y-%m-%d"),
            "time": create_time.strftime("%H:%M:%S"),
        }

    try:
        query = Q(name=name)

        # 执行查询
        queryset = User.objects(query).order_by('-create_time')
        total = queryset.count()
        data = queryset.skip((page - 1) * page_size).limit(page_size)
        return {
            "total_number": total,
            "data_list": [serialize_user(doc) for doc in data]
        }
    except:
        return {
            "total_number": 0,
            "data_list": []
        }


def delete_user_history(name: Optional[str], file_type: Optional[str]) -> int:
    """
        删除指定用户的所有历史记录

        :param name: 用户名（必传）
        :param file_type: file_type（必传）
        :return: 实际删除的记录数量
        """
    try:
        query = Q(name=name, type=file_type)
        result = User.objects(query).delete()
        return result
    except:
        return 0


if __name__ == '__main__':
    print(query_user(user='qu.mingming', robot_name=None))
