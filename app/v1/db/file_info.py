# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/8 19:45
@Auth ： qu ming
@File ：file_info.py
@IDE ：PyCharm
"""
from app.v1.core.utils import get_extension
from app.v1.models.FileInfo import File


def save_file(name: str, file_name: str, source: str = 'normal', is_study: bool = False,
              url: str = None, file_id: str = None, size: int = None, abstract: str = None,
              project_id: str = None):
    """
    保存文件
    """

    obj = File(name=name, file_name=file_name, source=source, filename_extension=get_extension(file_name),
               is_study=is_study, url=url, file_id=file_id, size=size, abstract=abstract,
               project_id=project_id)
    obj.save()


def query_file(name: str = None, file_name: str = None, project_id: str = None):
    """
    查询文件
    """
    return File.objects.filter(name=name, file_name=file_name, project_id=project_id)


def update_file(name: str, file_name: str, project_id: str, kwarg: dict):
    """
    修改文件信息
    """
    obj = query_file(name, file_name, project_id)
    if len(obj) == 1:
        obj[0].update(**kwarg)


def delete_file(name: str, file_name: str, project_id: str):
    """
    删除数据
    """
    File.objects.filter(name=name, file_name=file_name, project_id=project_id).delete()


if __name__ == '__main__':
    # print(File.objects.filter(name='yi.dongshan').delete())
    print(File.objects.all().delete())
