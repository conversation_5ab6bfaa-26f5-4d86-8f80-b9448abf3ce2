# -*- encoding: utf-8 -*-
"""
@File   :config.py
@Time   :2025/4/27 16:47
<AUTHOR>
"""
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # 企微密钥
    wechat_secret: str = Field(..., alias="WECHAT_SECRET", description="企业微信密钥")
    wechat_corpid: str = Field(..., alias="WECHAT_CORPID", description="企微的corpid")

    # mongodb配置
    mongo_host: str = Field(
        default="localhost", alias="MONGO_HOST", description="mongodb host"
    )
    mongo_port: int = Field(
        default=27017, alias="MONGO_PORT", description="mongodb port"
    )
    mongo_user: str = Field(
        default=None, alias="MONGO_USER", description="mongodb user"
    )
    mongo_password: str = Field(
        default=None, alias="MONGO_PASSWORD", description="mongodb password"
    )
    mongo_database: str = Field(
        default=None, alias="MONGO_DATABASE", description="mongodb database"
    )

    @property
    def mongo_url(self) -> str:
        if self.mongo_user and self.mongo_password:
            auth = f"{self.mongo_user}:{self.mongo_password}@"
        else:
            auth = ""
        db = f"/{self.mongo_database}" if self.mongo_database else ""
        return f"mongodb://{auth}{self.mongo_host}:{self.mongo_port}{db}"

    # mysql配置
    mysql_host: str = Field(
        default="localhost", alias="MYSQL_HOST", description="mysql host"
    )
    mysql_port: int = Field(default=3306, alias="MYSQL_PORT", description="mysql port")
    mysql_user: str = Field(default=None, alias="MYSQL_USER", description="mysql user")
    mysql_password: str = Field(
        default=None, alias="MYSQL_PASSWORD", description="mysql password"
    )
    mysql_database: str = Field(
        default=None, alias="MYSQL_DATABASE", description="mysql database"
    )

    # openai llm 通用配置
    azure_openai_base_url: str = Field(
        ..., alias="AZURE_OPENAI_BASE_URL", description="azure openai base url"
    )

    # chatgpt_version = 4.0 用的配置
    ca_azure_openai_base_url: str = Field(
        ...,
        alias="CA_AZURE_OPENAI_BASE_URL",
        description="CA East Azure OpenAI Base URL",
    )
    ca_azure_openai_api_version: str = Field(
        ...,
        alias="CA_AZURE_OPENAI_API_VERSION",
        description="CA East Azure OpenAI API Version",
    )
    ca_azure_east_api_key: str = Field(
        ..., alias="CA_AZURE_EAST_API_KEY", description="CA East Azure OpenAI API Key"
    )
    ca_azure_openai_deployment_id: str = Field(
        ...,
        alias="CA_AZURE_OPENAI_DEPLOYMENT_ID",
        description="CA East Azure OpenAI Deployment ID",
    )

    # chatgpt_version = 4o 和默认用的配置
    us_azure_openai_base_url: str = Field(
        ...,
        alias="US_AZURE_OPENAI_BASE_URL",
        description="US East Azure OpenAI Base URL",
    )
    us_azure_openai_api_version: str = Field(
        ...,
        alias="US_AZURE_OPENAI_API_VERSION",
        description="US East Azure OpenAI API Version",
    )
    us_azure_east_api_key: str = Field(
        ..., alias="US_AZURE_EAST_API_KEY", description="US East Azure OpenAI API Key"
    )
    us_azure_openai_deployment_id: str = Field(
        ...,
        alias="US_AZURE_OPENAI_DEPLOYMENT_ID",
        description="US East Azure OpenAI Deployment ID",
    )

    # 默认 fallback 配置
    default_openai_deployment_id: str = Field(
        ..., alias="DEFAULT_OPENAI_DEPLOYMENT_ID", description="默认Azure OpenAI部署ID"
    )

    # 私人信息配置
    private_info_url: str = Field(
        ..., alias="PRIVATE_INFO_URL", description="私人信息接口URL"
    )
    private_info_key: str = Field(..., alias="PRIVATE_INFO_KEY", description="私人信息密钥")
    private_info_secret: str = Field(
        ..., alias="PRIVATE_INFO_SECRET", description="私人信息密钥的秘密"
    )

    # 上市公司信息配置
    listed_company_txt_path: str = Field(
        ..., alias="LISTED_COMPANY_TXT_PATH", description="上市公司信息文件路径"
    )
    txt_storage_path: str = Field(..., alias="TXT_STORAGE_PATH", description="文本存储路径")

    # sacp配置
    sacp_project_url: str = Field(
        ..., alias="SACP_PROJECT_URL", description="SACP项目URL"
    )

    # redis配置
    redis_host: str = Field(
        default="localhost", alias="REDIS_HOST", description="redis host"
    )
    redis_port: int = Field(default=6379, alias="REDIS_PORT", description="redis port")
    redis_password: str = Field(
        default=None, alias="REDIS_PASSWORD", description="redis password"
    )
    redis_db: str = Field(default=None, alias="REDIS_DB", description="redis db")

    # ollama配置
    ollama_base_url: str = Field(
        default=None, alias="OLLAMA_BASE_URL", description="Ollama服务基础URL"
    )

    # Milvus配置
    milvus_host: str = Field(
        default=None, alias="MILVUS_HOST", description="Milvus host"
    )
    milvus_port: str = Field(
        default=None, alias="MILVUS_PORT", description="Milvus port"
    )

    # 文档解析服务配置
    document_parsing_url: str = Field(
        ..., alias="DOCUMENT_PARSING_URL", description="文档解析服务URL"
    )

    # maxkb配置
    maxkb_project_library_url: str = Field(
        ..., alias="MAXKB_PROJECT_LIBRARY_URL", description="MaxKB项目库URL"
    )
    maxkb_private_library_url: str = Field(
        ..., alias="MAXKB_PRIVATE_LIBRARY_URL", description="MaxKB私有库URL"
    )

    # 本地vllm模型兼容openai配置
    vllm_openai_base_url: str = Field(
        ..., alias="VLLM_OPENAI_BASE_URL", description="vllm部署兼容的openai的URL"
    )
    vllm_openai_api_key: str = Field(
        ..., alias="VLLM_OPENAI_API_KEY", description="vllm部署兼容的openai的api key"
    )

    # 法律法规api配置
    law_api_base_url: str = Field(
        ..., alias="LAW_API_BASE_URL", description="法律法规前缀URL"
    )
    law_api_query_law_title_path: str = Field(
        ..., alias="LAW_API_QUERY_LAW_TITLE_PATH", description="法律法规查询标题的接口路径"
    )
    law_api_key: str = Field(..., alias="LAW_API_KEY", description="法鲁法规接口的api key")
    law_api_content_path: str = Field(
        ..., alias="LAW_API_CONTENT_PATH", description="法律法规查询内容的接口路径"
    )

    @property
    def law_api_query_law_title_url(self) -> str:
        return self.law_api_base_url.rstrip("/") + self.law_api_query_law_title_path

    @property
    def law_api_query_law_content_url(self) -> str:
        return self.law_api_base_url.rstrip("/") + self.law_api_content_path

    class Config:
        env_file_encoding = "utf-8"

