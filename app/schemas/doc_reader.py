# -*- encoding: utf-8 -*-
"""
@File   :doc_reader.py
@Time   :2025/6/10 10:21
<AUTHOR>
"""
from typing import Optional, List
from pydantic import BaseModel, Field


class ChatRequest(BaseModel):
    session_id: str
    message: str


class ChatMessageRequest(BaseModel):
    session_id: str = Field(..., description="会话ID")
    page: Optional[int] = Field(default=None, ge=1, description="页码")
    page_size: Optional[int] = Field(default=None, ge=1, le=100, description="每页数量")
    keyword: Optional[str] = Field(default=None, description="关键字搜索")
    filter_role: Optional[str] = Field(default=None, description="角色过滤")


class FileDeleteRequest(BaseModel):
    doc_id: str
    session_id: str


class BatchFileDeleteRequest(BaseModel):
    """批量删除文件请求模型"""
    session_id: str = Field(..., description="会话ID")
    doc_ids: List[str] = Field(..., description="文档ID列表")


class FileRestoreRequest(BaseModel):
    """恢复已删除文件请求模型"""
    session_id: str = Field(..., description="会话ID")
    doc_id: str = Field(..., description="要恢复的文档ID")
