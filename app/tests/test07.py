# -*- encoding: utf-8 -*-
"""
@File   :test07.py
@Time   :2025/7/4 22:06
<AUTHOR>
"""
import requests



api_key = "application-d40c19d84aa61e3ff1c6728a00d6b068"
base_url = "http://172.17.10.70:8891/api/application/2af4de78-20e5-11f0-8003-926528472ef0/chat/completions"

headers = {
    "accept": "application/json",
    "Authorization": "Bearer " + api_key
}

payload = {
    "stream": True,
    "messages": [
        {"role": "user", "content": "公派出境需要准备哪些资料"}
    ]
}

response = requests.post(base_url, headers=headers, json=payload, stream=True)

# 按行输出响应内容
for line in response.iter_lines():
    if line:
        print(line.decode('utf-8'))
