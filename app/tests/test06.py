import asyncio
import aiohttp
import json
from typing import AsyncGenerator, Dict, Any


async def async_sse_stream(message: str, dataset_id: str) -> AsyncGenerator[bytes, None]:
    api_key = "application-d40c19d84aa61e3ff1c6728a00d6b068"
    base_url = "http://172.17.10.70:8891/api/application/2af4de78-20e5-11f0-8003-926528472ef0/chat/completions"

    payload = {
        "stream": True,
        "messages": [
            {"role": "user", "content": message}
        ]
    }
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "*/*",
    }

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(base_url, headers=headers, json=payload) as resp:
                if resp.status != 200:
                    yield "SSE 请求失败，获取回复失败\n".encode()
                    yield b"<source>[]</source>\n"
                    return

                buffer = ""  # ⬅️ 字符缓存区
                flush_len = 20  # ⬅️ 每凑够这么多字就 flush 一次

                while True:
                    raw_line = await resp.content.readline()
                    if not raw_line:
                        break

                    line = raw_line.decode("utf-8").strip()
                    if not line.startswith("data:"):
                        continue

                    data_str = line.removeprefix("data:").strip()
                    if data_str == "[DONE]":
                        break

                    try:
                        data = json.loads(data_str)
                    except json.JSONDecodeError:
                        continue

                    delta = data.get("choices", [{}])[0].get("delta", {})
                    content = delta.get("content", "")
                    if content:
                        buffer += content
                        if len(buffer) >= flush_len:
                            yield buffer.encode()
                            buffer = ""

                if buffer:
                    yield buffer.encode()

        except Exception as e:
            yield f"系统繁忙，获取回复失败: {e}\n".encode()
            yield b"<source>[]</source>\n"


async def main():
    message = "公派出境需要准备哪些资料"
    dataset_id = "a0880f08-2102-11f0-b213-926528472ef0"
    async for chunk in async_sse_stream(message, dataset_id):
        print(chunk.decode(), end="")


if __name__ == "__main__":
    asyncio.run(main())
