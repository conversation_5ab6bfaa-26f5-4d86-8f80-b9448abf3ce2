# -*- encoding: utf-8 -*-
"""
@File   :test8.py
@Time   :2025/7/4 22:46
<AUTHOR>
"""
from openai import OpenAI

api_key = "application-d40c19d84aa61e3ff1c6728a00d6b068"
base_url = "http://172.17.10.70:8891/api/application/2af4de78-20e5-11f0-8003-926528472ef0"

client = OpenAI(api_key=api_key, base_url=base_url)

response = client.chat.completions.create(
    model="QWQ-32B-NGINX",
    messages=[

        {"role": "user", "content": "公派出境需要准备哪些资料"},
    ],
    stream=True
)

for chunk in response:
    print(chunk.choices[0].delta.content, end="", flush=True)
