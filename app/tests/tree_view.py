# -*- encoding: utf-8 -*-
"""
@File   :tree_view.py
@Time   :2025/5/28 09:41
<AUTHOR>
"""

import os
import fnmatch
import argparse


def parse_ignore_patterns(ignore_str):
    if not ignore_str:
        return []
    return [pattern.strip() for pattern in ignore_str.split("|")]


def should_ignore(name, ignore_patterns):
    for pattern in ignore_patterns:
        if fnmatch.fnmatch(name, pattern):
            return True
    return False


def print_tree(root, prefix="", ignore_patterns=[]):
    entries = sorted(os.listdir(root))
    entries = [e for e in entries if not should_ignore(e, ignore_patterns)]

    for i, entry in enumerate(entries):
        path = os.path.join(root, entry)
        connector = "└── " if i == len(entries) - 1 else "├── "
        print(prefix + connector + entry)

        if os.path.isdir(path):
            extension = "    " if i == len(entries) - 1 else "│   "
            print_tree(path, prefix + extension, ignore_patterns)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="生成目录结构树（类似 tree 命令）")
    parser.add_argument("path", type=str, help="目标目录路径")
    parser.add_argument("-I", "--ignore", type=str, default="", help="忽略的通配符，用 | 分隔，如 .venv|*.log|data")

    args = parser.parse_args()
    root_path = os.path.abspath(args.path)
    ignore_patterns = parse_ignore_patterns(args.ignore)

    print(root_path)
    print_tree(root_path, "", ignore_patterns)
