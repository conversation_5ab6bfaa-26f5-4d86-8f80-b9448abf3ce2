# -*- encoding: utf-8 -*-
"""
@File    :config.py
@Time    :2024/3/26 15:31
<AUTHOR>
"""

from pydantic import Field
from pydantic_settings import BaseSettings

import yaml
from functools import lru_cache


class Settings(BaseSettings):
    # 应用配置
    app_name: str = "aicenter"
    version: str = "1.0.0"
    debug: bool = False

    # 日志配置
    log_level: str = Field("INFO", alias="LOG_LEVEL", description="log level")
    log_rotation: str = Field("10 MB", alias="LOG_ROTATION", description="log rotation")
    log_retention: str = Field(
        "10 days", alias="LOG_RETENTION", description="log retention"
    )
    log_dir: str = Field("logs", alias="LOG_DIR", description="log dir")

    # LLM配置
    default_llm_provider: str = "openai"
    llm_timeout: int = 30
    llm_max_retries: int = 3
    llm_requests_per_minute: int = 60

    # mongodb配置
    mongo_host: str = Field(
        default="localhost", alias="MONGO_HOST", description="mongodb host"
    )
    mongo_port: int = Field(
        default=27017, alias="MONGO_PORT", description="mongodb port"
    )
    mongo_user: str = Field(
        default=None, alias="MONGO_USER", description="mongodb user"
    )
    mongo_password: str = Field(
        default=None, alias="MONGO_PASSWORD", description="mongodb password"
    )
    mongo_database: str = Field(
        default=None, alias="MONGO_DATABASE", description="mongodb database"
    )
    mongo_pool_size: str = Field(
        default=5, alias="MONGO_POOL_SIZE", description="mongodb pool size"
    )
    encryption_key: str = Field(
        default=None, alias="ENCRYPTION_KEY", description="api key  encryption key"
    )
    wecom_webhook_url: str = Field(
        default=None, alias="WECOM_WEBHOOK_URL", description="企业微信机器人webhook url"
    )

    # mysql配置
    mysql_host: str = Field(
        default="localhost", alias="MYSQL_HOST", description="mysql host"
    )
    mysql_port: int = Field(default=3306, alias="MYSQL_PORT", description="mysql port")
    mysql_user: str = Field(default=None, alias="MYSQL_USER", description="mysql user")
    mysql_password: str = Field(
        default=None, alias="MYSQL_PASSWORD", description="mysql password"
    )
    mysql_database: str = Field(
        default=None, alias="MYSQL_DATABASE", description="mysql database"
    )
    # redis配置
    redis_host: str = Field(
        default="localhost", alias="REDIS_HOST", description="redis host"
    )
    redis_port: int = Field(
        default=6379, alias="REDIS_PORT", description="redis port"
    )
    redis_password: str = Field(
        default=None, alias="REDIS_PASSWORD", description="redis password"
    )
    redis_db: int = Field(default=0, alias="REDIS_DB", description="redis db")

    @property
    def get_mongo_config(self) -> dict:
        return {
            "host": self.mongo_host,
            "port": self.mongo_port,
            "username": self.mongo_user,
            "password": self.mongo_password,
            "database": self.mongo_database,
            "pool_size": self.mongo_pool_size,
        }

    @property
    def get_mysql_config(self) -> dict:
        return {
            "host": self.mysql_host,
            "port": self.mysql_port,
            "user": self.mysql_user,
            "password": self.mysql_password,
            "database": self.mysql_database,
        }

    @property
    def get_redis_config(self) -> dict:
        return {
            "host": self.redis_host,
            "port": self.redis_port,
            "password": self.redis_password,
            "db": self.redis_db
        }

    # 杏仁解析配置
    parser_base_url: str = Field(
        default="", alias="PARSER_BASE_URL", description="杏仁解析服务地址"
    )
    parser_api_key: str = Field(
        default="", alias="PARSER_API_KEY", description="杏仁解析服务api key"
    )
    # 启用llm调试监控
    enable_monitor: bool = Field(default=False, alias="ENABLE_MONITOR", description="启用llm调试监控")

    class Config:
        env_file_encoding = "utf-8"


def load_yaml_config(config_path: str = "configs/base.yaml") -> dict:
    with open(config_path, "r", encoding="utf-8") as f:
        return yaml.safe_load(f)


@lru_cache
def get_settings() -> Settings:
    return Settings()


settings = get_settings()
