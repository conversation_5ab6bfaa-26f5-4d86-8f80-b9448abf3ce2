# -*- encoding: utf-8 -*-
"""
@File   :__init__.py
@Time   :2025/6/20 14:51
<AUTHOR>
"""
import os
from loguru import logger
from dotenv import load_dotenv
from pathlib import Path


# 递归向上找项目根目录，直到发现.git或特定文件
def find_project_root(current: Path) -> Path:
    for parent in current.parents:
        if (parent / "app").exists():  # 你项目根目录下有 app 文件夹
            return parent
    return current


BASE_DIR = find_project_root(Path(__file__).resolve())
# 构建 .env 文件的完整路径
BASE_ENV_PATH = BASE_DIR / "app" / "configs" / "env"

# 外部ENV环境，默认是dev
ENV = os.getenv("ENV", "dev")


def load_env():
    env_file_mapping = {
        "dev": BASE_ENV_PATH / ".env.dev",
        "test": BASE_ENV_PATH / ".env.test",
        "prod": BASE_ENV_PATH / ".env.prod",
    }

    env_file = env_file_mapping.get(ENV, BASE_ENV_PATH / ".env.dev")
    env_doc_file = BASE_ENV_PATH / ".env.doc"
    env_auth_file = BASE_ENV_PATH / ".env.auth_premission"
    load_dotenv(env_file)
    load_dotenv(env_doc_file)
    load_dotenv(env_auth_file)
    logger.info(f"app env base path: {BASE_ENV_PATH}")
    logger.info(f"app env main path: {env_file}")
    logger.info(f"app env doc path: {env_file}")
    logger.info(f"app env auth path: {env_auth_file}")


load_env()
