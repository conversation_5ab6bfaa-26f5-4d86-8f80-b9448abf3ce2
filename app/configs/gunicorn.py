# -*- coding: utf-8 -*-
"""
@Time ： 2023/8/22 16:02
@Auth ： qu ming
@File ：gunicorn.py.py
@IDE ：PyCharm
"""

daemon = True  # 是否守护
bind = '0.0.0.0:9000'  # 绑定
pidfile = 'gunicorn.pid'  # pid文件地址
chdir = '.'  # 项目地址
worker_class = 'uvicorn.workers.UvicornWorker'
workers = 8
threads = 2
loglevel = 'debug'  # 日志级别
access_log_format = '%(t)s %(p)s %(h)s "%(r)s" %(s)s %(L)s %(b)s %(f)s" "%(a)s"'
accesslog = "gunicorn_access.log"
errorlog = "gunicorn_error.log"
