"""
文档处理相关配置
"""

from pydantic import Field
from functools import lru_cache
from pydantic_settings import BaseSettings
from app.configs.config import get_settings


class DocSettings(BaseSettings):
    default_upload_dir: str = Field(..., alias="UPLOAD_DIR")
    default_max_token_len: int = 60000
    default_enable_quantization: bool = True
    default_auto_vectorize: bool = True

    doc_collection: str = "doccontext"
    doc_chunk_collection: str = "doc_chunks"

    default_splitter_type: str = "recursive"
    default_chunk_size: int = 1024

    default_chunk_overlap: int = 64

    # 任务超时和重试配置
    max_retry_count: int = 2  # 最大重试次数
    ocr_task_timeout_hours: int = 4  # OCR任务超时时间（小时）
    text_task_timeout_minutes: int = 20  # TEXT任务超时时间（分钟）
    timeout_check_interval_minutes: int = 10  # 定时检查超时任务的时间间隔（分钟）

    @property
    def ocr_task_timeout_seconds(self) -> int:
        return self.ocr_task_timeout_hours * 3600

    @property
    def text_task_timeout_seconds(self) -> int:
        return self.text_task_timeout_minutes * 60

    @property
    def timeout_check_interval_seconds(self) -> int:
        return self.timeout_check_interval_minutes * 60

    class Config:
        env_file_encoding = "utf-8"

    @property
    def get_mongo_config(self) -> dict:
        # 复用全局 settings
        base = get_settings()
        return {
            "host": base.mongo_host,
            "port": base.mongo_port,
            "username": base.mongo_user,
            "password": base.mongo_password,
            "database": base.mongo_database,
            "pool_size": base.mongo_pool_size,
        }


@lru_cache
def get_doc_settings() -> DocSettings:
    return DocSettings()


doc_settings = get_doc_settings()
