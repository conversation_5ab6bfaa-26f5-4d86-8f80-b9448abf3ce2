# -*- encoding: utf-8 -*-
"""
@File   :init_db.py
@Time   :2025/6/16 15:40
<AUTHOR>
"""
# scripts/init_tables.py
from loguru import logger


from app.core.db.manager import DBManager
from app.core.db.base import Base
from app.core.db.models import *  # 确保所有模型已注册到 Base.metadata


def init_all_tables():
    """初始化项目中所有数据库表"""
    engine = DBManager.get_mysql()._pool._engine
    logger.info("🔧 正在创建数据库表...")
    Base.metadata.create_all(bind=engine)
    logger.info("✅ 所有表创建完成。")


if __name__ == "__main__":
    from app.configs.config import settings

    DBManager.initialize(mysql_config=settings.get_mysql_config)

    init_all_tables()
