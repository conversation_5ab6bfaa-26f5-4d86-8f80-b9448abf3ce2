# -*- encoding: utf-8 -*-
"""
@File   :sync2async.py
@Time   :2025/6/10 18:52
<AUTHOR>
"""
import asyncio
from typing import Iterable, AsyncGenerator, TypeVar

# 定义一个独一无二的“哨兵”对象，它只用来表示迭代结束
_sentinel = object()

# 为类型提示定义一个类型变量
T = TypeVar("T")


def _get_next_or_sentinel(sync_iterator):
    """
    这是一个在后台线程中运行的同步辅助函数。
    它会安全地获取下一个元素，如果迭代结束了，就返回哨兵对象。
    这个函数本身永远不会抛出 StopIteration。
    """
    try:
        # 尝试获取下一个元素
        return next(sync_iterator)
    except StopIteration:
        # 如果捕获到 StopIteration，说明迭代结束，返回哨兵
        return _sentinel


async def sync_to_async_generator(
    sync_iterable: Iterable[T],
) -> AsyncGenerator[T, None]:
    """
    一个健壮的、能将同步生成器安全地包装为异步生成器的函数。
    它使用“哨兵”模式来防止 StopIteration 异常泄漏到 asyncio 中。
    """
    sync_iterator = iter(sync_iterable)
    while True:
        # 在后台线程中调用我们那个安全的辅助函数
        item = await asyncio.to_thread(_get_next_or_sentinel, sync_iterator)

        # 检查返回的值是不是我们的哨兵对象
        if item is _sentinel:
            # 如果是哨兵，说明同步生成器已经耗尽，安全地退出循环
            break

        # 如果不是哨兵，说明是一个有效的数据，将其 yield 出去
        yield item
