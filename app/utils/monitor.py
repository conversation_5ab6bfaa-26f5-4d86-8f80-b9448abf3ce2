# -*- encoding: utf-8 -*-
"""
@File   :monitor.py
@Time   :2025/6/10 13:56
<AUTHOR>
"""
from datetime import datetime
from typing import List

import requests
from loguru import logger

from app.configs.config import settings


def send_wecom_message(content: str, webhook_url: str = None):
    """
    推送消息到企业微信机器人（默认使用 env 中配置）
    """
    try:
        webhook_url = webhook_url or settings.wecom_webhook_url
        payload = {"msgtype": "markdown", "markdown": {"content": content}}
        resp = requests.post(webhook_url, json=payload, timeout=5)
        logger.info(f"企业微信推送成功: {resp.json()}")
        if resp.status_code != 200:
            logger.warning(f"企业微信推送失败: {resp.text}")
    except Exception as e:
        logger.exception(f"企业微信推送异常: {e}")


def send_monitor_event(title: str, content: str, tags: List[str] = None):
    try:
        tag_str = " | ".join(tags) if tags else ""
        msg = f"""### 🚨 小杏仁监控告警  
        **标题**：{title}  
        **标签**：{tag_str}  
        **时间**：<font color="comment">{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</font>  

        {content}
        """
        # send_wecom_message(msg)
    except Exception as e:
        logger.error(f"监控上报失败: {e}")
