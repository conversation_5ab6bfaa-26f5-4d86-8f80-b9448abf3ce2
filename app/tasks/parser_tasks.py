# -*- encoding: utf-8 -*-
"""
@File   :parser_tasks.py
@Time   :2025/6/16 19:13
<AUTHOR>
"""
from datetime import datetime

from apscheduler.schedulers.background import BackgroundScheduler
from loguru import logger

from app.configs.config import settings
from app.core.parser_adapter.adapter import ParserAdapter
from app.core.db.manager import DBManager
from app.core.db.models.parse import ParseTask
from app.services.maxkb_service import upload_text_to_maxkb
from app.v1.db.file_info import update_file

# APScheduler 后台任务：每隔10秒检查一次 pending 的解析任务
scheduler = BackgroundScheduler()


@scheduler.scheduled_job("interval", seconds=10)
def check_parse_tasks():
    adapter = ParserAdapter(
        parser_host=settings.parser_base_url,
        api_key=settings.parser_api_key
    )

    with DBManager.get_mysql().session() as db:
        tasks = db.query(ParseTask).filter(ParseTask.status == "pending").all()
        for task in tasks:
            try:
                result = adapter.get_parse_result(task.doc_id, content_type="json")

                # 情况1：请求失败
                if not result.get("success"):
                    if result.get("error") == "pending":
                        logger.info(f"文档尚未解析完成: doc_id={task.doc_id}")
                        continue
                    error_msg = result.get("error", "获取解析结果失败")
                    task.status = "error"
                    task.updated_at = datetime.now()
                    task.error_message = error_msg

                    # 通知 update_file，标记为失败
                    update_file(task.username, task.file_name, task.maxkb_id, {
                        "status": "error",
                        "reason": error_msg
                    })

                    db.commit()
                    logger.error(f"❌ 获取解析结果失败: doc_id={task.doc_id}, error={error_msg}")
                    continue

                # 情况2：结果为空
                markdown_content = result.get("markdown_text", "")
                if not markdown_content:
                    error_msg = "解析结果为空"
                    task.status = "error"
                    task.updated_at = datetime.now()
                    task.error_message = error_msg

                    update_file(task.username, task.file_name, task.maxkb_id, {
                        "status": "error",
                        "reason": error_msg
                    })

                    db.commit()
                    logger.warning(f"⚠️ 解析结果为空: doc_id={task.doc_id}")
                    continue

                # 上传到 MaxKB
                filename = f"{task.file_name}.md"
                upload_result = upload_text_to_maxkb(
                    filename=filename,
                    text=markdown_content,
                    maxkb_url=task.maxkb_url,
                    maxkb_id=task.maxkb_id
                )
                logger.info(f"📤 上传结果到 MaxKB: doc_id={task.doc_id}, result={upload_result}")

                if upload_result and upload_result.get("message") == '成功':
                    file_id = upload_result["data"][0]["id"]
                    update_file(task.username, task.file_name, task.maxkb_id, {
                        "file_id": file_id,
                        "status": "done"
                    })
                    task.status = "done"
                    logger.info(f"✅ 任务完成: doc_id={task.doc_id}")
                else:
                    error_msg = "MaxKB上传失败"
                    task.status = "error"
                    task.error_message = error_msg
                    update_file(task.username, task.file_name, task.maxkb_id, {
                        "status": "error",
                        "reason": error_msg
                    })
                    logger.error(f"❌ MaxKB上传失败: doc_id={task.doc_id}")

                task.updated_at = datetime.now()
                db.commit()

            except Exception as e:
                error_msg = str(e)[:500]
                task.status = "error"
                task.updated_at = datetime.now()
                task.error_message = error_msg

                update_file(task.username, task.file_name, task.maxkb_id, {
                    "status": "error",
                    "reason": error_msg
                })

                db.commit()
                logger.exception(f"❌ 异常处理任务: doc_id={task.doc_id}")


def start_parser_scheduler():
    scheduler.start()
    logger.info("解析任务调度器已启动")


def stop_parser_scheduler():
    scheduler.shutdown()
    logger.info("解析任务调度器已停止")
