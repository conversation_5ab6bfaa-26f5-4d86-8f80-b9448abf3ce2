# -*- encoding: utf-8 -*-
"""
@File   :doc_reader_tasks.py
@Time   :2025/6/29
<AUTHOR>
"""
import os
import uuid
import time
from datetime import datetime, timed<PERSON>ta
from typing import Tuple

from apscheduler.schedulers.background import BackgroundScheduler
from loguru import logger

from app.configs.config import settings
from app.configs.document_config import doc_settings
from app.core.doc_reader.adapter import DocReaderAdapter
from app.core.doc_reader.local_parser import LocalDocumentParser
from app.core.db.manager import DBManager
from app.core.db.models.doc_reader_task import DocReaderTask
from app.core.llm.session.document_qa.doc_session import DocSessionManager


def _safe_read_file(file_path: str, max_retries: int = 3, retry_delay: float = 0.5) -> bytes:
    """
    安全读取文件，支持重试机制

    Args:
        file_path: 文件路径
        max_retries: 最大重试次数
        retry_delay: 重试间隔（秒）

    Returns:
        bytes: 文件内容

    Raises:
        FileNotFoundError: 文件不存在或无法访问
    """
    for attempt in range(max_retries + 1):
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 检查是否为文件（而不是目录）
            if not os.path.isfile(file_path):
                raise FileNotFoundError(f"路径不是文件: {file_path}")

            # 检查文件大小（确保文件写入完成）
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                if attempt < max_retries:
                    logger.warning(f"文件大小为0，可能正在写入中，等待重试: {file_path}")
                    time.sleep(retry_delay)
                    continue
                else:
                    raise FileNotFoundError(f"文件为空: {file_path}")

            # 尝试读取文件
            with open(file_path, 'rb') as f:
                file_bytes = f.read()

            logger.info(f"成功读取文件: {file_path}, 大小: {len(file_bytes)} 字节")
            return file_bytes

        except (OSError, IOError, PermissionError) as e:
            if attempt < max_retries:
                logger.warning(f"读取文件失败，第{attempt + 1}次重试: {file_path}, error={e}")
                time.sleep(retry_delay * (2 ** attempt))  # 指数退避
                continue
            else:
                logger.error(f"读取文件最终失败: {file_path}, error={e}")
                raise FileNotFoundError(f"无法读取文件: {file_path}, 原因: {e}")

    raise FileNotFoundError(f"读取文件失败，已达到最大重试次数: {file_path}")


# 文档解读专用调度器（限制并发执行，避免文件访问冲突）
doc_reader_scheduler = BackgroundScheduler(
    job_defaults={'max_instances': 1}  # 限制每个作业最多只能有1个实例同时运行
)


@doc_reader_scheduler.scheduled_job("interval", seconds=5)  # 5秒检查一次，避免与知识库任务冲突
def check_doc_reader_tasks():
    """检查文档解读任务状态"""
    # 处理OCR任务（PDF文件）
    check_ocr_tasks()

    # 处理TEXT任务（其他文档）
    check_text_tasks()


@doc_reader_scheduler.scheduled_job("interval", seconds=doc_settings.timeout_check_interval_seconds)
def check_timeout_tasks_job():
    """定时检查超时任务"""
    logger.info("开始检查超时任务...")
    check_timeout_tasks()
    logger.info("超时任务检查完成")


def check_ocr_tasks():
    """检查OCR类型的任务（PDF文件，通过杏仁解析中心处理）"""
    adapter = DocReaderAdapter(
        parser_host=settings.parser_base_url,
        api_key=settings.parser_api_key
    )

    with DBManager.get_mysql().session() as db:
        # 查询 pending 和 processing 状态的OCR任务
        tasks = db.query(DocReaderTask).filter(
            DocReaderTask.parse_type == "OCR",
            DocReaderTask.status.in_(["pending", "processing"])
        ).all()

        if not tasks:
            return

        logger.info(f"找到 {len(tasks)} 个待处理的OCR任务")

        for task in tasks:
            try:
                logger.info(f"检查OCR任务: task_id={task.task_id}, parser_doc_id={task.parser_doc_id}")

                # 原子性地更新任务状态为processing，防止重复处理
                if task.status == "pending":
                    # 使用原子操作更新状态
                    update_result = db.query(DocReaderTask).filter(
                        DocReaderTask.task_id == task.task_id,
                        DocReaderTask.status == "pending"
                    ).update({
                        "status": "processing",
                        "updated_at": datetime.now()
                    })

                    if update_result == 0:
                        logger.info(f"OCR任务状态已被其他实例更新，跳过: task_id={task.task_id}")
                        continue

                    db.commit()
                    task.status = "processing"  # 更新本地对象状态
                    logger.info(f"OCR任务状态已更新为processing: task_id={task.task_id}")
                elif task.status == "processing":
                    logger.info(f"OCR任务已在处理中，继续处理: task_id={task.task_id}")

                # 检查是否已提交OCR任务
                if not task.parser_doc_id:
                    logger.info(f"OCR任务尚未提交，开始提交: task_id={task.task_id}")

                    # 从任务元数据中获取临时文件路径
                    if not task.meta_info or "temp_file_path" not in task.meta_info:
                        error_msg = "文档解析任务元数据中缺少临时文件路径"
                        adapter.update_task_status(
                            task.task_id,
                            "failed",
                            error_message=error_msg
                        )
                        _update_file_status(task.session_id, task.task_id, "failed", error_msg)
                        logger.error(f"OCR任务提交失败: task_id={task.task_id}, error={error_msg}")
                        continue

                    # 从文件读取内容（使用重试机制）
                    temp_file_path = task.meta_info["temp_file_path"]
                    try:
                        file_bytes = _safe_read_file(temp_file_path)
                        logger.info(f"从文件读取数据: {temp_file_path}, 大小: {len(file_bytes)} 字节")

                    except Exception as e:
                        error_msg = f"读取文件失败: {e}"
                        adapter.update_task_status(
                            task.task_id,
                            "failed",
                            error_message=error_msg
                        )
                        _update_file_status(task.session_id, task.task_id, "failed", "读取文件失败")
                        logger.error(f"OCR任务提交失败: task_id={task.task_id}, error={error_msg}")
                        continue

                    # 提交OCR任务
                    try:
                        parser_doc_id = _submit_ocr_task_to_almond(
                            task.task_id,
                            task.filename,
                            file_bytes,
                            adapter
                        )

                        if parser_doc_id:
                            # 更新任务的parser_doc_id
                            with DBManager.get_mysql().session() as db:
                                db_task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task.task_id).first()
                                if db_task:
                                    db_task.parser_doc_id = parser_doc_id
                                    db.commit()
                            task.parser_doc_id = parser_doc_id
                            logger.info(f"OCR任务提交成功: task_id={task.task_id}, parser_doc_id={parser_doc_id}")
                            # 提交成功后继续下一个任务，等待下次检查获取结果
                            continue
                        else:
                            error_msg = "提交OCR任务失败，未获取到parser_doc_id"
                            adapter.update_task_status(
                                task.task_id,
                                "failed",
                                error_message=error_msg
                            )
                            _update_file_status(task.session_id, task.task_id, "failed", error_msg)
                            logger.error(f"OCR任务提交失败: task_id={task.task_id}, error={error_msg}")
                            continue

                    except Exception as e:
                        error_msg = f"提交OCR任务异常: {e}"
                        adapter.update_task_status(
                            task.task_id,
                            "failed",
                            error_message=error_msg
                        )
                        _update_file_status(task.session_id, task.task_id, "failed", "文档解析失败")
                        logger.error(f"OCR任务提交异常: task_id={task.task_id}, error={error_msg}")
                        continue

                # 获取解析结果（只有已提交的任务才会到这里）
                result = adapter.get_doc_reader_result(task.parser_doc_id)

                if not result.get("success"):
                    if result.get("error") == "pending":
                        logger.info(f"OCR任务仍在处理中: task_id={task.task_id}")
                        continue

                    # 解析失败
                    error_msg = result.get("error", "获取解析结果失败")
                    adapter.update_task_status(
                        task.task_id,
                        "failed",
                        error_message=error_msg
                    )

                    # 更新MongoDB中的文件状态
                    _update_file_status(task.session_id, task.task_id, "failed", error_msg)

                    logger.error(f"OCR任务失败: task_id={task.task_id}, error={error_msg}")
                    continue

                # 解析成功，获取内容
                markdown_content = result.get("markdown_text", "")
                if not markdown_content:
                    error_msg = "文档解析结果为空，可能是文档内容无法识别或文档已损坏"
                    adapter.update_task_status(
                        task.task_id,
                        "failed",
                        error_message=error_msg
                    )
                    _update_file_status(task.session_id, task.task_id, "failed", error_msg)
                    logger.warning(f"OCR解析结果为空: task_id={task.task_id}")
                    continue

                # 处理解析结果：直接保存到会话并分块
                try:
                    doc_id, chunk_count = _save_ocr_content_to_session(task, markdown_content)

                    if doc_id:
                        # 更新任务的doc_id和状态
                        with DBManager.get_mysql().session() as db:
                            db_task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task.task_id).first()
                            if db_task:
                                db_task.doc_id = doc_id
                                db_task.status = "completed"
                                db_task.completed_at = datetime.now()
                                db_task.updated_at = datetime.now()
                                db_task.chunks_count = chunk_count
                                db.commit()

                        # 更新MongoDB中的文件状态
                        _update_file_status(task.session_id, task.task_id, "completed")

                        # 清理临时文件
                        # _cleanup_temp_file(task)

                        logger.info(f"✅ OCR任务完成: task_id={task.task_id}, doc_id={doc_id}")
                    else:
                        error_msg = "保存OCR解析结果失败"
                        adapter.update_task_status(
                            task.task_id,
                            "failed",
                            error_message=error_msg
                        )
                        _update_file_status(task.session_id, task.task_id, "failed", error_msg)
                        logger.error(f"OCR任务失败: task_id={task.task_id}, error={error_msg}")

                except Exception as e:
                    error_msg = f"处理OCR解析结果异常: {e}"
                    adapter.update_task_status(
                        task.task_id,
                        "failed",
                        error_message=error_msg
                    )
                    _update_file_status(task.session_id, task.task_id, "failed", error_msg)
                    logger.error(f"OCR任务异常: task_id={task.task_id}, error={error_msg}")

            except Exception as e:
                error_msg = str(e)[:500]
                adapter.update_task_status(
                    task.task_id,
                    "failed",
                    error_message=error_msg
                )
                _update_file_status(task.session_id, task.task_id, "failed", "文档解析失败")
                logger.exception(f"❌ OCR任务异常: task_id={task.task_id}")


def check_text_tasks():
    """检查TEXT类型的任务（非PDF文件，本地处理）"""
    with DBManager.get_mysql().session() as db:
        # 只获取pending状态的TEXT任务，避免重复处理processing状态的任务
        pending_tasks = db.query(DocReaderTask).filter(
            DocReaderTask.parse_type == "TEXT",
            DocReaderTask.status == "pending"
        ).all()

        for task in pending_tasks:
            try:
                logger.info(f"发现待处理TEXT任务: task_id={task.task_id}, filename={task.filename}")

                # 不在这里预先更新状态，让LocalDocumentParser负责状态管理
                # 这样可以避免状态管理的竞争条件
                # 先保存文档信息到session（如果还没有doc_id）
                if not task.doc_id:
                    doc_id = _save_file_info_only(task)
                    if not doc_id:
                        logger.error(f"保存文档信息失败: task_id={task.task_id}")
                        # 更新任务状态为失败
                        with DBManager.get_mysql().session() as db:
                            db_task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task.task_id).first()
                            if db_task:
                                db_task.status = "failed"
                                db_task.error_message = "保存文档信息失败"
                                db_task.updated_at = datetime.now()
                                db.commit()
                        _update_file_status(task.session_id, task.task_id, "failed", "保存文档信息失败")
                        continue

                    # 更新任务的doc_id
                    with DBManager.get_mysql().session() as db:
                        db_task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task.task_id).first()
                        if db_task:
                            db_task.doc_id = doc_id
                            db.commit()
                    task.doc_id = doc_id

                # 处理任务（包含解析、分块等所有步骤）
                local_parser = LocalDocumentParser()
                success, error_message = local_parser.process_text_task(task.task_id)

                if success:
                    # 更新MongoDB中的文件状态为completed
                    _update_file_status(task.session_id, task.task_id, "completed")

                    # 清理临时文件
                    # _cleanup_temp_file(task)
                    logger.info(f"✅ TEXT任务完成: task_id={task.task_id}")
                else:
                    # 更新MongoDB中的文件状态为failed，包含错误消息
                    _update_file_status(task.session_id, task.task_id, "failed", error_message)
                    logger.error(f"❌ TEXT任务失败: task_id={task.task_id}, error={error_message}")
                    # 注意：LocalDocumentParser.process_text_task内部已经处理了失败状态更新

            except Exception as e:
                error_msg = str(e)[:500]
                logger.exception(f"❌ TEXT任务异常: task_id={task.task_id}, error={error_msg}")
                # 更新任务状态为失败
                try:
                    with DBManager.get_mysql().session() as db:
                        db_task = db.query(DocReaderTask).filter(DocReaderTask.task_id == task.task_id).first()
                        if db_task:
                            db_task.status = "failed"
                            db_task.error_message = error_msg
                            db_task.updated_at = datetime.now()
                            db.commit()
                    _update_file_status(task.session_id, task.task_id, "failed", f"任务处理异常: {error_msg}")
                except Exception as update_e:
                    logger.error(f"更新TEXT任务失败状态异常: task_id={task.task_id}, error={update_e}")


def _save_file_info_only(task: DocReaderTask) -> str:
    """
    只保存文件信息到session（不进行解析，避免重复解析）

    Args:
        task: 任务对象

    Returns:
        str: doc_id，失败返回空字符串
    """
    try:
        import io
        import uuid
        from app.core.llm.session.document_qa.utils import get_local_datetime

        # 从metadata中获取临时文件路径
        metadata = task.meta_info or {}
        temp_file_path = metadata.get("temp_file_path", "")
        temp_doc_id = metadata.get("temp_doc_id", "")

        if not temp_file_path:
            logger.error(f"任务中没有临时文件路径: task_id={task.task_id}")
            return ""

        # 检查临时文件是否存在
        if not os.path.isfile(temp_file_path):
            logger.error(f"临时文件不存在: {temp_file_path}")
            return ""

        # 生成doc_id
        doc_id = str(uuid.uuid4())

        # 获取文件信息
        file_size = os.path.getsize(temp_file_path)

        # 构建文件信息（不进行解析）
        file_info = {
            "doc_id": doc_id,
            "user_id": task.username,
            "file_name": task.filename,
            "file_path": temp_file_path,  # 保存临时文件路径，供后续解析使用
            "file_type": task.file_type,
            "file_size": file_size,
            "status": "uploaded",  # 标记为已上传，等待解析
            "created_at": get_local_datetime(),
            "updated_at": get_local_datetime(),
            "is_quantized": False,
            "is_vectorized": False,
            "char_count": 0,
        }

        # 保存文件信息到MongoDB
        session_manager = DocSessionManager()
        session_manager.file_manager.collection.insert_one(file_info)

        # 更新MongoDB中的文件记录
        session_manager.session_collection.update_one(
            {"session_id": task.session_id, "files.temp_doc_id": temp_doc_id},
            {
                "$set": {
                    "files.$.doc_id": doc_id,
                }
            }
        )
        logger.info(f"文件信息已保存: task_id={task.task_id}, doc_id={doc_id}")
        return doc_id

    except Exception as e:
        logger.error(f"保存文件信息失败: task_id={task.task_id}, error={e}")
        return ""


def _save_file_to_session(task: DocReaderTask) -> str:
    """
    保存文件到session（异步任务中执行）

    Returns:
        str: doc_id，失败返回空字符串
    """
    try:
        import io

        # 从metadata中获取临时文件路径
        metadata = task.meta_info or {}
        temp_file_path = metadata.get("temp_file_path", "")
        temp_doc_id = metadata.get("temp_doc_id", "")

        if not temp_file_path:
            logger.error(f"任务中没有临时文件路径: task_id={task.task_id}")
            return ""

        # 检查临时文件是否存在
        if not os.path.isfile(temp_file_path):
            logger.error(f"临时文件不存在: {temp_file_path}")
            return ""

        # 读取文件数据
        with open(temp_file_path, 'rb') as f:
            file_bytes = f.read()

        file_like_obj = io.BytesIO(file_bytes)
        logger.info(f"从临时文件读取数据: {temp_file_path}, 大小: {len(file_bytes)} 字节")

        # 保存文件到session
        session_manager = DocSessionManager()
        doc_id = session_manager.save_file_to_session(
            task.username, file_like_obj, task.filename, task.session_id
        )

        # 更新MongoDB中的文件记录
        session_manager.session_collection.update_one(
            {"session_id": task.session_id, "files.temp_doc_id": temp_doc_id},
            {
                "$set": {
                    "files.$.doc_id": doc_id,
                }
            }
        )

        logger.info(f"文件已保存到session: task_id={task.task_id}, doc_id={doc_id}")
        return doc_id

    except Exception as e:
        logger.error(f"保存文件到session失败: task_id={task.task_id}, error={e}")
        return ""


def _submit_ocr_task_to_almond(task_id: str, filename: str, file_bytes: bytes, adapter: DocReaderAdapter) -> str:
    """
    提交OCR任务到杏仁解析中心

    Args:
        task_id: 任务ID
        filename: 文件名
        file_bytes: 文件字节内容
        adapter: 文档解读适配器

    Returns:
        str: parser_doc_id，失败返回空字符串
    """
    import io
    import requests

    try:
        url = f"{adapter.parser_host}/api/v1/document/upload"
        files = {
            "files": (filename, io.BytesIO(file_bytes), "application/octet-stream")
        }
        data = {
            "service_type": "document"
        }

        response = requests.post(url, files=files, data=data, headers=adapter.headers, timeout=60)
        response.raise_for_status()
        parser_result = response.json()

        if parser_result.get("success"):
            parser_doc_id = parser_result["uploaded_files"][0]["document_id"]
            logger.info(f"OCR任务提交成功: task_id={task_id}, parser_doc_id={parser_doc_id}")
            return parser_doc_id
        else:
            logger.error(f"杏仁解析中心返回失败: {parser_result}")
            return ""

    except Exception as e:
        logger.error(f"提交OCR任务到杏仁解析中心失败: task_id={task_id}, error={e}")
        return ""


def _save_ocr_content_to_session(task: DocReaderTask, parsed_content: str) -> Tuple[str, int]:
    """
    保存OCR解析内容到会话

    Args:
        task: 任务对象
        parsed_content: 解析后的内容

    Returns:
        str: doc_id，失败返回空字符串
    """
    try:
        session_manager = DocSessionManager()

        # 获取文件大小
        file_size = task.meta_info.get("file_size", 0) if task.meta_info else 0

        # 保存OCR内容到会话
        doc_id, chunk_num = session_manager.save_file_to_session_with_content(
            user_id=task.username,
            filename=task.filename,
            session_id=task.session_id,
            parsed_content=parsed_content,
            file_type=task.file_type,
            file_size=file_size
        )

        if not doc_id:
            logger.error(f"保存OCR内容到会话失败: task_id={task.task_id}")
            return ""

        # 更新MongoDB中的文件记录
        temp_doc_id = task.meta_info.get("temp_doc_id", "") if task.meta_info else ""
        if temp_doc_id:
            update_data = {
                "files.$.doc_id": doc_id,
                "files.$.file_status": "completed"
            }
            session_manager.session_collection.update_one(
                {"session_id": task.session_id, "files.temp_doc_id": temp_doc_id},
                {"$set": update_data}
            )
            logger.info(f"MongoDB文件记录已更新: temp_doc_id={temp_doc_id}, doc_id={doc_id}")

        logger.info(f"OCR内容已保存到会话: task_id={task.task_id}, doc_id={doc_id}")
        return doc_id, chunk_num

    except Exception as e:
        logger.error(f"保存OCR内容到会话失败: task_id={task.task_id}, error={e}")
        return ""


def _cleanup_temp_file(task: DocReaderTask):
    """
    清理临时文件

    Args:
        task: 任务对象
    """
    try:
        if task.meta_info and "temp_file_path" in task.meta_info:
            temp_file_path = task.meta_info["temp_file_path"]
            if os.path.isfile(temp_file_path):
                os.remove(temp_file_path)
                logger.info(f"临时文件已清理: {temp_file_path}")
            else:
                logger.warning(f"临时文件不存在，无需清理: {temp_file_path}")
    except Exception as e:
        logger.error(f"清理临时文件失败: task_id={task.task_id}, error={e}")


def _update_file_status(session_id: str, task_id: str, status: str, error_message: str = None):
    """更新MongoDB中的文件状态"""
    try:
        session_manager = DocSessionManager()
        update_data = {"files.$.file_status": status}

        # 如果提供了错误消息，同时更新错误消息字段
        if error_message:
            update_data["files.$.error_message"] = error_message

        session_manager.session_collection.update_one(
            {"session_id": session_id, "files.task_id": task_id},
            {"$set": update_data}
        )

        if error_message:
            logger.info(f"文件状态已更新: task_id={task_id}, status={status}, error_message={error_message}")
        else:
            logger.info(f"文件状态已更新: task_id={task_id}, status={status}")
    except Exception as e:
        logger.error(f"更新文件状态失败: task_id={task_id}, error={e}")


def check_timeout_tasks():
    """检查并重置超时的processing任务"""
    try:
        current_time = datetime.now()
        total_reset_count = 0
        total_failed_count = 0

        # 分别处理OCR和TEXT任务的超时检查
        timeout_configs = [
            ("OCR", doc_settings.ocr_task_timeout_seconds),
            ("TEXT", doc_settings.text_task_timeout_seconds)
        ]

        with DBManager.get_mysql().session() as db:
            for parse_type, timeout_seconds in timeout_configs:
                # 计算超时阈值
                timeout_threshold = current_time - timedelta(seconds=timeout_seconds)

                # 查询超时的processing任务
                timeout_tasks = db.query(DocReaderTask).filter(
                    DocReaderTask.parse_type == parse_type,
                    DocReaderTask.status == "processing",
                    DocReaderTask.updated_at < timeout_threshold
                ).all()

                reset_count = 0
                failed_count = 0

                for task in timeout_tasks:
                    # 计算任务已运行时间
                    running_time = current_time - task.updated_at
                    running_seconds = int(running_time.total_seconds())

                    if task.retry_count < doc_settings.max_retry_count:
                        # 重置为pending，增加重试计数
                        _reset_task_to_pending(db, task, timeout_seconds)
                        reset_count += 1
                        logger.warning(
                            f"🔄 任务超时重置: task_id={task.task_id}, parse_type={parse_type}, "
                            f"运行时间={running_seconds}秒, retry_count={task.retry_count + 1}/{doc_settings.max_retry_count}, "
                            f"filename={task.filename}"
                        )
                    else:
                        # 超过重试次数，标记为失败
                        _mark_task_as_failed(db, task, f"任务超时且超过最大重试次数({doc_settings.max_retry_count})")
                        failed_count += 1
                        logger.error(
                            f"❌ 任务超时失败: task_id={task.task_id}, parse_type={parse_type}, "
                            f"运行时间={running_seconds}秒, 已重试{task.retry_count}次, filename={task.filename}"
                        )

                if timeout_tasks:
                    logger.info(f"📊 {parse_type}任务超时处理: 重置{reset_count}个, 失败{failed_count}个")
                    total_reset_count += reset_count
                    total_failed_count += failed_count

            db.commit()

            # 总结日志
            if total_reset_count > 0 or total_failed_count > 0:
                logger.info(f"⏰ 超时任务处理完成: 总重置{total_reset_count}个, 总失败{total_failed_count}个")

    except Exception as e:
        logger.error(f"❌ 检查超时任务失败: {e}")


def _reset_task_to_pending(db, task: DocReaderTask, timeout_seconds: int):
    """重置任务为pending状态"""
    try:
        # 更新任务状态
        task.status = "pending"
        task.retry_count += 1
        task.updated_at = datetime.now()
        task.error_message = f"任务超时重置(超时时间:{timeout_seconds}秒,重试次数:{task.retry_count})"

        # 同时更新MongoDB中的文件状态
        _update_file_status(task.session_id, task.task_id, "pending", task.error_message)

        logger.info(f"任务已重置为pending: task_id={task.task_id}")

    except Exception as e:
        logger.error(f"重置任务失败: task_id={task.task_id}, error={e}")


def _mark_task_as_failed(db, task: DocReaderTask, error_message: str):
    """标记任务为失败状态"""
    try:
        # 更新任务状态
        task.status = "failed"
        task.updated_at = datetime.now()
        task.error_message = error_message

        # 同时更新MongoDB中的文件状态
        _update_file_status(task.session_id, task.task_id, "failed", error_message)

        logger.info(f"任务已标记为失败: task_id={task.task_id}, error={error_message}")

    except Exception as e:
        logger.error(f"标记任务失败状态失败: task_id={task.task_id}, error={e}")


def start_doc_reader_scheduler():
    """启动文档解读调度器"""
    doc_reader_scheduler.start()
    logger.info("文档解读任务调度器已启动")


def stop_doc_reader_scheduler():
    """停止文档解读调度器"""
    doc_reader_scheduler.shutdown()
    logger.info("文档解读任务调度器已停止")
