# Python 缓存文件
__pycache__/
*.py[cod]
*.pyo
*.pyd

# 虚拟环境
.venv/
venv/
ENV/
env/

# IDE配置
.vscode/
.idea/

# 环境配置
*.env
*.env.*

# 日志文件
*.log
logs/
auth_permission/logs/
*.bak

# 数据缓存与临时文件
*.db
*.sqlite3
*.tmp
tmp/
data/

# Jupyter Notebook 检查点
.ipynb_checkpoints/

# Mac 和 Windows 系统文件
.DS_Store
Thumbs.db

# 编译相关
build/
dist/
*.egg-info/

# 测试临时文件
test_*.py
coverage.*
htmlcov/
.tox/
.nox/
.cache/
pytest_cache/

app/tmp/
app/data/
app/data/uploads/
