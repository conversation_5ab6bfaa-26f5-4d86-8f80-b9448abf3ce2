[project]
name = "aicenter"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "aiofiles==24.1.0",
    "aiohappyeyeballs==2.5.0",
    "aiohttp==3.11.13",
    "aiosignal==1.3.2",
    "annotated-types==0.7.0",
    "anyio==4.8.0",
    "async-timeout==4.0.3",
    "attrs==25.1.0",
    "backoff==2.2.1",
    "beautifulsoup4==4.13.3",
    "bs4==0.0.2",
    "certifi==2025.1.31",
    "cffi==1.17.1",
    "chardet==5.2.0",
    "charset-normalizer==3.4.1",
    "click==8.1.8",
    "cryptography==44.0.2",
    "dataclasses-json==0.6.7",
    "distro==1.9.0",
    "dnspython==2.7.0",
    "emoji==2.14.1",
    "et-xmlfile==2.0.0",
    "eval-type-backport==0.2.2",
    "exceptiongroup==1.2.2",
    "fastapi==0.115.11",
    "filetype==1.2.0",
    "frozenlist==1.5.0",
    "greenlet==3.1.1",
    "grpcio==1.67.1",
    "h11==0.14.0",
    "html5lib==1.1",
    "httpcore==1.0.7",
    "httpx==0.28.1",
    "httpx-sse==0.4.0",
    "humanize==4.12.2",
    "idna==3.10",
    "jieba==0.42.1",
    "jiter==0.9.0",
    "joblib==1.4.2",
    "jsonpatch==1.33",
    "jsonpointer==3.0.0",
    "langchain==0.3.20",
    "langchain-community==0.3.19",
    "langchain-core==0.3.56",
    "langchain-ollama==0.3.2",
    "langchain-openai==0.3.14",
    "langchain-text-splitters==0.3.6",
    "langdetect==1.0.9",
    "langsmith==0.3.11",
    "lxml==5.3.2",
    "marshmallow==3.26.1",
    "mongoengine==0.29.1",
    "multidict==6.1.0",
    "mypy-extensions==1.0.0",
    "nest-asyncio==1.6.0",
    "nltk==3.9.1",
    "numpy==2.0.2",
    "olefile==0.47",
    "ollama==0.4.8",
    "openai==1.76.0",
    "openpyxl==3.1.5",
    "orjson==3.10.15",
    "packaging==24.2",
    "pandas==2.2.3",
    "propcache==0.3.0",
    "protobuf==6.30.2",
    "psutil==7.0.0",
    "pycparser==2.22",
    "pycryptodome==3.22.0",
    "pydantic==2.10.6",
    "pydantic-core==2.27.2",
    "pydantic-settings==2.8.1",
    "pydub==0.25.1",
    "pyjwt==2.10.1",
    "pymilvus==2.5.7",
    "pymongo==4.11.2",
    "pymupdf==1.25.3",
    "pymysql==1.1.1",
    "pypdf==5.4.0",
    "pypdf2==3.0.1",
    "python-dateutil==2.9.0.post0",
    "python-docx==1.1.2",
    "python-dotenv==1.0.1",
    "python-iso639==2025.2.18",
    "python-magic==0.4.27",
    "python-multipart==0.0.20",
    "python-oxmsg==0.0.2",
    "pytz==2025.1",
    "pyyaml==6.0.2",
    "rapidfuzz==3.13.0",
    "redis==5.2.1",
    "regex==2024.11.6",
    "requests==2.32.3",
    "requests-toolbelt==1.0.0",
    "sentry-sdk==2.22.0",
    "six==1.17.0",
    "sniffio==1.3.1",
    "soupsieve==2.6",
    "speechrecognition==3.14.1",
    "sqlalchemy==2.0.38",
    "sseclient==0.0.27",
    "sseclient-py==1.7.2",
    "starlette==0.46.0",
    "tenacity==9.0.0",
    "tiktoken==0.9.0",
    "tqdm==4.67.1",
    "typing-extensions==4.12.2",
    "typing-inspect==0.9.0",
    "typing-inspection==0.4.0",
    "tzdata==2025.1",
    "ujson==5.10.0",
    "unstructured==0.17.2",
    "unstructured-client==0.32.3",
    "urllib3==2.3.0",
    "uvicorn==0.34.0",
    "webencodings==0.5.1",
    "wrapt==1.17.2",
    "yarl==1.18.3",
    "zstandard==0.23.0",
]
