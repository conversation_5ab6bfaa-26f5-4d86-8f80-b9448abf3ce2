# -*- encoding: utf-8 -*-
"""
@File    :main.py
@Time    :2024/3/26 15:31
<AUTHOR>
"""
import os

import sentry_sdk
from fastapi import FastAPI
from loguru import logger
from pyfiglet import Figlet
from colorama import Fore
from contextlib import asynccontextmanager

from app.configs.config import settings
from app.core.logger import init_logger
from app.tasks import init_scheduler_tasks
from app.v1.routers import register_v1_routes
from app.api import register_routes
from app.core.llm.session.base import SessionManager
from app.tasks.parser_tasks import start_parser_scheduler
from starlette.middleware.cors import CORSMiddleware

from auth_permission.core.init_app import register_exceptions

__version__ = settings.version

os.environ["http_proxy"] = ""
os.environ["https_proxy"] = ""
os.environ["no_proxy"] = "localhost,127.0.0.1,.bdo.,.bdo.com"


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    自定义生命周期
    """
    # 初始化日志
    init_logger()
    logger.info(f"Starting  v{__version__}...")
    f = Figlet(font="slant")
    logger.info("\n" + Fore.YELLOW + f.renderText(settings.app_name))
    # 初始化会话管理器
    SessionManager()
    yield
    logger.info("Shutting down...")


app = FastAPI(
    title="立信AI中心",
    version="1.0.0",
    lifespan=lifespan,
)

origins = ["http://localhost", "http://localhost:8888", "http://127.0.0.1:80"]

# 3、配置 CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # 允许访问的源
    allow_credentials=True,  # 支持 cookie
    allow_methods=["*"],  # 允许使用的请求方法
    allow_headers=["*"],  # 允许携带的 Headers
)

sentry_sdk.init(
    dsn="http://3b5282b0331c259fbdbb4024a00b77b0@************:9000/2",
    # Set traces_sample_rate to 1.0 to capture 100%
    # of transactions for performance monitoring.
    # We recommend adjusting this value in production,
    # traces_sample_rate=1.0,
    send_default_pii=True,
)

# 注册异常中间件
register_exceptions(app)
# 注册 v1 路由
register_v1_routes(app)
# 注册 api 路由
register_routes(app)

# 初始化解析任务调度器
init_scheduler_tasks()


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
