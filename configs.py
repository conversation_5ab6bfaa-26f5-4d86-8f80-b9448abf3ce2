# -*- encoding: utf-8 -*-
"""
@File    : configs.py
@Time    : 2023/4/6 15:30
<AUTHOR> Qu ming
"""
import os
import platform

SECRET = 'HAPFqlyPAnhx1MlX4Bqpa_wtACGXXwlvNa9Jt8knxyc'

ID = 'wx2b1e95bda2a7b974'

# db
mongodb_info = {
    'db': 'chatbot',
    'host': '************',
    'port': 27017,
}

DB_HOST = "************"
DB_PORT = 5188
DB_USER = "dev"
DB_PASSWORD = "dev@2022"
DB_NAME = "bdo_xxr2"


USEast_api_key = '********************************'

# azure_api
api_base = 'https://useast1-openai-xxr.openai.azure.com/'

token_url = "http://***********:9999/base/app/login"

data = {
    'key': "b5f7e514-6f44-4548-96b9-b74017cc272f",
    'secret': "abb5e52f-4858-4f3a-a485-1631d72c0b07"
}


company_word = os.path.join(os.getcwd(), 'company.txt')

# 存放txt的目录
if platform.system() == 'Windows':
    print('ddddddddddddd')
    txt_path = r'D:\traing_law\files'
elif platform.system() == 'Linux':
    txt_path = '/home/<USER>/python/files'

BASE_URL = 'https://sacpuat.bdo.com.cn/Faith/SacpApi/receive'

# 获取token
token_url = "http://***********:9999/base/app/login"

# redis
host = '************'

password = '4ms9uS1o0HadRICS'

special_robot_name = ['IT_style', 'financial_data']

ollama_bge_base = "http://*************:11434"

connection_args = {"host": "*************", "port": "19530"}

pu_ip = 'http://*************:8000'

BASE_XM_URL = "http://************:8891"  # maxkb:sacp项目

BASE_GR_URL = "http://************:8891"  # maxkb:个人库

MONGO_URI = "mongodb://************:27017/"

chat_api_base = 'http://idc-vllm.bdo.com.cn/v1'

MONGO_DB = "chatbot"

OPENAPI_BASE_URL = "http://***********:13702"

OPENAPI_APIKEY = "f23d3775374d4836a13e063304a8e22d"

openai_api_key = 'q5A53chTPgb4wkMHZhZUH2c1wd4ph6Aw'

QUERY_LAW_TITLE_PATH = "/api/external/laws/regulations/v1/queryLawTitle"

QUERY_LAW_CONTENT_PATH = "/api/external/laws/regulations/v1/queryContentLaw"
