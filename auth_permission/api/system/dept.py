#!/usr/bin/env python
# -*- coding: utf-8 -*-

import random
from fastapi import APIRouter, Depends, Query
from fastapi.responses import JSONResponse

from auth_permission.core.params import DeptQueryParams
from auth_permission.core.router_class import Operation<PERSON><PERSON><PERSON>oute
from auth_permission.core.dependencies import AuthPermission
from auth_permission.services.system import DeptService
from auth_permission.utils.response import SuccessResponse
from auth_permission.schemas.system import (
    Auth,
    DeptCreate,
    DeptUpdate,
    DeptBatchSetAvailable
)


router = APIRouter(route_class=OperationLogRoute, prefix="/admin/api/system/dept", tags=["部门模块"])


@router.get("/list", summary="查询部门", description="查询部门")
async def get_dept_list(
        auth: Auth = Depends(AuthPermission(permissions=["system:dept:query"])),
        dept_query: DeptQueryParams = Depends()
) -> JSONResponse:
    search = dept_query.__dict__
    data = await DeptService.get_dept_list(search, auth)
    return SuccessResponse(data)


@router.get("/detail", summary="查询部门详情", description="查询部门详情")
async def get_dept_detail(
        id: int = Query(..., description="部门ID"),
        auth: Auth = Depends(AuthPermission(permissions=["system:dept:query"])),
) -> JSONResponse:
    data = await DeptService.get_dept_detail(id, auth)
    return SuccessResponse(data)


@router.get("/options", summary="查询部门选项", description="查询部门选项")
async def get_dept_options(
        auth: Auth = Depends(AuthPermission(permissions=["system:dept:options"])),
) -> JSONResponse:
    data = await DeptService.get_dept_options(auth)
    return SuccessResponse(data)


@router.post("/create", summary="创建部门", description="创建部门")
async def create_dept(
        dept_in: DeptCreate,
        auth: Auth = Depends(AuthPermission(permissions=["system:dept:create"])),
) -> JSONResponse:
    numbers = random.sample(range(10), 8)
    dept_id = int(''.join(map(str, numbers))) + 1
    dept_in = dept_in.model_copy(update={"dept_id": dept_id})
    data = await DeptService.create_dept(dept_in, auth)
    return SuccessResponse(data, msg="创建成功")


@router.post("/update", summary="修改部门", description="修改部门")
async def update_dept(
        dept_in: DeptUpdate,
        auth: Auth = Depends(AuthPermission(permissions=["system:dept:update"])),
) -> JSONResponse:
    data = await DeptService.update_dept(dept_in, auth)
    return SuccessResponse(data, msg="修改成功")


@router.post("/delete", summary="删除部门", description="删除部门")
async def delete_dept(
        id: int = Query(..., description="部门ID"),
        auth: Auth = Depends(AuthPermission(permissions=["system:dept:delete"])),
) -> JSONResponse:
    await DeptService.delete_dept(id, auth)
    return SuccessResponse(msg="删除成功")


@router.post("/batch/enable", summary="批量启用菜单", description="批量启用菜单")
async def batch_enabled_dept(
        data: DeptBatchSetAvailable,
        auth: Auth = Depends(AuthPermission(permissions=["system:dept:update"])),
) -> JSONResponse:
    await DeptService.enable_dept(data.ids, auth)
    return SuccessResponse(msg="启用成功")


@router.post("/batch/disable", summary="批量停用菜单", description="批量停用菜单")
async def batch_disable_dept(
        data: DeptBatchSetAvailable,
        auth: Auth = Depends(AuthPermission(permissions=["system:dept:update"])),
) -> JSONResponse:
    await DeptService.disable_dept(data.ids, auth)
    return SuccessResponse(msg="停用成功")
