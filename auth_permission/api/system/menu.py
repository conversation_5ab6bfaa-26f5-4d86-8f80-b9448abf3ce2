#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Depends, Query
from fastapi.responses import JSONResponse

from auth_permission.core.params import PaginationQueryParams, MenuQueryParams
from auth_permission.core.router_class import OperationLogRoute
from auth_permission.core.dependencies import AuthPermission
from auth_permission.services.system import MenuService
from auth_permission.utils.response import SuccessResponse, PaginationResponse
from auth_permission.schemas.system import (
    Auth,
    MenuCreate,
    MenuUpdate,
    MenuBatchSetAvailable
)


router = APIRouter(route_class=OperationLogRoute, prefix="/admin/api/system/menu", tags=["菜单模块"])


@router.get("/list", summary="查询菜单", description="查询菜单")
async def get_menu_list(
        auth: Auth = Depends(AuthPermission(permissions=["system:menu:query"], check_data_scope=False)),
        paging_query: PaginationQueryParams = Depends(),
        menu_query: MenuQueryParams = Depends(),
) -> JSONResponse:
    search = menu_query.__dict__
    data = await MenuService.get_menu_list(search, auth)
    return PaginationResponse(data, page=paging_query.page, page_size=paging_query.page_size)


@router.get("/detail", summary="查询菜单详情", description="查询菜单详情")
async def get_menu_detail(
        id: int = Query(..., description="菜单ID"),
        auth: Auth = Depends(AuthPermission(permissions=["system:menu:query"], check_data_scope=False)),
) -> JSONResponse:
    data = await MenuService.get_menu_detail(id, auth)
    return SuccessResponse(data)


@router.get("/options", summary="查询菜单选项", description="查询菜单选项")
async def get_menu_options(
        auth: Auth = Depends(AuthPermission(permissions=["system:menu:options"], check_data_scope=False)),
) -> JSONResponse:
    data = await MenuService.get_menu_options(auth)
    return SuccessResponse(data)


@router.post("/create", summary="创建菜单", description="创建菜单")
async def create_menu(
        menu_in: MenuCreate,
        auth: Auth = Depends(AuthPermission(permissions=["system:menu:create"], check_data_scope=False)),
) -> JSONResponse:
    data = await MenuService.create_menu(menu_in, auth)
    return SuccessResponse(data, msg="创建成功")


@router.post("/update", summary="修改菜单", description="修改菜单")
async def update_menu(
        menu_in: MenuUpdate,
        auth: Auth = Depends(AuthPermission(permissions=["system:menu:update"], check_data_scope=False)),
) -> JSONResponse:
    data = await MenuService.update_menu(menu_in, auth)
    return SuccessResponse(data, msg="修改成功")


@router.post("/delete", summary="删除菜单", description="删除菜单")
async def delete_menu(
        id: int = Query(..., description="菜单ID"),
        auth: Auth = Depends(AuthPermission(permissions=["system:menu:delete"], check_data_scope=False)),
) -> JSONResponse:
    await MenuService.delete_menu(id, auth)
    return SuccessResponse(msg="删除成功")


@router.post("/batch/enable", summary="批量启用菜单", description="批量启用菜单")
async def batch_enabled_menu(
        data: MenuBatchSetAvailable,
        auth: Auth = Depends(AuthPermission(permissions=["system:menu:update"], check_data_scope=False)),
) -> JSONResponse:
    await MenuService.enable_menu(data.ids, auth=auth)
    return SuccessResponse(msg="启用成功")


@router.post("/batch/disable", summary="批量停用菜单", description="批量停用菜单")
async def batch_disable_menu(
        data: MenuBatchSetAvailable,
        auth: Auth = Depends(AuthPermission(permissions=["system:menu:update"], check_data_scope=False)),
) -> JSONResponse:
    await MenuService.disable_menu(data.ids, auth=auth)
    return SuccessResponse(msg="停用成功")
