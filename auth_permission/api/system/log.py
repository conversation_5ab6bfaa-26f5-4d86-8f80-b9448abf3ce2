#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from auth_permission.core.router_class import OperationLogRoute
from auth_permission.core.params import PaginationQueryParams, LogQueryParams
from auth_permission.core.dependencies import AuthPermission
from auth_permission.services.system import LogService
from auth_permission.utils.response import PaginationResponse
from auth_permission.schemas.system import Auth


router = APIRouter(route_class=OperationLogRoute,prefix="/admin/api/system/log", tags=["操作日志"])


@router.get("/list", summary="查询操作日志", description="查询操作日志")
async def get_user_list(
        paging_query: PaginationQueryParams = Depends(),
        log_query: LogQueryParams = Depends(),
        auth: Auth = Depends(AuthPermission(permissions=["system:log:query"])),
) -> JSONResponse:
    search = log_query.__dict__
    data = await LogService.get_log_list(search, auth)
    return PaginationResponse(data, page=paging_query.page, page_size=paging_query.page_size)
