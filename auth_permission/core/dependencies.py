#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import As<PERSON><PERSON>enerator, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from auth_permission.core.database import session_connect
from fastapi import Request, Depends, status, Header
from aioredis import Redis
from auth_permission.core.security import OAuth2Schema, decode_jwt_token
from auth_permission.core.exceptions import CustomException
from auth_permission.services.system import UserService
from auth_permission.schemas.system import Auth
from app.v1.core.tokens import decode_token


async def session_getter() -> AsyncGenerator[AsyncSession, None]:
    async with session_connect() as session:
        async with session.begin():
            yield session


async def redis_getter(request: Request) -> Redis:
    return request.app.state.redis


async def get_current_user(
        request: Request,
        Authorization: str = Header(None),
        session: AsyncSession = Depends(session_getter)
) -> Auth:
    # token_payload = decode_jwt_token(token)
    # if token_payload.is_refresh:
    username = decode_token(Authorization)
    if not username:
        raise CustomException(
            msg="非法凭证",
            code=status.HTTP_403_FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN,
        )
    auth = Auth(session=session)
    user = await UserService.get_detail_by_username(username, auth)
    if not user.available:
        raise CustomException(
            msg="用户已被停用",
            code=status.HTTP_403_FORBIDDEN,
        )
    request.scope["user_id"] = user.id
    user.roles = list(filter(lambda item: item.available, user.roles))
    # user.positions = list(filter(lambda item: item.available, user.positions))
    auth.user = user
    return auth


async def get_user(
        request: Request,
        username: str = None,
        session: AsyncSession = Depends(session_getter)
) -> Auth:
    if not username:
        raise CustomException(
            msg="非法凭证",
            code=status.HTTP_403_FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN,
        )
    auth = Auth(session=session)
    user = await UserService.get_detail_by_username(username, auth)
    if not user.available:
        raise CustomException(
            msg="用户已被停用",
            code=status.HTTP_403_FORBIDDEN,
        )
    request.scope["user_id"] = user.id
    user.roles = list(filter(lambda item: item.available, user.roles))
    auth.user = user
    return auth


class AuthPermission:

    def __init__(self, permissions: Optional[list[str]] = None, check_data_scope: bool = True,) -> None:
        self.permissions = set(permissions) if permissions else None
        self.check_data_scope = check_data_scope

    async def __call__(
            self,
            request: Request,
            auth: Auth = Depends(get_current_user),
    ) -> Auth:

        auth.check_data_scope = self.check_data_scope

        is_superuser = auth.user.is_superuser
        if is_superuser:
            return auth

        if not self.permissions:
            return auth

        permissions = set()
        for role in auth.user.roles:
            for menu in role.menus:
                if not menu.permission:
                    continue
                permissions.add(menu.permission)
        # if len(self.permissions) != len(self.permissions & permissions):
        #
        #     raise CustomException(
        #         msg="无权限操作",
        #         code=status.HTTP_403_FORBIDDEN
        #     )

        return auth
