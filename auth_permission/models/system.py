# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/22 14:21
@Auth ： qu ming
@File ：system.py
@IDE ：PyCharm
"""
import json

from pydantic import field_validator

from auth_permission.models.base import Model, CustomMixin, TimestampMixin
from sqlalchemy.orm import relationship
from auth_permission.models.m2m import (
    RoleMenusModel,
    RoleDeptsModel,
    UserPositionsModel,
    UserRolesModel
)
from sqlalchemy import (
    Column,
    String,
    Integer,
    Boolean,
    DateTime,
    Text,
    BIGINT,
    ForeignKey, JSON
)


class MenuModel(TimestampMixin, Model):
    __tablename__ = "system_menu"
    __table_args__ = ({'comment': '权限表'})

    id = Column(BIGINT, primary_key=True, autoincrement=True, unique=True, comment='主键ID', nullable=False)
    name = Column(String(50), nullable=False, comment="菜单名称")
    type = Column(Integer, nullable=False, comment="菜单类型")
    icon = Column(String(50), nullable=False, default="", comment="图标")
    order = Column(Integer, nullable=False, default=1, comment="显示排序")
    permission = Column(String(50), nullable=False, default="", comment="权限标识")
    route_name = Column(String(50), nullable=True, comment="路由名称")
    route_path = Column(String(50), nullable=True, comment="路由路径")
    component_path = Column(String(50), nullable=True, comment="组件路径")
    redirect = Column(String(50), nullable=True, comment="重定向")
    available = Column(Boolean, nullable=False, default=True, comment="是否可用")
    cache = Column(Boolean, nullable=False, default=True, comment="是否缓存")
    hidden = Column(Boolean, nullable=False, default=False, comment="是否隐藏")
    parent_id = Column(
        BIGINT,
        ForeignKey("system_menu.id", ondelete="CASCADE", onupdate="CASCADE"),
        nullable=True, index=True, comment="父级菜单ID"
    )
    parent_name = Column(String(50), nullable=True, comment="父级菜单名称")
    description = Column(Text, nullable=True, comment="备注")

    parent = relationship(
        "MenuModel",
        cascade='all, delete-orphan',
        primaryjoin="MenuModel.parent_id == MenuModel.id",
        uselist=False
    )


class DeptModel(TimestampMixin, Model):
    __tablename__ = "system_dept"
    __table_args__ = ({'comment': '部门表'})

    id = Column(BIGINT, primary_key=True, autoincrement=True, unique=True, comment='主键ID', nullable=False)
    dept_id = Column(BIGINT, comment='部门id', nullable=False)
    name = Column(String(40), nullable=False, comment="部门名称")
    order = Column(Integer, nullable=False, default=1, comment="显示排序")
    available = Column(Boolean, nullable=False, default=True, comment="是否可用")
    parent_id = Column(
        BIGINT,
        ForeignKey("system_dept.dept_id", ondelete="CASCADE", onupdate="CASCADE"),
        nullable=True, index=True, comment="父级部门ID"
    )
    description = Column(Text, nullable=True, comment="备注")

    parent = relationship("DeptModel", cascade='all, delete-orphan', uselist=False)


class PositionModel(CustomMixin, Model):
    __tablename__ = "system_position"
    __table_args__ = ({'comment': '岗位表'})

    name = Column(String(40), nullable=False, comment="岗位名称")
    order = Column(Integer, nullable=False, default=1, comment="显示排序")
    available = Column(Boolean, default=True, nullable=False, comment="是否可用")

    users = relationship(
        "UserModel",
        secondary=UserPositionsModel.__tablename__,
        back_populates='positions',
        lazy="joined",
        uselist=True
    )


class RoleModel(CustomMixin, Model):
    __tablename__ = "system_role"
    __table_args__ = ({'comment': '角色表'})

    name = Column(String(40), nullable=False, comment="角色名称")
    code = Column(String(40), nullable=True, comment="角色编码")
    description = Column(String(500), nullable=False, comment="角色描述")
    order = Column(Integer, nullable=False, default=1, comment="显示排序")
    data_scope = Column(Integer, nullable=False, default=0, comment="数据权限")
    available = Column(Boolean, default=True, nullable=False, comment="是否可用")

    menus = relationship("MenuModel", secondary=RoleMenusModel.__tablename__, lazy="joined", uselist=True)
    depts = relationship("DeptModel", secondary=RoleDeptsModel.__tablename__, lazy="joined", uselist=True)


class UserModel(CustomMixin, Model):
    __tablename__ = "system_user"
    __table_args__ = ({'comment': '用户表'})
    id = Column(BIGINT, primary_key=True, autoincrement=True, unique=True, comment='主键ID', nullable=False)
    username = Column(String(150), nullable=False, comment="用户名")
    password = Column(String(128), nullable=False, comment="密码")
    name = Column(String(40), nullable=False, comment="姓名")
    mobile = Column(String(20), nullable=True, comment="手机号")
    email = Column(String(255), nullable=True, comment="邮箱")
    gender = Column(Integer, default=1, nullable=False, comment="性别")
    company = Column(String(255), nullable=True, comment="所属公司")
    account_type = Column(String(255), nullable=True, comment="公司账号")
    avatar = Column(String(255), nullable=True, comment="头像",
                    default="https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png")
    available = Column(Boolean, default=True, nullable=False, comment="是否可用")
    is_superuser = Column(Boolean, default=False, nullable=False, comment="是否超管")
    last_login = Column(DateTime, nullable=True, comment="最近登录时间")
    dept_id = Column(
        BIGINT,
        ForeignKey('system_dept.dept_id', ondelete="SET NULL", onupdate="CASCADE"),
        nullable=True, index=True, comment="部门ID"
    )

    dept = relationship('DeptModel', primaryjoin="UserModel.dept_id == DeptModel.dept_id", lazy="select", uselist=False)
    roles = relationship("RoleModel", secondary=UserRolesModel.__tablename__, lazy="joined", uselist=True)
    positions = relationship("PositionModel", secondary=UserPositionsModel.__tablename__, lazy="joined", uselist=True)


class OperationLogModel(CustomMixin, Model):
    __tablename__ = "system_operation_log"
    __table_args__ = ({'comment': '操作日志表'})

    request_path = Column(String(255), nullable=True, comment="请求路径")
    request_method = Column(String(10), nullable=True, comment="请求方式")
    request_payload = Column(Text, nullable=True, comment="请求体")
    request_ip = Column(String(50), nullable=True, comment="请求IP地址")
    request_os = Column(String(64), nullable=True, comment="操作系统")
    request_browser = Column(String(64), nullable=True, comment="浏览器")
    response_code = Column(Integer, nullable=True, comment="响应状态码")
    response_json = Column(Text, nullable=True, comment="响应体")


class AssistantModel(TimestampMixin, Model):
    __tablename__ = "assistant"
    __table_args__ = ({'comment': '助手表'})

    id = Column(BIGINT, primary_key=True, autoincrement=True, unique=True, comment='主键ID', nullable=False)
    create_user = Column(String(40), nullable=False, comment="创建人")
    assistant_name = Column(String(300), nullable=False, comment="助手名称")
    prompt_content = Column(Text, nullable=True, comment="助手内容")
    order = Column(Integer, nullable=False, default=1, comment="显示排序")
    description = Column(Text, nullable=True, comment="备注")
    project_id = Column(JSON, nullable=True, comment="项目id（JSON列表格式）")


    @field_validator('project_id', mode='before')
    def convert_to_json(cls, v):
        if isinstance(v, list):
            return json.dumps(v, ensure_ascii=False)
        return v

class PromptModel(TimestampMixin, Model):
    __tablename__ = "prompt"
    __table_args__ = ({'comment': '提示词表'})

    id = Column(BIGINT, primary_key=True, autoincrement=True, unique=True, comment='主键ID', nullable=False)
    create_user = Column(String(40), nullable=False, comment="创建人")
    prompt_name = Column(String(300), nullable=False, comment="提示词标题")
    prompt_content = Column(Text, nullable=True, comment="提示词内容")
    order = Column(Integer, nullable=False, default=1, comment="显示排序")
    description = Column(JSON, nullable=True, comment="备注（JSON列表格式）")

    @field_validator('description', mode='before')
    def convert_to_json(cls, v):
        if isinstance(v, list):
            return json.dumps(v, ensure_ascii=False)
        return v
