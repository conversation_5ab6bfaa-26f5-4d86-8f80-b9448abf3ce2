# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/10 18:05
@Auth ： qu ming
@File ：assistant.py
@IDE ：PyCharm
"""
from fastapi import APIRouter, Depends, Query
from fastapi.responses import JSONResponse

from ai_assistant.services.assistant_service import AssistantService
from auth_permission.core.router_class import OperationLogRoute
from auth_permission.core.dependencies import AuthPermission
from auth_permission.utils.response import SuccessResponse
from ai_assistant.schemas import (
    Auth,
    AssistantCreate,
    AssistantUpdate,
)
from app.v1.db.user_project import update_project

router = APIRouter(route_class=OperationLogRoute, prefix="/api/Assistants", tags=["助手模块"])


@router.get("/list", summary="查询助手", description="查询助手")
async def get_assistant_list(
        auth: Auth = Depends(AuthPermission(permissions=["assistant:query"])),
) -> JSONResponse:
    data = await AssistantService.get_assistant_list(auth)
    return SuccessResponse(data)


@router.get("/detail", summary="查询助手详情", description="查询助手详情")
async def get_assistant_detail(
        id: int = Query(..., description="助手ID"),
        auth: Auth = Depends(AuthPermission(permissions=["assistant:query"])),
) -> JSONResponse:
    data = await AssistantService.get_assistant_detail(id, auth)
    return SuccessResponse(data)


@router.post("/create", summary="创建助手", description="创建助手")
async def create_assistant(
        assistant_in: AssistantCreate,
        auth: Auth = Depends(AuthPermission(permissions=["assistant:create"])),
) -> JSONResponse:
    assistant_in = assistant_in.model_copy(update={"create_user": auth.user.username})
    data = await AssistantService.create_assistant(assistant_in, auth)
    update_project(assistant_in.project_id, str(data['id']))
    return SuccessResponse(data, msg="创建成功")


@router.post("/update", summary="修改助手", description="修改助手")
async def update_assistant(
        assistant_in: AssistantUpdate,
        auth: Auth = Depends(AuthPermission(permissions=["assistant:update"])),
) -> JSONResponse:
    data = await AssistantService.update_assistant(assistant_in, auth)
    update_project(assistant_in.project_id, str(assistant_in.id))
    return SuccessResponse(data, msg="修改成功")


@router.post("/delete", summary="删除助手", description="删除助手")
async def delete_assistant(
        id: int = Query(..., description="助手ID"),
        auth: Auth = Depends(AuthPermission(permissions=["assistant:delete"])),
) -> JSONResponse:
    await AssistantService.delete_assistant(id, auth)
    return SuccessResponse(msg="删除成功")
