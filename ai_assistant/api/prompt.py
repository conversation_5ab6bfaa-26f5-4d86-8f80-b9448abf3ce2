# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/4 16:09
@Auth ： qu ming
@File ：prompts.py
@IDE ：PyCharm
"""

from fastapi import APIRouter, Depends, Query
from fastapi.responses import JSONResponse

from ai_assistant.services.prompts_service import PromptService
from auth_permission.core.params import PaginationQueryParams, PromptQueryParams
from auth_permission.core.router_class import Operation<PERSON>og<PERSON>oute
from auth_permission.core.dependencies import AuthPermission
from auth_permission.utils.response import SuccessResponse, PaginationResponse
from ai_assistant.schemas import (
    Auth,
    PromptCreate,
    PromptUpdate,
)


router = APIRouter(route_class=OperationLogRoute, prefix="/admin/api/prompts", tags=["提示词模块"])


@router.get("/list", summary="查询提示词", description="查询提示词")
async def get_prompt_list(
        auth: Auth = Depends(AuthPermission(permissions=["prompts:query"])),
        paging_query: PaginationQueryParams = Depends(),
        prompt_query: PromptQueryParams = Depends()
) -> JSONResponse:
    search = prompt_query.__dict__
    data = await PromptService.get_prompt_list(search,auth)
    return PaginationResponse(data, page=paging_query.page, page_size=paging_query.page_size)


@router.get("/detail", summary="查询提示词详情", description="查询提示词详情")
async def get_prompt_detail(
        id: int = Query(..., description="提示词ID"),
        auth: Auth = Depends(AuthPermission(permissions=["prompts:query"])),
) -> JSONResponse:
    data = await PromptService.get_prompt_detail(id, auth)
    return SuccessResponse(data)


@router.post("/create", summary="创建提示词", description="创建提示词")
async def create_dept(
        prompt_in: PromptCreate,
        auth: Auth = Depends(AuthPermission(permissions=["prompts:create"])),
) -> JSONResponse:
    prompt_in = prompt_in.model_copy(update={"create_user": auth.user.username})
    data = await PromptService.create_prompt(prompt_in, auth)
    return SuccessResponse(data, msg="创建成功")


@router.post("/update", summary="修改提示词", description="修改提示词")
async def update_dept(
        prompt_in: PromptUpdate,
        auth: Auth = Depends(AuthPermission(permissions=["prompts:update"])),
) -> JSONResponse:
    data = await PromptService.update_prompt(prompt_in, auth)
    return SuccessResponse(data, msg="修改成功")


@router.post("/delete", summary="删除提示词", description="删除提示词")
async def delete_dept(
        id: int = Query(..., description="提示词ID"),
        auth: Auth = Depends(AuthPermission(permissions=["prompts:delete"])),
) -> JSONResponse:
    await PromptService.delete_prompt(id, auth)
    return SuccessResponse(msg="删除成功")


