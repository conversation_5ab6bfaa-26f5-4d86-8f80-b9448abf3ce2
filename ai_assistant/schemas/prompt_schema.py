# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/10 10:25
@Auth ： qu ming
@File ：Prompt_schema.py
@IDE ：PyCharm
"""

from typing import Optional, List
from pydantic import BaseModel, ConfigDict
from auth_permission.schemas.base import TimestampOutSchema


class Prompt(BaseModel):
    create_user: Optional[str] = None
    prompt_name: Optional[str] = None
    prompt_content: Optional[str] = None
    description: Optional[List[str]] = None


class PromptCreate(Prompt):
    ...


class PromptUpdate(Prompt):
    id: int


class PromptOut(Prompt):
    model_config = ConfigDict(from_attributes=True)
    id: int


class PromptSimpleOut(Prompt, TimestampOutSchema):
    ...


class PromptOptionsOut(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    prompt_name: str
    prompt_content: str
    create_user: str
    description:str

