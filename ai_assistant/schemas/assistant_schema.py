# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/10 18:07
@Auth ： qu ming
@File ：assistant_schema.py
@IDE ：PyCharm
"""

from typing import Optional, List
from pydantic import BaseModel, ConfigDict
from auth_permission.schemas.base import TimestampOutSchema


class Assistant(BaseModel):
    create_user: Optional[str] = None
    assistant_name: Optional[str] = None
    prompt_content: Optional[str] = None
    description: Optional[str] = None
    project_id: Optional[List[str]] = None


class AssistantCreate(Assistant):
    ...


class AssistantUpdate(Assistant):
    id: int


class AssistantOut(Assistant):
    model_config = ConfigDict(from_attributes=True)
    id: int


class AssistantSimpleOut(Assistant, TimestampOutSchema):
    ...


class AssistantOptionsOut(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    assistant_name: str
    assistant_content: str
    create_user: str
    description: str

