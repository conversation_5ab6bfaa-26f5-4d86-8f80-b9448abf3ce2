# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/9 19:10
@Auth ： qu ming
@File ：prompts_service.py
@IDE ：PyCharm
"""

from typing import List, Dict

from ai_assistant.crud.prompt_crud import PromptCRUD
from ai_assistant.schemas import (
    Auth,
    PromptCreate,
    PromptUpdate,
    PromptOut,
    PromptSimpleOut,
)


class PromptService:
    """
    提示词服务层
    """

    @classmethod
    async def get_prompt_detail(cls, id: int, auth: Auth) -> Dict:
        obj = await PromptCRUD(auth).get_by_id(id)
        return PromptSimpleOut.model_validate(obj).model_dump()

    @classmethod
    async def get_prompt_list(cls, search, auth: Auth) -> List[Dict]:
        data = await PromptCRUD(auth).get_prompt_list(search, order=["order"])
        data = [PromptOut.model_validate(obj).model_dump() for obj in data]
        return data

    @classmethod
    async def create_prompt(cls, prompt_in: PromptCreate, auth: Auth) -> Dict:
        new_dept = await PromptCRUD(auth).create(obj_in=prompt_in)
        return PromptOut.model_validate(new_dept).model_dump()

    @classmethod
    async def update_prompt(cls, prompt_in: PromptUpdate, auth: Auth) -> Dict:
        new_dept = await PromptCRUD(auth).update(id=prompt_in.id, obj_in=prompt_in)

        return PromptOut.model_validate(new_dept).model_dump()

    @classmethod
    async def delete_prompt(cls, id: int, auth: Auth) -> None:
        await PromptCRUD(auth).delete(ids=[id])


