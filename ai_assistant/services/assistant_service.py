# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/10 18:07
@Auth ： qu ming
@File ：assistant_service.py.py
@IDE ：PyCharm
"""

from typing import List, Dict

from ai_assistant.crud.assistant_crud import AssistantCRUD
from ai_assistant.schemas import (
    Auth,
    AssistantCreate,
    AssistantUpdate,
    AssistantOut,
    AssistantSimpleOut,
)


class AssistantService:
    """
    提示词服务层
    """

    @classmethod
    async def get_assistant_detail(cls, id: int, auth: Auth) -> Dict:
        obj = await AssistantCRUD(auth).get_by_id(id)
        return AssistantSimpleOut.model_validate(obj).model_dump()

    @classmethod
    async def get_assistant_list(cls, auth: Auth) -> List[Dict]:
        data = await AssistantCRUD(auth).get_assistant_list(order=["order"])
        data = [AssistantOut.model_validate(obj).model_dump() for obj in data if getattr(obj, 'create_user', None) == auth.user.username or getattr(obj, 'id', None) == 1]
        return data

    @classmethod
    async def create_assistant(cls, assistant_in: AssistantCreate, auth: Auth) -> Dict:
        new_dept = await AssistantCRUD(auth).create(obj_in=assistant_in)
        return AssistantOut.model_validate(new_dept).model_dump()

    @classmethod
    async def update_assistant(cls, assistant_in: AssistantUpdate, auth: Auth) -> Dict:
        new_dept = await AssistantCRUD(auth).update(id=assistant_in.id, obj_in=assistant_in)

        return AssistantOut.model_validate(new_dept).model_dump()

    @classmethod
    async def delete_assistant(cls, id: int, auth: Auth) -> None:
        await AssistantCRUD(auth).delete(ids=[id])
