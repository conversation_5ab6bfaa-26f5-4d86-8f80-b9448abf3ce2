# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/10 9:49
@Auth ： qu ming
@File ：prompt_crud.py
@IDE ：PyCharm
"""

from typing import Dict, List, Sequence

from ai_assistant.schemas.prompt_schema import PromptCreate,PromptUpdate
from auth_permission.crud.base import CRUDBase
from auth_permission.models.system import PromptModel
from auth_permission.schemas.system import Auth
from auth_permission.utils.tools import dict_to_search_sql


class PromptCRUD(CRUDBase[PromptModel, PromptCreate, PromptUpdate]):
    """
    提示词模块数据查询层
    """
    def __init__(self, auth: Auth) -> None:
        super().__init__(model=PromptModel, auth=auth)

    async def get_by_id(self, id: int) -> PromptModel:
        obj = await self.get(id=id)
        return obj

    async def get_prompt_list(self, search: Dict = None, order: List[str] = None) -> Sequence[PromptModel]:
        sql_where = dict_to_search_sql(self.model, search) if search else []
        return await self.list(search=sql_where, order=order)
