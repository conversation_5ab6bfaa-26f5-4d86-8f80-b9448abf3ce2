# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/10 18:06
@Auth ： qu ming
@File ：assistant_crud.py
@IDE ：PyCharm
"""

from typing import Dict, List, Sequence

from ai_assistant.schemas.assistant_schema import Assistant<PERSON>reate,AssistantUpdate
from auth_permission.crud.base import CRUDBase
from auth_permission.models.system import AssistantModel
from auth_permission.schemas.system import Auth
from auth_permission.utils.tools import dict_to_search_sql


class AssistantCRUD(CRUDBase[AssistantModel, AssistantCreate, AssistantUpdate]):
    """
    助手模块数据查询层
    """
    def __init__(self, auth: Auth) -> None:
        super().__init__(model=AssistantModel, auth=auth)

    async def get_by_id(self, id: int) -> AssistantModel:
        obj = await self.get(id=id)
        return obj

    async def get_assistant_list(self, search: Dict = None, order: List[str] = None) -> Sequence[AssistantModel]:
        sql_where = dict_to_search_sql(self.model, search) if search else []
        return await self.list(search=sql_where, order=order)
